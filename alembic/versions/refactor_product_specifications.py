"""重构产品规格系统

Revision ID: refactor_product_specs
Revises: 
Create Date: 2025-01-26 10:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'refactor_product_specs'
down_revision = None  # 需要根据实际情况设置
branch_labels = None
depends_on = None


def upgrade():
    """升级数据库结构"""
    
    # 1. 为specs表添加新字段
    op.add_column('specs', sa.Column('description', sa.String(500), nullable=True, comment='规格描述'))
    op.add_column('specs', sa.Column('sort_order', sa.Integer(), default=0, comment='排序顺序'))
    op.add_column('specs', sa.Column('is_active', sa.<PERSON>olean(), default=True, comment='是否启用'))
    
    # 2. 为spec_options表添加新字段
    op.add_column('spec_options', sa.Column('color_code', sa.String(7), nullable=True, comment='颜色代码'))
    op.add_column('spec_options', sa.Column('image_url', sa.String(500), nullable=True, comment='规格值图片URL'))
    op.add_column('spec_options', sa.Column('sort_order', sa.Integer(), default=0, comment='排序顺序'))
    op.add_column('spec_options', sa.Column('is_active', sa.Boolean(), default=True, comment='是否启用'))
    
    # 3. 创建新的combination_spec_options关联表
    op.create_table(
        'combination_spec_options',
        sa.Column('combination_id', sa.BigInteger(), nullable=False),
        sa.Column('spec_option_id', sa.BigInteger(), nullable=False),
        sa.ForeignKeyConstraint(['combination_id'], ['product_spec_combinations.id'], ),
        sa.ForeignKeyConstraint(['spec_option_id'], ['spec_options.id'], ),
        sa.PrimaryKeyConstraint('combination_id', 'spec_option_id')
    )
    
    # 4. 为product_spec_combinations表添加新字段
    op.add_column('product_spec_combinations', sa.Column('cost_price', sa.Integer(), nullable=True, comment='成本价(分)'))
    op.add_column('product_spec_combinations', sa.Column('market_price', sa.Integer(), nullable=True, comment='市场价(分)'))
    op.add_column('product_spec_combinations', sa.Column('min_stock_level', sa.Integer(), default=0, comment='最小库存水平'))
    op.add_column('product_spec_combinations', sa.Column('max_stock_level', sa.Integer(), nullable=True, comment='最大库存水平'))
    op.add_column('product_spec_combinations', sa.Column('is_active', sa.Boolean(), default=True, comment='是否启用'))
    op.add_column('product_spec_combinations', sa.Column('is_default', sa.Boolean(), default=False, comment='是否为默认组合'))
    op.add_column('product_spec_combinations', sa.Column('weight', sa.Float(), nullable=True, comment='重量(kg)'))
    op.add_column('product_spec_combinations', sa.Column('barcode', sa.String(100), nullable=True, comment='条码'))
    op.add_column('product_spec_combinations', sa.Column('description', sa.Text(), nullable=True, comment='组合描述'))
    
    # 5. 修改price字段类型为Integer(分)
    op.alter_column('product_spec_combinations', 'price',
                    existing_type=sa.Float(),
                    type_=sa.Integer(),
                    existing_nullable=True,
                    comment='价格(分)')
    
    # 6. 添加索引
    op.create_index('idx_specs_active', 'specs', ['is_active'])
    op.create_index('idx_specs_sort_order', 'specs', ['sort_order'])
    op.create_index('idx_spec_options_spec_active', 'spec_options', ['spec_id', 'is_active'])
    op.create_index('idx_spec_options_sort_order', 'spec_options', ['sort_order'])
    op.create_index('idx_combinations_product_active', 'product_spec_combinations', ['product_id', 'is_active'])
    op.create_index('idx_combinations_barcode', 'product_spec_combinations', ['barcode'])
    op.create_index('idx_combination_spec_options_combination', 'combination_spec_options', ['combination_id'])
    op.create_index('idx_combination_spec_options_spec_option', 'combination_spec_options', ['spec_option_id'])
    
    # 7. 数据迁移：将spec_option_ids JSON数据迁移到关联表
    # 注意：这需要根据实际数据情况编写迁移逻辑
    migrate_spec_option_ids_to_association_table()
    
    # 8. 删除冗余表（在确认数据迁移完成后）
    # op.drop_table('product_spec_options')
    # op.drop_table('product_specs')
    
    # 9. 删除spec_option_ids字段（在确认关联表数据正确后）
    # op.drop_column('product_spec_combinations', 'spec_option_ids')


def migrate_spec_option_ids_to_association_table():
    """将JSON存储的spec_option_ids迁移到关联表"""
    connection = op.get_bind()
    
    # 获取所有组合的spec_option_ids数据
    result = connection.execute(
        sa.text("SELECT id, spec_option_ids FROM product_spec_combinations WHERE spec_option_ids IS NOT NULL")
    )
    
    for row in result:
        combination_id = row[0]
        spec_option_ids = row[1]
        
        if spec_option_ids and isinstance(spec_option_ids, list):
            # 为每个spec_option_id创建关联记录
            for spec_option_id in spec_option_ids:
                try:
                    connection.execute(
                        sa.text("""
                            INSERT INTO combination_spec_options (combination_id, spec_option_id) 
                            VALUES (:combination_id, :spec_option_id)
                            ON CONFLICT DO NOTHING
                        """),
                        combination_id=combination_id,
                        spec_option_id=spec_option_id
                    )
                except Exception as e:
                    print(f"Error migrating combination {combination_id}, option {spec_option_id}: {e}")


def downgrade():
    """降级数据库结构"""
    
    # 1. 恢复spec_option_ids字段
    op.add_column('product_spec_combinations', sa.Column('spec_option_ids', postgresql.JSON(), nullable=True))
    
    # 2. 将关联表数据迁移回JSON字段
    restore_spec_option_ids_from_association_table()
    
    # 3. 删除新增的索引
    op.drop_index('idx_combination_spec_options_spec_option')
    op.drop_index('idx_combination_spec_options_combination')
    op.drop_index('idx_combinations_barcode')
    op.drop_index('idx_combinations_product_active')
    op.drop_index('idx_spec_options_sort_order')
    op.drop_index('idx_spec_options_spec_active')
    op.drop_index('idx_specs_sort_order')
    op.drop_index('idx_specs_active')
    
    # 4. 删除关联表
    op.drop_table('combination_spec_options')
    
    # 5. 恢复price字段类型
    op.alter_column('product_spec_combinations', 'price',
                    existing_type=sa.Integer(),
                    type_=sa.Float(),
                    existing_nullable=True,
                    comment='价格')
    
    # 6. 删除新增字段
    op.drop_column('product_spec_combinations', 'description')
    op.drop_column('product_spec_combinations', 'barcode')
    op.drop_column('product_spec_combinations', 'weight')
    op.drop_column('product_spec_combinations', 'is_default')
    op.drop_column('product_spec_combinations', 'is_active')
    op.drop_column('product_spec_combinations', 'max_stock_level')
    op.drop_column('product_spec_combinations', 'min_stock_level')
    op.drop_column('product_spec_combinations', 'market_price')
    op.drop_column('product_spec_combinations', 'cost_price')
    
    op.drop_column('spec_options', 'is_active')
    op.drop_column('spec_options', 'sort_order')
    op.drop_column('spec_options', 'image_url')
    op.drop_column('spec_options', 'color_code')
    
    op.drop_column('specs', 'is_active')
    op.drop_column('specs', 'sort_order')
    op.drop_column('specs', 'description')


def restore_spec_option_ids_from_association_table():
    """从关联表恢复JSON格式的spec_option_ids"""
    connection = op.get_bind()
    
    # 获取所有组合的关联数据
    result = connection.execute(
        sa.text("""
            SELECT combination_id, array_agg(spec_option_id) as spec_option_ids
            FROM combination_spec_options 
            GROUP BY combination_id
        """)
    )
    
    for row in result:
        combination_id = row[0]
        spec_option_ids = list(row[1]) if row[1] else []
        
        connection.execute(
            sa.text("""
                UPDATE product_spec_combinations 
                SET spec_option_ids = :spec_option_ids 
                WHERE id = :combination_id
            """),
            combination_id=combination_id,
            spec_option_ids=spec_option_ids
        )
