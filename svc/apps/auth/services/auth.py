"""
认证服务模块，提供面向对象形式的用户认证和令牌管理功能。
使用Result模式处理错误，采用面向对象编程风格。
"""
from datetime import datetime, timedelta, timezone
from typing import Any, Dict, Optional

from fastapi_events.dispatcher import dispatch
from redis.asyncio import Redis

from svc.apps.auth.models.user import User
from svc.apps.auth.repositories import RoleRepository, UserRepository
from svc.apps.auth.schemas import (AuthCredentials, LoginParams,
                                   PasswordResetParams, PasswordResetRequest,
                                   TokenData)
from svc.core.config.settings import get_settings
from svc.core.events.event_names import (AUTH_PASSWORD_RESET_COMPLETED,
                                         AUTH_PASSWORD_RESET_REQUESTED,
                                         AUTH_USER_AUTHENTICATION_FAILED,
                                         AUTH_USER_AUTHENTICATION_SUCCESS,
                                         AUTH_USER_LOGGED_IN,
                                         AUTH_USER_LOGGED_OUT)
from svc.core.exceptions.error_codes import ErrorCode
from svc.core.models.result import Result
from svc.core.security.password import verify_password
from svc.core.security.token import TokenService
from svc.core.services.base import BaseService
from svc.core.services.mixins.batch_operation import BatchOperationMixin
from svc.core.services.mixins.cache import CacheMixin
from svc.core.services.mixins.error_result import ErrorResultMixin

# 缓存过期时间（秒）
CACHE_TTL = 3600  # 1小时

# 获取应用设置
settings = get_settings()

class AuthService(BaseService, ErrorResultMixin, BatchOperationMixin, CacheMixin):
    """认证服务类，提供用户认证和令牌管理功能
    
    该服务类负责：
    1. 处理用户认证与登录
    2. 生成和验证JWT令牌
    3. 处理密码重置流程
    4. 触发认证相关事件
    5. 封装认证结果返回
    """
    
    # 设置资源类型名称
    resource_type = "auth"
    
    def __init__(
        self, 
        redis: Optional[Redis] = None, 
        token_service: TokenService = None,
        token_expire_minutes: int = None,
        user_repo: Optional[UserRepository] = None,
        role_repo: Optional[RoleRepository] = None
    ):
        """初始化认证服务
        
        Args:
            db: 数据库会话
            redis: Redis客户端，用于缓存和分布式锁
            token_service: 令牌服务，用于生成和验证令牌
            token_expire_minutes: 令牌过期时间（分钟）
            user_repo: 用户仓库实例，不提供则创建新实例
            role_repo: 角色仓库实例，不提供则创建新实例
        """
        BaseService.__init__(self, redis)
        self.settings = get_settings()
        self.token_service = token_service
        self.token_expire_minutes = token_expire_minutes
        self.user_repo = user_repo or UserRepository()
        self.role_repo = role_repo or RoleRepository()
    
    async def get_resource_by_id(self, user_id: int) -> Optional[User]:
        """获取指定ID的用户资源
        
        Args:
            user_id: 用户ID
            
        Returns:
            Optional[User]: 用户对象，不存在时返回None
        """
        return await self.user_repo.get_by_id( user_id)
    
    def _create_token_data(self, user_id: int, expires_minutes: int = None) -> TokenData:
        """创建令牌数据（私有辅助方法）
        
        Args:
            user_id: 用户ID
            expires_minutes: 过期时间（分钟）
            
        Returns:
            令牌数据对象，格式符合OAuth2标准
        """
        expires = expires_minutes or self.settings.access_token_expire_minutes
        expires_delta = timedelta(minutes=expires)
        
        # 使用TokenService创建令牌
        access_token = self.token_service.create_access_token(
            user_id=user_id,
            extra_data={}
        )
        
        # 创建可选的刷新令牌
        refresh_token = self.token_service.create_refresh_token(user_id)
        
        return TokenData(
            access_token=access_token,
            token_type="bearer",  # OAuth2标准使用小写"bearer"
            expires_in=expires * 60,  # OAuth2标准使用秒为单位
            refresh_token=refresh_token,
            user_id=user_id
        )
    
    async def authenticate(self, credentials: AuthCredentials) -> Result[Dict[str, Any]]:
        """验证用户凭据
        
        Args:
            credentials: 认证凭据
        
        Returns:
            Result[Dict[str, Any]]: 认证结果对象
        """
        try:
            self.logger.info(f"尝试验证用户凭据: email={credentials.email}")
            user = None
            if self.redis:
                try:
                    email_key = f"{self.resource_type}:email:{credentials.email}"
                    cached_user_id = await self.redis.get(email_key)
                    if cached_user_id:
                        user = await self.get_resource_by_id(int(cached_user_id))
                        if user:
                            self.logger.debug(f"从缓存获取到用户: email={credentials.email}, user_id={user.id}")
                        else:
                            self.logger.warning(f"缓存中的用户ID无效: email={credentials.email}, user_id={cached_user_id}")
                            await self.redis.delete(email_key)
                    if not user:
                        user = await self.user_repo.get_by_email(email=credentials.email)
                except Exception as e:
                    self.logger.warning(f"从缓存获取用户失败: email={credentials.email}, 错误={str(e)}")
                    user = await self.user_repo.get_by_email(email=credentials.email)
            else:
                user = await self.user_repo.get_by_email(email=credentials.email)

            if not user:
                self.logger.warning(f"验证失败: 用户不存在, email={credentials.email}")
                return self.create_error_result(
                    error_code=ErrorCode.USER_NOT_FOUND,
                    error_message="用户不存在"
                )

            if not verify_password(credentials.password, user.hashed_password):
                # 增加失败计数
                if hasattr(self.user_repo, 'increment_login_fail'):
                    await self.user_repo.increment_login_fail(user.id)
                # 检查是否锁定
                if hasattr(user, 'login_fail_count') and user.login_fail_count is not None and user.login_fail_count + 1 >= 5:
                    if hasattr(self.user_repo, 'lock_user'):
                        await self.user_repo.lock_user(user.id)
                    self.logger.warning(f"用户被自动锁定: email={user.email}, user_id={user.id}")
                    return self.create_error_result(
                        error_code=ErrorCode.ACCOUNT_LOCKED,
                        error_message="账号已被锁定"
                    )
                self.logger.warning(f"验证失败: 密码错误, email={credentials.email}, user_id={user.id}")
                dispatch(AUTH_USER_AUTHENTICATION_FAILED, payload={
                    "user_id": user.id,
                    "email": user.email,
                    "ip_address": getattr(credentials, 'ip_address', None),
                    "user_agent": getattr(credentials, 'user_agent', None),
                    "timestamp": datetime.utcnow().isoformat()
                })
                return self.create_error_result(
                    error_code=ErrorCode.INVALID_PASSWORD,
                    error_message="密码错误"
                )

            if not user.is_active:
                self.logger.warning(f"验证失败: 用户未激活, email={credentials.email}, user_id={user.id}")
                return self.create_error_result(
                    error_code=ErrorCode.USER_INACTIVE,
                    error_message="用户未激活"
                )

            if getattr(user, 'is_locked', False):
                self.logger.warning(f"验证失败: 用户被锁定, email={credentials.email}, user_id={user.id}")
                return self.create_error_result(
                    error_code=ErrorCode.ACCOUNT_LOCKED,
                    error_message="账号已被锁定"
                )

            self.logger.info(f"用户验证成功: email={credentials.email}, user_id={user.id}")
            dispatch(AUTH_USER_AUTHENTICATION_SUCCESS, payload={"user_id": user.id, "email": user.email})
            return self.create_success_result({
                "user_id": user.id,
                "is_active": user.is_active
            })
        except Exception as e:
            self.logger.error(f"验证用户凭据异常: email={credentials.email}, 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.SYSTEM_ERROR,
                error_message="认证服务异常"
            )
    
    async def login(self, params: LoginParams) -> Result[Dict[str, Any]]:
        """处理用户登录
        
        Args:
            params: 登录参数
            
        Returns:
            Result[Dict[str, Any]]: 登录结果对象，成功时包含符合OAuth2标准的令牌
        """
        try:
            self.logger.info(f"用户尝试登录: email={params.email}")
            
            auth_result = await self.authenticate(
                AuthCredentials(email=params.email, password=params.password)
            )
            
            if not auth_result.is_success:
                self.logger.warning(f"用户登录失败: email={params.email}, 错误={auth_result.result_code}")
                return auth_result
            
            # 获取用户信息
            user = await self.get_resource_by_id(auth_result.data["user_id"])
            
            # 创建符合OAuth2标准的token
            token_data = self._create_token_data(user.id)
            
            try:
                # 发送事件
                dispatch(AUTH_USER_LOGGED_IN, payload={
                    "user_id": user.id,
                    "ip_address": params.ip_address,
                    "user_agent": params.user_agent if hasattr(params, 'user_agent') else ""
                })
                
                self.logger.info(f"用户登录成功: email={params.email}, user_id={user.id}")
                
            except Exception as e:
                # 记录错误但不阻止主流程
                self.logger.error(f"登录后处理失败: user_id={user.id}, 错误={str(e)}", exc_info=True)
            
            return self.create_success_result({
                "token_data": token_data,
                "user_id": user.id
            })
        except Exception as e:
            self.logger.error(f"登录失败: email={params.email}, 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.LOGIN_FAILED,
                error_message="登录失败，请稍后再试"
            )
    
    async def validate_token(self, token: str) -> Result[Dict[str, Any]]:
        """验证令牌有效性
        
        Args:
            token: JWT令牌
            
        Returns:
            Result[Dict[str, Any]]: 认证结果对象
        """
        try:
            self.logger.debug(f"验证令牌: token={token[:10]}...")
            
            # 先从缓存验证
            if self.redis:
                try:
                    token_key = f"token:{token}"
                    user_id = await self.redis.get(token_key)
                    if user_id:
                        user_id = int(user_id)
                        user = await self.get_resource_by_id(user_id)
                        if user and user.is_active:
                            self.logger.debug(f"从缓存验证令牌成功: user_id={user_id}")
                            return self.create_success_result({"user_id": user_id})
                        else:
                            # 删除无效缓存
                            await self.redis.delete(token_key)
                except Exception as e:
                    self.logger.warning(f"从缓存验证令牌失败: 错误={str(e)}")
            
            # 使用TokenService验证令牌
            payload = self.token_service.verify_access_token(token)
            if not payload:
                self.logger.warning("令牌验证失败: 无效令牌")
                return self.create_error_result(
                    error_code=ErrorCode.TOKEN_INVALID,
                    error_message="无效的令牌"
                )
                
            # 获取用户ID
            user_id = int(payload.get("sub"))
            if not user_id:
                self.logger.warning("令牌验证失败: 缺少用户ID")
                return self.create_error_result(
                    error_code=ErrorCode.TOKEN_INVALID,
                    error_message="无效的令牌"
                )
            
            # 检查用户是否存在并激活
            user = await self.get_resource_by_id(user_id)
            if not user:
                self.logger.warning(f"令牌验证失败: 用户 {user_id} 不存在")
                return self.create_error_result(
                    error_code=ErrorCode.USER_NOT_FOUND,
                    error_message="用户不存在"
                )
                
            if not user.is_active:
                self.logger.warning(f"令牌验证失败: 用户 {user_id} 未激活")
                return self.create_error_result(
                    error_code=ErrorCode.USER_INACTIVE,
                    error_message="用户未激活"
                )
            
            # 缓存有效令牌
            if self.redis:
                try:
                    # 计算剩余过期时间
                    exp_time = payload.get("exp", 0)
                    current_time = int(datetime.now(timezone.utc).timestamp())
                    ttl = max(0, exp_time - current_time)
                    
                    if ttl > 0:
                        token_key = f"token:{token}"
                        await self.redis.set(token_key, user_id, ex=ttl)
                        self.logger.debug(f"缓存验证令牌: user_id={user_id}, ttl={ttl}秒")
                except Exception as e:
                    self.logger.warning(f"缓存令牌失败: 错误={str(e)}")
            
            self.logger.debug(f"令牌验证成功: user_id={user_id}")
            
            return self.create_success_result({"user_id": user_id})
        except Exception as e:
            self.logger.error(f"验证令牌时发生错误: {str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.TOKEN_INVALID,
                error_message="令牌验证失败"
            )
    
    async def request_password_reset(self, request: PasswordResetRequest) -> Result[Dict[str, Any]]:
        """请求密码重置
        
        Args:
            request: 密码重置请求
            
        Returns:
            Result[Dict[str, Any]]: 认证结果对象
        """
        try:
            self.logger.info(f"密码重置请求: email={request.email}")
            
            # 检查用户是否存在
            user = await self.user_repo.get_by_email( email=request.email)
            if not user:
                self.logger.warning(f"密码重置请求失败: 用户 {request.email} 不存在")
                # 为安全考虑，不返回具体错误
                return self.create_success_result({
                    "message": "如果该邮箱存在，重置链接将发送至该邮箱"
                })
                
            if not user.is_active:
                self.logger.warning(f"密码重置请求失败: 用户 {request.email} 未激活")
                return self.create_success_result({
                    "message": "如果该邮箱存在，重置链接将发送至该邮箱"
                })
            
            # 生成重置令牌
            reset_token = self.token_service.create_password_reset_token(user.id)
            
            # 保存重置令牌到缓存
            if self.redis:
                try:
                    reset_key = f"password_reset:{user.id}"
                    await self.redis.set(reset_key, reset_token, ex=3600)  # 1小时有效期
                    self.logger.debug(f"密码重置令牌已缓存: user_id={user.id}")
                except Exception as e:
                    self.logger.warning(f"缓存密码重置令牌失败: user_id={user.id}, 错误={str(e)}")
            
            # 发送事件
            try:
                dispatch(AUTH_PASSWORD_RESET_REQUESTED, payload={
                    "user_id": user.id,
                    "email": user.email,
                    "reset_token": reset_token,
                })
            except Exception as e:
                self.logger.error(f"发送密码重置事件失败: user_id={user.id}, 错误={str(e)}", exc_info=True)
                return self.create_error_result(
                    error_code=ErrorCode.SYSTEM_ERROR,
                    error_message="发送重置链接失败，请稍后再试"
                )
            
            self.logger.info(f"密码重置请求成功: email={request.email}, user_id={user.id}")
            
            await self.invalidate_resource_cache(user.id)
            
            return self.create_success_result({
                "message": "如果该邮箱存在，重置链接将发送至该邮箱"
            })
        except Exception as e:
            self.logger.error(f"处理密码重置请求失败: email={request.email}, 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.INVALID_REQUEST,
                error_message="处理密码重置请求失败"
            )
    
    async def reset_password(self, params: PasswordResetParams) -> Result[Dict[str, Any]]:
        """重置密码
        
        Args:
            params: 密码重置参数
            
        Returns:
            Result[Dict[str, Any]]: 密码重置结果对象
        """
        try:
            self.logger.info("处理密码重置")
            
            # 验证重置令牌
            payload = self.token_service.verify_access_token(params.reset_token)
            if not payload or payload.get("type") != "reset":
                self.logger.warning("密码重置失败: 无效的令牌类型")
                return self.create_error_result(
                    error_code=ErrorCode.TOKEN_INVALID,
                    error_message="无效或已过期的重置令牌"
                )
                
            user_id = int(payload.get("sub"))
            if not user_id:
                self.logger.warning("密码重置失败: 缺少用户ID")
                return self.create_error_result(
                    error_code=ErrorCode.TOKEN_INVALID,
                    error_message="无效的重置令牌"
                )
            
            # 从缓存验证令牌
            if self.redis:
                try:
                    reset_key = f"password_reset:{user_id}"
                    cached_token = await self.redis.get(reset_key)
                    if not cached_token or cached_token.decode() != params.reset_token:
                        self.logger.warning(f"密码重置失败: 令牌不匹配或已被使用 - user_id={user_id}")
                        return self.create_error_result(
                            error_code=ErrorCode.TOKEN_INVALID,
                            error_message="无效或已过期的重置令牌"
                        )
                except Exception as e:
                    self.logger.warning(f"验证缓存令牌失败: user_id={user_id}, 错误={str(e)}")
            
            # 获取用户
            user = await self.get_resource_by_id(user_id)
            if not user:
                self.logger.warning(f"密码重置失败: 用户 {user_id} 不存在")
                return self.create_error_result(
                    error_code=ErrorCode.USER_NOT_FOUND,
                    error_message="用户不存在"
                )
                
            if not user.is_active:
                self.logger.warning(f"密码重置失败: 用户 {user_id} 未激活")
                return self.create_error_result(
                    error_code=ErrorCode.USER_INACTIVE,
                    error_message="用户未激活"
                )
            
            # 更新密码
            try:
                user.update_password(params.new_password)
                user.failed_login_attempts = 0
                user.locked_until = None
                await self.user_repo.save(user)
                # 发送事件
                dispatch(AUTH_PASSWORD_RESET_COMPLETED, payload={
                    "user_id": user.id,
                    "email": user.email,
                })
                self.logger.info(f"密码重置成功: user_id={user_id}")
                return self.create_success_result({
                    "message": "密码已成功重置，请使用新密码登录"
                })
                
            except Exception as e:
                self.logger.error(f"更新密码失败: user_id={user_id}, 错误={str(e)}", exc_info=True)
                return self.create_error_result(
                    error_code=ErrorCode.SYSTEM_ERROR,
                    error_message="重置密码失败，请稍后再试"
                )
        except Exception as e:
            self.logger.error(f"密码重置过程中发生错误: {str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.INVALID_REQUEST,
                error_message="处理密码重置请求失败"
            )
    
    async def logout(self, token: str, user_id: int) -> Result[Dict[str, Any]]:
        """用户登出
        
        Args:
            token: 访问令牌
            user_id: 用户ID
            
        Returns:
            Result[Dict[str, Any]]: 认证结果对象
        """
        try:
            self.logger.info(f"用户登出: user_id={user_id}")
            
            # 发送事件
            try:
                dispatch(AUTH_USER_LOGGED_OUT, payload={
                    "user_id": user_id,
                    "token": token,
                })
            except Exception as e:
                # 记录错误但不阻止主流程
                self.logger.error(f"发送登出事件失败: user_id={user_id}, 错误={str(e)}", exc_info=True)
            
            self.logger.info(f"用户登出成功: user_id={user_id}")
            
            return self.create_success_result({"success": True})
        except Exception as e:
            self.logger.error(f"用户登出失败: user_id={user_id}, 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.INVALID_REQUEST,
                error_message="登出失败，请稍后再试"
            )


