"""
角色管理API路由
"""
from typing import Any, Dict, List

from fastapi import APIRouter, Depends, Query, Path,status

from svc.apps.auth.schemas.role import RoleQueryParams
from svc.core.models.result import Result
from svc.apps.auth.schemas import (
    RoleCreate, 
    RoleUpdate, 
    RoleResponse,
    RoleListResponse
)
from svc.apps.auth.services import (
    RoleService
)
from svc.apps.auth.dependencies import (
    get_role_service,
    get_current_active_user,
    get_current_superuser,
    has_permission,
    resource_permission
)
from svc.apps.auth.models.user import User
from svc.core.exceptions.route_error_handler import handle_route_errors, AUTH_ERROR_MAPPING
from svc.core.exceptions.error_codes import ErrorCode
from svc.core.schemas.base import PageParams

router = APIRouter(tags=["角色管理"])


@router.get("", response_model=Result[RoleListResponse])
@handle_route_errors(AUTH_ERROR_MAPPING)
async def list_roles(
    params: PageParams = Depends(),
    search_term: str = Query(default="", description="搜索关键词", alias="searchTerm"),
    order_by: str = Query(default="created_at", description="排序字段", alias="orderBy"),
    order_desc: bool = Query(default=True, description="是否降序", alias="orderDesc"),
    role_service: RoleService = Depends(get_role_service),
    current_user: User = Depends(get_current_active_user),
    _: bool = Depends(has_permission("role:read")),
) -> Result[RoleListResponse]:
    """
    获取角色列表。
    
    需要role:read权限。
    """
    # 设置查询参数
    params_obj = RoleQueryParams(
        page_num=params.page_num,
        page_size=params.page_size,
        search_term=search_term,
        order_by=order_by,
        order_desc=order_desc
    )
    
    # 获取角色列表并返回结果
    return await role_service.get_roles(params_obj)


@router.get("/me", response_model=Result[List[RoleResponse]])
@handle_route_errors(AUTH_ERROR_MAPPING)
async def get_my_roles(
    role_service: RoleService = Depends(get_role_service),
    current_user: User = Depends(get_current_active_user),
) -> Result[List[RoleResponse]]:
    """
    获取当前用户的角色列表。
    
    需要用户已认证。
    """
    return await role_service.get_current_user_roles(current_user.id)


@router.get("/{role_id}", response_model=Result[RoleResponse])
@handle_route_errors(AUTH_ERROR_MAPPING)
async def get_role_details(
    role_id: int = Path(..., description="角色ID"),
    role_service: RoleService = Depends(get_role_service),
    current_user: User = Depends(get_current_active_user),
    _: Any = Depends(resource_permission("role", "read")),
) -> Result[RoleResponse]:
    """
    获取角色详情。
    
    需要对特定角色资源的读取权限。
    """
    
    # 获取角色详情并返回结果
    return await role_service.get_role(role_id=role_id)


@router.post("", response_model=Result[Dict[str, Any]], status_code=status.HTTP_201_CREATED)
@handle_route_errors(AUTH_ERROR_MAPPING)
async def create_role(
    role_in: RoleCreate,
    role_service: RoleService = Depends(get_role_service),
    current_user: User = Depends(get_current_active_user),
    _: bool = Depends(lambda: has_permission("role:create")),
) -> Result[Dict[str, Any]]:
    """
    创建新角色。
    
    需要role:create权限。
    """

    # 创建角色并返回结果
    return await role_service.create_role(role_in, current_user.id)
    return await role_service.create_role(role_in,)


@router.put("/{role_id}", response_model=Result[Dict[str, Any]])
@handle_route_errors(AUTH_ERROR_MAPPING)
async def update_role(
    role_in: RoleUpdate,
    role_id: int = Path(..., description="角色ID"),
    role_service: RoleService = Depends(get_role_service),
    current_user: User = Depends(get_current_superuser),
    _: Any = Depends(resource_permission("role", "update")),
) -> Result[Dict[str, Any]]:
    """
    更新角色信息。
    
    需要对特定角色资源的更新权限。
    """
    
    # 更新角色并返回结果
    return await role_service.update_role(current_user.id, role_id, role_in)


@router.delete("/{role_id}", response_model=Result[Dict[str, Any]])
@handle_route_errors(AUTH_ERROR_MAPPING)
async def delete_role(
    role_id: int = Path(..., description="角色ID"),
    role_service: RoleService = Depends(get_role_service),
    current_user: User = Depends(get_current_active_user),
    _: Any = Depends(resource_permission("role", "delete")),
) -> Result[Dict[str, Any]]:
    """
    删除角色。
    
    需要对特定角色资源的删除权限。
    """
    
    # 删除角色并返回结果
    return await role_service.delete_role(current_user.id, role_id) 