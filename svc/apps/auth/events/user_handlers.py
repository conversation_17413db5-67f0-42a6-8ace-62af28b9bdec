"""
用户模块的事件处理器 (已重构支持 DI)
提供处理用户相关事件的功能，实现关注点分离和松耦合设计
"""

import logging
from datetime import datetime
from typing import Any, Dict, Optional

# Import DI mechanism
from fastapi import Depends
from fastapi_events.dispatcher import dispatch
from fastapi_events.handlers.local import local_handler
from fastapi_events.typing import Event

from svc.apps.auth.dependencies import get_user_service
# Remove direct service instantiation if injected
# from svc.apps.auth.services.user import UserService
# Import services needed for DI
from svc.apps.auth.services.user import UserService
from svc.core.events.event_names import (
    AUTH_USER_ACTIVATED, AUTH_USER_DEACTIVATED, AUTH_USER_DELETED,
    AUTH_USER_PASSWORD_CHANGED, AUTH_USER_REGISTERED, AUTH_USER_ROLE_ASSIGNED,
    AUTH_USER_ROLE_REMOVED, AUTH_USER_UPDATED,
    MARKETING_INVITATION_PROCESS_REQUESTED, SYSTEM_AUDIT_LOG_RECORDED,
    SYSTEM_STATS_UPDATE_REQUESTED, SYSTEM_TASK_SCHEDULED,
    SYSTEM_USER_NOTIFICATION_REQUESTED)

# Remove direct db/redis imports if not directly used
# from redis.asyncio import Redis
# from sqlalchemy.ext.asyncio import AsyncSession


logger = logging.getLogger(__name__)


@local_handler.register(event_name=AUTH_USER_REGISTERED)
async def handle_user_created(
    event: Event,
    user_service: UserService = Depends(get_user_service), # Keep DI
):
    """处理用户创建事件，触发通知、统计、审计，并请求处理邀请关系"""
    event_name, payload = event
    user_id = payload.get("user_id")
    email = payload.get("email")
    full_name = payload.get("full_name")
    source = payload.get("source", "direct")
    created_by = payload.get("created_by_user_id")
    client_ip = payload.get("client_ip")
    client_device = payload.get("client_device")
    inviter_id = payload.get("inviter_id")

    if not user_id or not email:
        logger.error("处理用户创建事件失败: 缺少 user_id 或 email")
        return

    logger.info(f"新用户创建事件: user_id={user_id}, email={email}")

    # 处理欢迎通知、统计和审计日志
    try:
        # 触发欢迎通知事件
        dispatch(SYSTEM_USER_NOTIFICATION_REQUESTED, payload={
            "user_id": user_id,
            "notification_type": "welcome",
            "channel": "email",
            "data": {
                "user_id": user_id,
                "email": email,
                "full_name": full_name
            }
        })

        # 记录用户创建统计
        dispatch(SYSTEM_STATS_UPDATE_REQUESTED, payload={
            "entity_type": "system",
            "entity_id": 0,  # 系统级统计
            "metric_type": "user_registration",
            "increment_value": 1,
            "metadata": {
                "source": source,
                "datetime": datetime.utcnow().isoformat()
            }
        })

        # 记录审计日志
        dispatch(SYSTEM_AUDIT_LOG_RECORDED, payload={
            "user_id": created_by if created_by is not None else user_id,
            "action": "user_created",
            "resource_type": "user",
            "resource_id": str(user_id),  # 确保是字符串类型
            "username": email,  # 添加用户名
            "status": "success",  # 添加状态
            "message": f"用户注册成功: {email}",  # 添加消息
            "metadata": {
                "email": email,
                "ip_address": client_ip,
                "user_agent": client_device
            }
        })
    except Exception as e:
        logger.error(f"处理用户创建事件（通知/统计/审计）失败: user_id={user_id}, 错误={str(e)}", exc_info=True)

    # 检查并请求处理邀请关系
    if inviter_id:
        invitee_id = user_id  # 被邀请人就是当前创建的用户

        if not inviter_id:
            logger.warning(f"用户创建事件：邀请数据缺少邀请人ID: {inviter_id}")
            return

        try:
            inviter_id = int(inviter_id) # 确保 inviter_id 是整数
            if inviter_id == invitee_id:
                logger.warning(f"用户创建事件：用户不能邀请自己: inviter_id={inviter_id}, invitee_id={invitee_id}")
                return
        except ValueError:
             logger.warning(f"用户创建事件：邀请人ID格式无效: {inviter_id}")
             return

        # 触发邀请处理请求事件
        try:
            logger.info(f"用户创建事件：触发邀请处理请求: 邀请人ID={inviter_id}, 被邀请人ID={invitee_id}")
            dispatch(MARKETING_INVITATION_PROCESS_REQUESTED, payload={
                "inviter_id": inviter_id,
                "invitee_id": invitee_id,
                "client_ip": client_ip,
                "client_device": client_device
            })
        except Exception as e:
            logger.error(f"用户创建事件：触发邀请处理请求失败: user_id={user_id}, 错误={str(e)}", exc_info=True)

@local_handler.register(event_name=AUTH_USER_UPDATED)
async def handle_user_updated(
    event: Event,
    user_service: UserService = Depends(get_user_service), # Keep DI
):
    """处理用户更新事件，触发相关通知和统计更新"""
    event_name, payload = event
    user_id = payload.get("user_id")
    updated_fields = payload.get("updated_fields") or {} # Extract from payload
    updated_by = payload.get("updater_id")
    ip_address = payload.get("ip_address")
    user_agent = payload.get("user_agent")
    old_values = payload.get("old_values") or {} # Get old values if passed
    
    if not user_id:
        logger.error("处理用户更新事件失败: 缺少 user_id")
        return

    logger.info(f"用户信息更新: user_id={user_id}, 更新字段={updated_fields}")

    try:
        # 如果更新了敏感信息，记录审计日志
        sensitive_fields = ["email", "phone", "roles", "is_active", "is_superuser"] # Added 'roles'
        updated_sensitive = any(field in updated_fields for field in sensitive_fields)

        if updated_sensitive:
            # 记录审计日志
            dispatch(SYSTEM_AUDIT_LOG_RECORDED, payload={
                "user_id": updated_by if updated_by is not None else user_id,
                "action": "user_sensitive_info_updated",
                "resource_type": "user",
                "resource_id": str(user_id),  # 确保是字符串类型
                "status": "success",  # 添加状态
                "metadata": {
                    "updated_fields": [f for f in updated_fields if f in sensitive_fields],
                    "ip_address": ip_address,
                    "user_agent": user_agent
                }
            })

        # 如果更新了邮箱，发送确认邮件
        if "email" in updated_fields:
            old_email = old_values.get("email") # Get old email from old_values
            new_email = updated_fields.get("email")
            # 触发邮箱变更通知
            dispatch(SYSTEM_USER_NOTIFICATION_REQUESTED, payload={
                "user_id": user_id,
                "notification_type": "email_changed",
                "channel": "email",
                "data": {
                    "user_id": user_id,
                    "old_email": old_email or "",
                    "new_email": new_email
                }
            })
    except Exception as e:
        logger.error(f"处理用户更新事件失败: user_id={user_id}, 错误={str(e)}", exc_info=True)

@local_handler.register(event_name=AUTH_USER_DELETED)
async def handle_user_deleted(
    event: Event,
    user_service: UserService = Depends(get_user_service), # Keep DI
):
    """处理用户删除事件，清理相关资源、记录审计日志"""
    event_name, payload = event
    user_id = payload.get("user_id")
    email = payload.get("email")
    deleted_by = payload.get("deleter_id")
    ip_address = payload.get("ip_address")
    user_agent = payload.get("user_agent")
    reason = payload.get("reason") or ""
    delete_content = payload.get("delete_content", False)
    
    if not user_id or not email:
        logger.error("处理用户删除事件失败: 缺少 user_id 或 email")
        return

    logger.info(f"用户被删除: user_id={user_id}, email={email}")

    try:
        # 记录审计日志
        dispatch(SYSTEM_AUDIT_LOG_RECORDED, payload={
            "user_id": deleted_by,
            "action": "user_deleted",
            "resource_type": "user",
            "resource_id": str(user_id),  # 确保是字符串类型
            "status": "success",  # 添加状态
            "metadata": {
                "email": email,
                "ip_address": ip_address,
                "user_agent": user_agent,
                "reason": reason
            }
        })

        # 清理用户相关资源
        # 这里可以触发清理用户数据的事件
        dispatch(SYSTEM_TASK_SCHEDULED, payload={
            "task_type": "user_data_cleanup",
            "params": {
                "user_id": user_id,
                "email": email,
                "delete_content": delete_content
            }
        })
    except Exception as e:
        logger.error(f"处理用户删除事件失败: user_id={user_id}, 错误={str(e)}", exc_info=True)

@local_handler.register(event_name=AUTH_USER_ACTIVATED)
async def handle_user_activated(
    event: Event,
    user_service: UserService = Depends(get_user_service), # Keep DI
):
    """处理用户激活事件"""
    event_name, payload = event
    user_id = payload.get("user_id")
    activated_by = payload.get("activated_by")
    ip_address = payload.get("ip_address")
    user_agent = payload.get("user_agent")
    
    if not user_id:
        logger.error("处理用户激活事件失败: 缺少 user_id")
        return
        
    logger.info(f"用户被激活: user_id={user_id}")

    try:
        # 发送激活通知
        dispatch(SYSTEM_USER_NOTIFICATION_REQUESTED, payload={
             "user_id": user_id,
             "notification_type": "account_activated",
             "channel": "email", # Or other channels
             "data": { "user_id": user_id }
        })

        # 记录审计日志
        dispatch(SYSTEM_AUDIT_LOG_RECORDED, payload={
            "user_id": activated_by, # The user performing the action
            "action": "user_activated",
            "resource_type": "user",
            "resource_id": str(user_id),  # 确保是字符串类型
            "status": "success",  # 添加状态
            "metadata": {
                "ip_address": ip_address,
                "user_agent": user_agent
            }
        })
    except Exception as e:
        logger.error(f"处理用户激活事件失败: user_id={user_id}, 错误={str(e)}", exc_info=True)

@local_handler.register(event_name=AUTH_USER_DEACTIVATED)
async def handle_user_deactivated(
    event: Event,
    user_service: UserService = Depends(get_user_service), # Keep DI
):
    """处理用户停用事件"""
    event_name, payload = event
    user_id = payload.get("user_id")
    deactivated_by = payload.get("deactivated_by")
    reason = payload.get("reason") or ""
    ip_address = payload.get("ip_address")
    user_agent = payload.get("user_agent")

    if not user_id:
        logger.error("处理用户停用事件失败: 缺少 user_id")
        return

    logger.info(f"用户被停用: user_id={user_id}, 原因: {reason}")

    try:
        # 发送停用通知 (可选)
        dispatch(SYSTEM_USER_NOTIFICATION_REQUESTED, payload={
             "user_id": user_id,
             "notification_type": "account_deactivated",
             "channel": "email", # Or other channels
             "data": { "user_id": user_id, "reason": reason }
        })
        
        # 记录审计日志
        dispatch(SYSTEM_AUDIT_LOG_RECORDED, payload={
            "user_id": deactivated_by,
            "action": "user_deactivated",
            "resource_type": "user",
            "resource_id": str(user_id),  # 确保是字符串类型
            "status": "success",  # 添加状态
            "metadata": {
                "reason": reason,
                "ip_address": ip_address,
                "user_agent": user_agent
            }
        })

        # 清理用户会话/令牌 (如果需要)
        # dispatch(AUTH_USER_TOKENS_INVALIDATE, payload={"user_id": user_id}) # Example
        
    except Exception as e:
        logger.error(f"处理用户停用事件失败: user_id={user_id}, 错误={str(e)}", exc_info=True)

@local_handler.register(event_name=AUTH_USER_ROLE_ASSIGNED)
async def handle_user_role_assigned(
    event: Event,
    user_service: UserService = Depends(get_user_service), # Keep DI
):
    """处理用户角色分配事件，记录审计并清除用户缓存""" 
    event_name, payload = event
    user_id = payload.get("user_id")
    role_id = payload.get("role_id")
    assigned_by = payload.get("assigned_by_user_id")
    role_name = payload.get("role_name")
    
    if not user_id or not role_id:
        logger.error("处理用户角色分配事件失败: 缺少 user_id 或 role_id")
        return

    logger.debug(f"处理用户角色分配事件: user_id={user_id}, role_id={role_id}")

    try:
        # 记录审计日志 (由 role_handlers 处理，这里可能不需要重复记录)
        # dispatch(SYSTEM_AUDIT_LOG_RECORDED, ...)

        # 清除用户缓存 (因为角色变化影响权限)
        await user_service.invalidate_resource_cache(user_id)
        logger.info(f"用户角色分配，已清除用户缓存: user_id={user_id}")

        # 发送通知 (可选)
        # dispatch(SYSTEM_USER_NOTIFICATION_REQUESTED, ...) 
        
    except Exception as e:
        logger.error(f"处理用户角色分配事件失败: user_id={user_id}, role_id={role_id}, 错误={str(e)}", exc_info=True)

@local_handler.register(event_name=AUTH_USER_ROLE_REMOVED)
async def handle_user_role_removed(
    event: Event,
    user_service: UserService = Depends(get_user_service), # Keep DI
):
    """处理用户角色移除事件，记录审计并清除用户缓存"""
    event_name, payload = event
    user_id = payload.get("user_id")
    role_id = payload.get("role_id")
    removed_by = payload.get("removed_by_user_id")
    role_name = payload.get("role_name")
    
    if not user_id or not role_id:
        logger.error("处理用户角色移除事件失败: 缺少 user_id 或 role_id")
        return
        
    logger.debug(f"处理用户角色移除事件: user_id={user_id}, role_id={role_id}")

    try:
        # 记录审计日志 (由 role_handlers 处理，这里可能不需要重复记录)
        # dispatch(SYSTEM_AUDIT_LOG_RECORDED, ...)

        # 清除用户缓存 (因为角色变化影响权限)
        await user_service.invalidate_resource_cache(user_id)
        logger.info(f"用户角色移除，已清除用户缓存: user_id={user_id}")

        # 发送通知 (可选)
        # dispatch(SYSTEM_USER_NOTIFICATION_REQUESTED, ...) 

    except Exception as e:
        logger.error(f"处理用户角色移除事件失败: user_id={user_id}, role_id={role_id}, 错误={str(e)}", exc_info=True)

@local_handler.register(event_name=AUTH_USER_PASSWORD_CHANGED)
async def handle_user_password_changed(
    event: Event,
):
    """处理用户密码变更事件，记录审计日志"""
    event_name, payload = event
    user_id = payload.get("user_id")
    ip_address = payload.get("ip_address")
    user_agent = payload.get("user_agent")

    if not user_id:
        logger.error("处理用户密码变更事件失败: 缺少 user_id")
        return

    logger.info(f"用户密码已变更: user_id={user_id}")

    try:
        # 记录审计日志
        dispatch(SYSTEM_AUDIT_LOG_RECORDED, payload={
            "user_id": user_id, # Action performed by the user themselves
            "action": "user_password_changed",
            "resource_type": "user",
            "resource_id": str(user_id),  # 确保是字符串类型
            "status": "success",  # 添加状态
            "metadata": {
                "ip_address": ip_address,
                "user_agent": user_agent
            }
        })

    except Exception as e:
        logger.error(f"处理用户密码变更事件失败: user_id={user_id}, 错误={str(e)}", exc_info=True) 