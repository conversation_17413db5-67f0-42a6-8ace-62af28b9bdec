"""
用户模块的模型定义，包含用户和角色相关的所有模型。
采用函数式设计和RORO模式。
"""
from datetime import datetime
from enum import Enum
from typing import Optional, List, Dict, Any, Protocol, TypeVar


from pydantic import BaseModel, EmailStr, Field, field_validator, ConfigDict
from svc.apps.auth.schemas.role import RoleProtocol,RoleBase
from svc.core.models.base import CamelCaseModel
from svc.core.schemas.base import PageParams, PaginatedResponse




class UserProtocol(Protocol):
    """
    用户模型协议，定义用户对象必须实现的属性和方法
    """
    id: int
    username: str
    email: str
    hashed_password: str
    fullname: Optional[str]
    is_active: bool
    is_superuser: bool
    
    # 时间戳
    created_at: datetime
    updated_at: datetime
    last_login: Optional[datetime]
    
    # 用户配置
    avatar_url: Optional[str]
    locale: str
    timezone: str
    
    # 安全相关
    failed_login_attempts: int
    locked_until: Optional[datetime]
    password_changed_at: Optional[datetime]
    
    # 用户角色
    roles: List["RoleProtocol"]
    
    # 状态属性
    is_locked: bool
    
    def has_permission(self, permission: str) -> bool:
        """检查用户是否拥有指定权限"""
        ...
    
    async def update_last_login(self, db) -> None:
        """更新用户最后登录时间"""
        ...
    
    async def add_role(self, db, role: RoleProtocol) -> None:
        """为用户添加角色"""
        ...
    
    async def remove_role(self, db, role: RoleProtocol) -> None:
        """移除用户的角色"""
        ...

# 用户类型别名，用于类型注解
UserType = TypeVar("UserType", bound=UserProtocol)
# -------------------- 用户模型 --------------------

class UserBase(CamelCaseModel):
    """用户基本信息"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "email": "<EMAIL>",
                "username": "johndoe",
                "fullname": "John Doe"
            }
        }
    )
    
    email: EmailStr = Field(..., description="用户邮箱")
    username: str = Field(..., description="用户名")
    fullname: Optional[str] = Field(None, description="用户全名")
    is_active: bool = Field(True, description="是否激活")
    is_superuser: bool = Field(False, description="是否为超级用户")


class UserCreate(UserBase):
    """用户创建请求"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "email": "<EMAIL>",
                "username": "guest",
                "password": "qinjun666",
                "is_active": True,
                "is_superuser": True,
                "fullname":"qinjun",
                "roles":['user'],
                "inviter_id": "1"
            }
        }
    )
    
    password: str = Field(..., description="用户密码")
    roles: List[str] = Field(default_factory=list, description="用户角色")
    inviter_id: Optional[str] = Field(default=None, description="邀请人ID")
    
    @field_validator("password")
    def validate_password_strength(cls, value: str) -> str:
        """验证密码强度"""
        if len(value) < 8:
            raise ValueError("密码长度必须至少为8个字符")
        return value


class UserUpdate(CamelCaseModel):
    """用户更新请求"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "username": "johndoe",
                "email": "<EMAIL>",
                "password": "newpassword123",
                "fullname": "John Doe",
                "is_active": True,
                "is_superuser": False,
                "avatar_url": "https://example.com/avatar.jpg",
                "locale": "zh-CN",
                "timezone": "Asia/Shanghai"
            }
        }
    )
    
    email: Optional[EmailStr] = Field(None, description="用户邮箱")
    username: Optional[str] = Field(None, description="用户名")
    fullname: Optional[str] = Field(None, description="用户全名")
    password: Optional[str] = Field(None, description="用户密码")
    is_active: Optional[bool] = Field(None, description="是否激活")
    is_superuser: Optional[bool] = Field(None, description="是否为超级用户")
    avatar_url: Optional[str] = Field(None, description="头像URL")
    locale: Optional[str] = Field(None, description="语言区域")
    timezone: Optional[str] = Field(None, description="时区")
    
    @field_validator("password")
    def validate_password_strength(cls, value: Optional[str]) -> Optional[str]:
        """验证密码强度（如果提供）"""
        if value is not None and len(value) < 8:
            raise ValueError("密码长度必须至少为8个字符")
        return value


class UserResponse(UserBase):
    """用户响应模型"""
    model_config = ConfigDict(
        from_attributes=True,
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "id": 1,
                "email": "<EMAIL>",
                "fullname": "John Doe",
                "is_active": True,
                "is_superuser": False,
                "created_at": "2024-03-24T12:00:00",
                "updated_at": "2024-03-24T12:00:00",
                "last_login": "2024-03-24T12:00:00",
                "avatar_url": "https://example.com/avatar.jpg",
                "locale": "zh-CN",
                "timezone": "Asia/Shanghai",
                "failed_login_attempts": 0,
                "locked_until": None,
                "password_changed_at": "2024-03-24T12:00:00",
                "is_locked": False
            }
        }
    )
    
    id: int = Field(..., description="用户ID")
    email: EmailStr = Field(..., description="用户邮箱")
    username: str = Field(..., description="用户名")
    fullname: Optional[str] = Field(None, description="用户全名")
    is_active: bool = Field(..., description="是否激活")
    is_superuser: bool = Field(..., description="是否为超级用户")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    last_login: Optional[datetime] = Field(None, description="最后登录时间")
    
    # 用户配置
    avatar_url: Optional[str] = Field(None, description="头像URL")
    locale: str = Field(..., description="语言区域")
    timezone: str = Field(..., description="时区")
    
    # 安全相关
    failed_login_attempts: int = Field(..., description="登录失败次数")
    locked_until: Optional[datetime] = Field(None, description="锁定截止时间")
    password_changed_at: Optional[datetime] = Field(None, description="密码修改时间")
    is_locked: bool = Field(..., description="是否被锁定")


class UserWithRoles(UserResponse):
    """包含角色信息的用户响应模型"""
    model_config = ConfigDict(
        from_attributes=True,
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "roles": [
                    {
                        "name": "admin",
                        "description": "管理员角色"
                    }
                ]
            }
        }
    )
    
    roles: List[RoleBase] = Field([], description="用户角色列表")




# -------------------- 用户操作参数和结果 --------------------

class GetUserParams(BaseModel):
    """获取用户参数"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "user_id": 1,
                "email": "<EMAIL>"
            }
        }
    )
    
    user_id: Optional[int] = Field(default=None, description="用户ID")
    email: Optional[EmailStr] = Field(default=None, description="用户邮箱")


class GetUsersParams(BaseModel):
    """获取用户列表参数"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "page_num": 1,
                "page_size": 10,
                "search_term": "john",
                "is_active": True,
                "order_by": "created_at",
                "order_desc": True
            }
        }
    )
    
    page_params: PageParams = Field(default_factory=PageParams, description="分页参数")
    search_term: Optional[str] = Field(default=None, description="搜索关键词")
    is_active: Optional[bool] = Field(default=None, description="是否激活")
    order_by: str = Field(default="created_at", description="排序字段")
    order_desc: bool = Field(default=True, description="是否降序")


class CreateUserParams(BaseModel):
    """创建用户参数"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "user_data": {},
                "send_welcome_email": True,
                "client_ip": "127.0.0.1",
                "client_device": "Mozilla/5.0 (Windows NT 10.0; Win64; x64)"
            }
        }
    )
    
    user_data: UserCreate = Field(description="用户创建数据")
    send_welcome_email: bool = Field(default=False, description="是否发送欢迎邮件")
    client_ip: Optional[str] = Field(default=None, description="客户端IP地址")
    client_device: Optional[str] = Field(default=None, description="客户端设备信息")


class UpdateUserParams(BaseModel):
    """更新用户参数"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "user_id": 1,
                "user_data": {}
            }
        },
        from_attributes=True
    )
    
    user_id: int = Field(description="用户ID")
    user_data: UserUpdate = Field(description="用户更新数据")

    


class DeleteUserParams(BaseModel):
    """删除用户参数"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "user_id": 1,
                "permanent": False
            }
        }
    )
    
    user_id: int = Field(description="用户ID")
    executor_id: int = Field(description="执行者ID")
    permanent: bool = Field(default=False, description="是否永久删除")


# -------------------- 角色操作参数和结果 --------------------

class AssignRoleParams(BaseModel):
    """分配角色参数"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "user_id": 1,
                "role_id": 1,
                "executor_id": 1
            }
        }
    )
    
    user_id: int = Field(description="用户ID")
    role_id: int = Field(description="角色ID")
    executor_id: int = Field(description="执行者ID")


class RemoveRoleParams(BaseModel):
    """移除角色参数"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "user_id": 1,
                "role_id": 1,
                "executor_id": 1
            }
        }
    )
    
    user_id: int = Field(description="用户ID")
    role_id: int = Field(description="角色ID")
    executor_id: int = Field(description="执行者ID")


class UserListResponse(PaginatedResponse[UserResponse]):
    """用户列表响应模型，继承自分页基类"""
    pass # 字段已在 PaginatedResponse 中定义

