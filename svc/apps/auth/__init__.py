"""
认证模块
包含用户认证、授权和管理功能
"""

# 导出ORM模型
from svc.apps.auth.models import User, Role, Permission, user_role, role_permission_table

# 导出Schema模型
from svc.apps.auth.schemas import (
    # 基础模型
    
    # 认证相关模型
    AuthCredentials, TokenData, TokenPayload,
    RefreshTokenRequest, LoginRequest, LoginContext,
    LoginParams, PasswordResetRequest,
    PasswordResetParams,
    
    # 用户相关模型
    UserBase, UserCreate, UserUpdate, UserResponse, UserWithRoles,
    RoleBase, RoleCreate, RoleUpdate, RoleResponse,
    
    # 操作参数和结果
    GetUserParams, GetUsersParams, CreateUserParams, UpdateUserParams,
    DeleteUserParams, AssignRoleParams, RemoveRoleParams,
)

# 导出服务类
from svc.apps.auth.services import (
    AuthService,
    UserService,
    RoleService,
)

# 导出依赖函数
from svc.apps.auth.dependencies import (
    get_auth_service,
    get_user_service,
    get_role_service,
    get_token_service,
    get_current_user,
    get_current_active_user,
    get_current_superuser,
)

# 导出工具函数
from svc.core.exceptions.route_error_handler import handle_route_errors, AUTH_ERROR_MAPPING
from svc.core.exceptions.error_codes import ErrorCode

# 导出事件
from svc.apps.auth.events import (
    user_handlers,
    auth_handlers,
    role_handlers
)

__all__ = [
    # 模型
    "User", "Role", "Permission", "user_role", "role_permission_table",
    
    # Schema

    "AuthCredentials", "TokenData", "TokenPayload",
    "RefreshTokenRequest", "LoginRequest", "LoginContext",
    "LoginParams", "PasswordResetRequest",
    "PasswordResetParams",
    "UserBase", "UserCreate", "UserUpdate", "UserResponse", "UserWithRoles",
    "RoleBase", "RoleCreate", "RoleUpdate", "RoleResponse",
    "GetUserParams", "GetUsersParams", "CreateUserParams", "UpdateUserParams",
    "DeleteUserParams", "AssignRoleParams",
    "RemoveRoleParams",
    
    # 服务类
    "AuthService", "UserService", "RoleService",
    
    # 依赖函数
    "get_auth_service", "get_user_service", "get_role_service",
    "get_token_service", "get_current_user", "get_current_active_user",
    "get_current_superuser",
    
    # 错误处理
     "ErrorCode","handle_route_errors"

    # 事件
    "user_handlers", "auth_handlers", "role_handlers"
]


