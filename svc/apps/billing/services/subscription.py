"""
订阅服务模块，提供面向对象形式的订阅管理功能。
使用Result模式处理错误，采用面向对象编程风格。
"""

from datetime import timedelta
from typing import Optional

from fastapi_events.dispatcher import dispatch
from redis.asyncio import Redis
from sqlalchemy.ext.asyncio import AsyncSession

from svc.apps.billing.models.subscription import Subscription
from svc.apps.billing.repositories.subscription import SubscriptionRepository
from svc.apps.billing.repositories.subscription_plan import \
    SubscriptionPlanRepository
from svc.apps.billing.schemas.subscription import (
    CancelSubscriptionParams, ChangePlanParams, CreateSubscriptionParams,
    GetSubscriptionParams, GetSubscriptionsParams, PauseSubscriptionParams,
    ReactivateSubscriptionParams, RenewSubscriptionParams,
    ResumeSubscriptionParams, SubscriptionListResponse, SubscriptionResponse,
    UpdateSubscriptionParams, UpgradeSubscriptionParams,
    UserSubscriptionsResult)
from svc.apps.billing.schemas.subscription_plan import SubscriptionPlanResponse
from svc.core.events import event_names
from svc.core.exceptions.error_codes import ErrorCode
from svc.core.models.result import Result, ResultFactory
from svc.core.services.base import BaseService
from svc.core.services.mixins.batch_operation import BatchOperationMixin
from svc.core.services.mixins.cache import CacheMixin
from svc.core.services.mixins.error_result import ErrorResultMixin
from svc.core.utils.datetime_utils import get_utc_now_without_tzinfo, is_after

# 定义缺失的参数类


# 缓存过期时间（秒）
CACHE_TTL = 3600  # 1小时

class SubscriptionService(BaseService, ErrorResultMixin, BatchOperationMixin, CacheMixin):
    """订阅服务类，提供订阅管理相关功能
    
    该服务类负责：
    1. 处理订阅的业务逻辑
    2. 验证请求参数和权限
    3. 通过仓库层访问数据
    4. 触发相关事件
    5. 管理缓存
    """
    
    # 设置资源类型名称
    resource_type = "subscription"
    
    def __init__(
        self,
        redis: Redis,
        subscription_repo: SubscriptionRepository,
        subscription_plan_repo: SubscriptionPlanRepository
    ):
        """初始化订阅服务
        
        Args:
            db: 数据库会话
            redis: Redis客户端
            subscription_repo: 订阅仓库
            subscription_plan_repo: 订阅计划仓库
        """
        BaseService.__init__(self, redis)
        self.subscription_repo = subscription_repo
        self.subscription_plan_repo = subscription_plan_repo
    
    async def get_resource_by_id(self, subscription_id: int) -> Optional[Subscription]:
        """获取指定ID的订阅资源
        
        Args:
            subscription_id: 订阅ID
            
        Returns:
            Optional[Subscription]: 订阅对象，不存在时返回None
        """
        return await self.subscription_repo.get_by_id(self.db, subscription_id)
    
    async def create_subscription(self, params: CreateSubscriptionParams) -> Result[SubscriptionResponse]:
        """创建订阅
        
        Args:
            params: 创建订阅参数
            
        Returns:
            订阅结果对象
        """
        self.logger.info(f"开始创建订阅: user_id={params.user_id}, plan_id={params.subscription_data.plan_id}")
        
        try:
            # 检查订阅计划是否存在
            plan = await self.subscription_plan_repo.get_by_id(self.db, params.subscription_data.plan_id)
            if not plan:
                self.logger.warning(f"订阅计划不存在: plan_id={params.subscription_data.plan_id}")
                return self.create_error_result(
                    error_code="PLAN_NOT_FOUND",
                    error_message="订阅计划不存在"
                )
            
            # 检查用户是否已有活跃订阅
            active_subscription = await self.subscription_repo.get_active_by_user_id(self.db, params.user_id)
            if active_subscription:
                self.logger.warning(f"用户已有活跃订阅: user_id={params.user_id}")
                return self.create_error_result(
                    error_code="ACTIVE_SUBSCRIPTION_EXISTS",
                    error_message="用户已有活跃订阅"
                )
            
            # 准备创建数据
            now = get_utc_now_without_tzinfo()
            subscription_data = params.subscription_data.model_dump()
            subscription_data["user_id"] = params.user_id
            
            # 基础版使用试用期，高级版(非basic)直接激活
            is_basic_plan = plan.tier == "basic"
            if is_basic_plan and plan.trial_period_days:
                subscription_data["trial_start"] = now
                subscription_data["trial_end"] = now + timedelta(days=plan.trial_period_days)
                subscription_data["current_period_start"] = subscription_data["trial_end"]
                subscription_data["status"] = "trialing"
            else:
                subscription_data["current_period_start"] = now
                subscription_data["status"] = "active"
            
            # 计算订阅结束时间
            if plan.interval == "month":
                subscription_data["current_period_end"] = subscription_data["current_period_start"] + timedelta(days=30 * plan.interval_count)
            elif plan.interval == "year":
                subscription_data["current_period_end"] = subscription_data["current_period_start"] + timedelta(days=365 * plan.interval_count)
            else:
                return self.create_error_result(
                    error_code="INVALID_INTERVAL",
                    error_message=f"不支持的订阅周期: {plan.interval}"
                )
            
            # 创建订阅
            subscription = await self.subscription_repo.create(self.db, subscription_data)
            
            # 构建响应
            response = SubscriptionResponse.model_validate(subscription)
            response.plan = SubscriptionPlanResponse.model_validate(plan)
            
            # 缓存订阅
            await self._cache_subscription(subscription.id, response)
            
            # 触发订阅创建事件
            event_data = {
                "subscription_id": subscription.id,
                "user_id": subscription.user_id,
                "plan_id": subscription.plan_id,
                "status": subscription.status,
                "start_date": subscription.start_date.isoformat() if subscription.start_date else None,
                "end_date": subscription.end_date.isoformat() if subscription.end_date else None,
                "current_period_start": subscription.current_period_start.isoformat() if subscription.current_period_start else None,
                "current_period_end": subscription.current_period_end.isoformat() if subscription.current_period_end else None,
                "plan_name": plan.name,
                "plan_price": plan.price,
                "plan_currency": plan.currency
            }
            dispatch(event_names.SUBSCRIPTION_CREATED, **event_data)
            
            self.logger.info(f"订阅创建成功: subscription_id={subscription.id}")
            return self.create_success_result(response)
            
        except Exception as e:
            self.logger.error(f"订阅创建失败: {str(e)}", exc_info=True)
            return self.create_error_result(
                error_code="CREATE_FAILED",
                error_message=f"创建订阅失败: {str(e)}"
            )
    
    async def get_subscription(self, params: GetSubscriptionParams) -> Result[SubscriptionResponse]:
        """获取订阅
        
        Args:
            params: 获取订阅参数
            
        Returns:
            订阅结果对象
        """
        self.logger.info(f"获取订阅信息: subscription_id={params.subscription_id}")
        
        try:
            # 先尝试从缓存获取
            cached_response = await self._get_cached_subscription(params.subscription_id)
            if cached_response:
                self.logger.debug(f"从缓存获取到订阅: subscription_id={params.subscription_id}")
                # 验证用户权限
                if params.user_id and cached_response.user_id != params.user_id:
                    return self.permission_denied_result(params.subscription_id)
                return self.create_success_result(cached_response)
            
            # 从数据库获取
            subscription = await self.get_resource_by_id(params.subscription_id)
            if not subscription:
                return self.resource_not_found_result(params.subscription_id)
            
            # 验证用户权限
            if params.user_id and subscription.user_id != params.user_id:
                return self.permission_denied_result(params.subscription_id)
            
            # 获取关联的计划
            plan = await self.subscription_plan_repo.get_by_id(self.db, subscription.plan_id)
            if not plan:
                return self.create_error_result(
                    error_code="PLAN_NOT_FOUND",
                    error_message="订阅计划不存在"
                )
            
            # 构建响应
            response = SubscriptionResponse.model_validate(subscription)
            response.plan = SubscriptionPlanResponse.model_validate(plan)
            
            # 缓存结果
            await self._cache_subscription(subscription.id, response)
            
            return self.create_success_result(response)
            
        except Exception as e:
            self.logger.error(f"获取订阅失败: {str(e)}", exc_info=True)
            return self.create_error_result(
                error_code="GET_FAILED",
                error_message=f"获取订阅失败: {str(e)}"
            )
    
    async def get_subscriptions(self, params: GetSubscriptionsParams) -> Result:
        """获取订阅列表
        
        Args:
            params: 获取订阅列表参数
            
        Returns:
            订阅列表结果对象
        """
        self.logger.info(f"获取订阅列表: user_id={params.user_id}, status={params.status}, page_num={params.page_num}, page_size={params.page_size}")
        
        try:
            filters = {}
            if params.user_id is not None:
                filters['user_id'] = params.user_id
            if params.status is not None:
                filters['status'] = params.status

            subscriptions, total = await self.subscription_repo.get_paginated(
                page=params.page_num,
                page_size=params.page_size,
                order_by="created_at",
                order_direction="desc",
                **filters
            )

            import math
            page_count = math.ceil(total / params.page_size) if params.page_size > 0 else 0

            response = SubscriptionListResponse(
                items=[SubscriptionResponse.model_validate(s) for s in subscriptions],
                total=total,
                page_num=params.page_num,
                page_size=params.page_size,
                page_count=page_count
            )

            return self.create_success_result(response)

        except Exception as e:
            self.logger.error(f"获取订阅列表失败: {str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.OPERATION_FAILED,
                error_message=f"获取订阅列表失败: {str(e)}"
            )
    
    async def get_active_subscription(self, user_id: int) -> Result[SubscriptionResponse]:
        """获取用户当前活跃的订阅
        
        Args:
            user_id: 用户ID
            
        Returns:
            订阅结果对象
        """
        self.logger.info(f"获取用户活跃订阅: user_id={user_id}")
        
        try:
            # 先尝试从缓存获取
            cache_key = self._get_active_subscription_cache_key(user_id)
            cached_response = await self._get_cached_response(cache_key, SubscriptionResponse)
            if cached_response:
                self.logger.debug(f"从缓存获取到活跃订阅: user_id={user_id}")
                return self.create_success_result(cached_response)
            
            # 从数据库获取
            self.logger.debug(f"从数据库获取活跃订阅: user_id={user_id}")
            
            # 使用仓库方法获取活跃订阅
            subscription = await self.subscription_repo.get_active_by_user_id(self.db, user_id)
            
            if not subscription:
                self.logger.info(f"用户没有活跃订阅: user_id={user_id}")
                return self.create_error_result(
                    error_code="ACTIVE_SUBSCRIPTION_NOT_FOUND",
                    error_message="未找到活跃订阅"
                )
            
            # 加载关联的计划
            plan = await self.subscription_plan_repo.get_by_id(self.db, subscription.plan_id)
            if not plan:
                self.logger.warning(f"订阅计划不存在: plan_id={subscription.plan_id}")
                return self.create_error_result(
                    error_code="PLAN_NOT_FOUND",
                    error_message="订阅计划不存在"
                )
            
            # 构建响应
            response = SubscriptionResponse.model_validate(subscription)
            response.plan = SubscriptionPlanResponse.model_validate(plan)
            
            # 缓存结果
            await self._cache_subscription(subscription.id, response)
            await self._cache_response(cache_key, response, CACHE_TTL)
            
            return self.create_success_result(response)
            
        except Exception as e:
            self.logger.error(f"获取活跃订阅失败: user_id={user_id}, 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                error_code="GET_FAILED",
                error_message=f"获取活跃订阅失败: {str(e)}"
            )
    
    async def update_subscription(self, params: UpdateSubscriptionParams) -> Result[SubscriptionResponse]:
        """更新订阅
        
        Args:
            params: 更新订阅参数
            
        Returns:
            订阅结果对象
        """
        self.logger.info(f"更新订阅: subscription_id={params.subscription_id}, user_id={params.user_id}")
        
        try:
            # 获取订阅
            subscription = await self.get_resource_by_id(params.subscription_id)
            if not subscription:
                self.logger.warning(f"订阅不存在: subscription_id={params.subscription_id}")
                return self.resource_not_found_result(params.subscription_id, "SUBSCRIPTION_NOT_FOUND")
            
            # 验证用户权限
            if params.user_id and subscription.user_id != params.user_id:
                self.logger.warning(f"用户权限不足: subscription_id={params.subscription_id}, 期望user_id={subscription.user_id}, 实际user_id={params.user_id}")
                return self.permission_denied_result(params.subscription_id)
            
            # 提取更新数据
            update_data = params.subscription_data.model_dump(exclude_unset=True)
            if not update_data:
                self.logger.debug(f"没有需要更新的数据: subscription_id={params.subscription_id}")
                
                # 不进行额外查询，直接返回当前订阅
                # 获取关联的计划
                plan = await self.subscription_plan_repo.get_by_id(self.db, subscription.plan_id)
                
                # 构建响应
                response = SubscriptionResponse.model_validate(subscription)
                if plan:
                    response.plan = SubscriptionPlanResponse.model_validate(plan)
                    
                return self.create_success_result(response)
            
            # 如果更新了关键字段，需要清除用户的活跃订阅缓存
            clear_active_cache = any(field in update_data for field in ["status", "end_date"])
            
            # 检查计划ID是否发生变化
            if "plan_id" in update_data:
                plan = await self.subscription_plan_repo.get_by_id(self.db, update_data["plan_id"])
                if not plan:
                    self.logger.warning(f"订阅计划不存在: plan_id={update_data['plan_id']}")
                    return self.create_error_result(
                        error_code="PLAN_NOT_FOUND",
                        error_message="订阅计划不存在"
                    )
            
            # 记录更新前的重要字段
            old_status = subscription.status
            old_plan_id = subscription.plan_id
            
            # 使用仓库方法更新订阅
            subscription = await self.subscription_repo.update(
                self.db,
                db_obj=subscription,
                data=update_data
            )
            
            if not subscription:
                return self.create_error_result(
                    error_code="UPDATE_FAILED",
                    error_message="更新订阅失败，无法查找到订阅"
                )
            
            # 获取关联的计划
            plan = await self.subscription_plan_repo.get_by_id(self.db, subscription.plan_id)
            
            # 构建响应
            response = SubscriptionResponse.model_validate(subscription)
            if plan:
                response.plan = SubscriptionPlanResponse.model_validate(plan)
            
            # 更新缓存
            await self._cache_subscription(subscription.id, response)
            
            # 如果需要，清除活跃订阅缓存
            if clear_active_cache:
                await self._clear_active_subscription_cache(subscription.user_id)
            
            # 构建事件数据
            event_data = {
                "subscription_id": subscription.id,
                "user_id": subscription.user_id,
                "plan_id": subscription.plan_id,
                "status": subscription.status,
                "updated_fields": list(update_data.keys()),
                "current_period_start": subscription.current_period_start.isoformat() if subscription.current_period_start else None,
                "current_period_end": subscription.current_period_end.isoformat() if subscription.current_period_end else None
            }
            
            # 添加状态变更信息
            if "status" in update_data and old_status != subscription.status:
                event_data["old_status"] = old_status
                event_data["new_status"] = subscription.status
            
            # 添加计划变更信息
            if "plan_id" in update_data and old_plan_id != subscription.plan_id:
                event_data["old_plan_id"] = old_plan_id
                event_data["new_plan_id"] = subscription.plan_id
                if plan:
                    event_data["new_plan_name"] = plan.name
                    event_data["new_plan_price"] = plan.price
                    event_data["new_plan_currency"] = plan.currency
            
            # 触发订阅更新事件
            dispatch(event_names.SUBSCRIPTION_UPDATED, **event_data)
            
            self.logger.info(f"订阅更新成功: subscription_id={params.subscription_id}, 更新字段={list(update_data.keys())}")
            return self.create_success_result(response)
            
        except Exception as e:
            self.logger.error(f"更新订阅失败: subscription_id={params.subscription_id}, 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                error_code="UPDATE_FAILED",
                error_message=f"更新订阅失败: {str(e)}"
            )
    
    async def cancel_subscription(self, params: CancelSubscriptionParams) -> Result:
        """取消订阅
        
        Args:
            params: 取消订阅参数
            
        Returns:
            订阅结果对象
        """
        self.logger.info(f"开始取消订阅: subscription_id={params.subscription_id}")
        
        try:
            # 获取订阅
            subscription = await self.get_resource_by_id(params.subscription_id)
            if not subscription:
                return self.resource_not_found_result(params.subscription_id)
            
            # 验证用户权限
            if subscription.user_id != params.user_id:
                return self.permission_denied_result(params.subscription_id)
            
            # 检查订阅状态
            if subscription.status not in ["active", "past_due"]:
                return self.create_error_result(
                    error_code=ErrorCode.OPERATION_FAILED,
                    error_message=f"无法取消状态为 {subscription.status} 的订阅"
                )
            
            # 更新订阅状态
            meta_data = {"cancel_reason": params.reason} if params.reason else None
            subscription = await self.subscription_repo.update_status(
                self.db,
                params.subscription_id,
                status="canceled" if not params.cancel_at_period_end else "active",
                meta_data=meta_data
            )
            
            # 获取关联的计划
            plan = await self.subscription_plan_repo.get_by_id(self.db, subscription.plan_id)
            
            # 构建响应
            response = SubscriptionResponse.model_validate(subscription)
            if plan:
                response.plan = SubscriptionPlanResponse.model_validate(plan)
            
            # 更新缓存
            await self._cache_subscription(subscription.id, response)
            
            # 触发事件
            event_data = {
                "subscription_id": subscription.id,
                "user_id": subscription.user_id,
                "plan_id": subscription.plan_id,
                "status": subscription.status,
                "cancel_at_period_end": params.cancel_at_period_end,
                "reason": params.reason
            }
            dispatch(event_names.SUBSCRIPTION_CANCELED, **event_data)
            
            self.logger.info(f"订阅已取消: subscription_id={subscription.id}")
            return self.create_success_result(response)
            
        except Exception as e:
            self.logger.error(f"取消订阅失败: {str(e)}", exc_info=True)
            return self.create_error_result(
                error_code="CANCEL_FAILED",
                error_message=f"取消订阅失败: {str(e)}"
            )
    
    async def reactivate_subscription(self, params: ReactivateSubscriptionParams) -> Result[SubscriptionResponse]:
        """重新激活订阅
        
        Args:
            params: 重新激活订阅参数
            
        Returns:
            订阅结果对象
        """
        self.logger.info(f"开始重新激活订阅: subscription_id={params.subscription_id}, user_id={params.user_id}")
        
        try:
            # 获取订阅
            subscription = await self.get_resource_by_id(params.subscription_id)
            if not subscription:
                self.logger.warning(f"订阅不存在: subscription_id={params.subscription_id}")
                return self.resource_not_found_result(params.subscription_id, "SUBSCRIPTION_NOT_FOUND")
            
            # 验证用户权限
            if subscription.user_id != params.user_id:
                self.logger.warning(f"用户权限不足: 期望user_id={subscription.user_id}, 实际user_id={params.user_id}")
                return self.permission_denied_result(params.subscription_id)
            
            # 检查订阅状态
            if subscription.status != "canceled":
                self.logger.warning(f"订阅状态不允许重新激活: status={subscription.status}")
                return self.create_error_result(
                    error_code="INVALID_STATUS",
                    error_message="只能重新激活已取消状态的订阅"
                )
            
            # 检查是否有其他活跃订阅
            active_subscription = await self.subscription_repo.get_active_by_user_id(self.db, params.user_id)
            if active_subscription:
                self.logger.warning(f"用户已有活跃订阅: user_id={params.user_id}, 已有订阅ID={active_subscription.id}")
                return self.create_error_result(
                    error_code="ACTIVE_SUBSCRIPTION_EXISTS",
                    error_message="用户已有活跃订阅"
                )
            
            # 记录重新激活前的状态
            old_status = subscription.status
            
            # 执行重新激活操作
            self.logger.info(f"执行订阅重新激活操作: subscription_id={subscription.id}")
            
            # 使用仓库方法更新状态为活跃
            now = get_utc_now_without_tzinfo()
            update_data = {
                "status": "active",
                "current_period_start": now,
                "current_period_end": now + timedelta(days=30)  # 设置默认30天的订阅周期
            }
            
            subscription = await self.subscription_repo.update(
                self.db,
                db_obj=subscription,
                data=update_data
            )
            
            if not subscription:
                return self.create_error_result(
                    error_code="REACTIVATE_FAILED",
                    error_message="重新激活订阅失败，无法更新状态"
                )
            
            # 获取关联的计划
            plan = await self.subscription_plan_repo.get_by_id(self.db, subscription.plan_id)
            
            # 构建响应
            response = SubscriptionResponse.model_validate(subscription)
            if plan:
                response.plan = SubscriptionPlanResponse.model_validate(plan)
            
            # 更新缓存
            await self._cache_subscription(subscription.id, response)
            
            # 构建事件数据
            event_data = {
                "subscription_id": subscription.id,
                "user_id": subscription.user_id,
                "plan_id": subscription.plan_id,
                "old_status": old_status,
                "new_status": subscription.status,
                "current_period_start": subscription.current_period_start.isoformat() if subscription.current_period_start else None,
                "current_period_end": subscription.current_period_end.isoformat() if subscription.current_period_end else None
            }
            
            # 添加计划信息
            if plan:
                event_data.update({
                    "plan_name": plan.name,
                    "plan_price": plan.price,
                    "plan_currency": plan.currency
                })
            
            # 触发订阅重新激活事件
            dispatch(event_names.SUBSCRIPTION_REACTIVATED, **event_data)
            
            self.logger.info(f"订阅已重新激活: subscription_id={subscription.id}")
            return self.create_success_result(response)
            
        except Exception as e:
            self.logger.error(f"重新激活订阅失败: subscription_id={params.subscription_id}, 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                error_code="REACTIVATE_FAILED",
                error_message=f"重新激活订阅失败: {str(e)}"
            )
    
    async def renew_subscription(self, params: RenewSubscriptionParams) -> Result[SubscriptionResponse]:
        """续订订阅，开始新的计费周期
        
        Args:
            params: 续订订阅参数
            
        Returns:
            订阅结果对象
        """
        self.logger.info(f"开始续订订阅: subscription_id={params.subscription_id}, user_id={params.user_id}")
        
        try:
            # 获取订阅
            subscription = await self.get_resource_by_id(params.subscription_id)
            if not subscription:
                self.logger.warning(f"订阅不存在: subscription_id={params.subscription_id}")
                return self.resource_not_found_result(params.subscription_id, "SUBSCRIPTION_NOT_FOUND")
            
            # 验证用户权限
            if subscription.user_id != params.user_id:
                self.logger.warning(f"用户权限不足: 期望user_id={subscription.user_id}, 实际user_id={params.user_id}")
                return self.permission_denied_result(params.subscription_id)
            
            # 检查订阅状态
            if subscription.status not in ["active", "past_due"]:
                self.logger.warning(f"订阅状态不允许续订: status={subscription.status}")
                return self.create_error_result(
                    error_code="INVALID_STATUS",
                    error_message=f"无法续订状态为 {subscription.status} 的订阅"
                )
            
            # 记录续订前的状态和周期
            old_status = subscription.status
            old_period_end = subscription.current_period_end
            
            # 执行续订操作
            self.logger.info(f"执行订阅续订操作: subscription_id={subscription.id}")
            await subscription.renew(self.db)
            
            # 获取关联的计划
            plan = await self.subscription_plan_repo.get_by_id(self.db, subscription.plan_id)
            
            # 构建响应
            response = SubscriptionResponse.model_validate(subscription)
            if plan:
                response.plan = SubscriptionPlanResponse.model_validate(plan)
            
            # 更新缓存
            await self._cache_subscription(subscription.id, response)
            
            # 构建事件数据
            event_data = {
                "subscription_id": subscription.id,
                "user_id": subscription.user_id,
                "plan_id": subscription.plan_id,
                "old_status": old_status,
                "new_status": subscription.status,
                "old_period_end": old_period_end.isoformat() if old_period_end else None,
                "new_period_start": subscription.current_period_start.isoformat() if subscription.current_period_start else None,
                "new_period_end": subscription.current_period_end.isoformat() if subscription.current_period_end else None
            }
            
            # 添加计划信息
            if plan:
                event_data.update({
                    "plan_name": plan.name,
                    "plan_price": plan.price,
                    "plan_currency": plan.currency
                })
            
            # 触发订阅续订事件
            dispatch(event_names.SUBSCRIPTION_RENEWED, **event_data)
            
            self.logger.info(f"订阅已续订: subscription_id={subscription.id}")
            return self.create_success_result(response)
            
        except Exception as e:
            self.logger.error(f"续订订阅失败: subscription_id={params.subscription_id}, 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                error_code="RENEW_FAILED",
                error_message=f"续订订阅失败: {str(e)}"
            )
    
    async def change_plan(self, params: ChangePlanParams) -> Result[SubscriptionResponse]:
        """更改订阅计划
        
        Args:
            params: 更改订阅计划参数
            
        Returns:
            订阅结果对象
        """
        self.logger.info(f"开始更改订阅计划: subscription_id={params.subscription_id}, user_id={params.user_id}, plan_id={params.plan_id}")
        
        try:
            # 获取订阅
            subscription = await self.get_resource_by_id(params.subscription_id)
            if not subscription:
                self.logger.warning(f"订阅不存在: subscription_id={params.subscription_id}")
                return self.resource_not_found_result(params.subscription_id, "SUBSCRIPTION_NOT_FOUND")
            
            # 验证用户权限
            if subscription.user_id != params.user_id:
                self.logger.warning(f"用户权限不足: 期望user_id={subscription.user_id}, 实际user_id={params.user_id}")
                return self.permission_denied_result(params.subscription_id)
            
            # 检查订阅状态
            if subscription.status != "active":
                self.logger.warning(f"订阅状态不允许更改计划: status={subscription.status}")
                return self.create_error_result(
                    error_code="INVALID_STATUS",
                    error_message="只能为活跃状态的订阅更改计划"
                )
            
            # 检查新计划是否存在
            new_plan = await self.subscription_plan_repo.get_by_id(self.db, params.plan_id)
            if not new_plan:
                self.logger.warning(f"订阅计划不存在: plan_id={params.plan_id}")
                return self.create_error_result(
                    error_code="PLAN_NOT_FOUND",
                    error_message="订阅计划不存在"
                )
            
            # 如果新计划与当前计划相同，则不执行更改
            if subscription.plan_id == params.plan_id:
                self.logger.info(f"订阅计划未发生变化: plan_id={params.plan_id}")
                
                # 构建响应
                response = SubscriptionResponse.model_validate(subscription)
                response.plan = SubscriptionPlanResponse.model_validate(new_plan)
                
                return self.create_success_result(response)
            
            # 获取当前计划
            old_plan = await self.subscription_plan_repo.get_by_id(self.db, subscription.plan_id)
            
            # 记录更改前的状态和计划
            old_plan_id = subscription.plan_id
            old_period_end = subscription.current_period_end
            
            # 执行计划更改操作
            self.logger.info(f"执行订阅计划更改操作: subscription_id={subscription.id}, old_plan_id={old_plan_id}, new_plan_id={params.plan_id}")
            await subscription.change_plan(self.db, params.plan_id)
            
            # 构建响应
            response = SubscriptionResponse.model_validate(subscription)
            response.plan = SubscriptionPlanResponse.model_validate(new_plan)
            
            # 更新缓存
            await self._cache_subscription(subscription.id, response)
            
            # 构建事件数据
            event_data = {
                "subscription_id": subscription.id,
                "user_id": subscription.user_id,
                "old_plan_id": old_plan_id,
                "new_plan_id": params.plan_id,
                "old_period_end": old_period_end.isoformat() if old_period_end else None,
                "new_period_start": subscription.current_period_start.isoformat() if subscription.current_period_start else None,
                "new_period_end": subscription.current_period_end.isoformat() if subscription.current_period_end else None
            }
            
            # 添加新旧计划信息
            if old_plan:
                event_data.update({
                    "old_plan_name": old_plan.name,
                    "old_plan_price": old_plan.price,
                    "old_plan_currency": old_plan.currency
                })
            
            event_data.update({
                "new_plan_name": new_plan.name,
                "new_plan_price": new_plan.price,
                "new_plan_currency": new_plan.currency
            })
            
            # 触发订阅计划更改事件
            dispatch(event_names.SUBSCRIPTION_PLAN_CHANGED, **event_data)
            
            self.logger.info(f"订阅计划已更改: subscription_id={subscription.id}")
            return self.create_success_result(response)
            
        except Exception as e:
            self.logger.error(f"更改订阅计划失败: subscription_id={params.subscription_id}, 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                error_code="CHANGE_PLAN_FAILED",
                error_message=f"更改订阅计划失败: {str(e)}"
            )
    
    async def pause_subscription(self, params: PauseSubscriptionParams) -> Result[SubscriptionResponse]:
        """暂停订阅
        
        Args:
            params: 暂停订阅的参数
            
        Returns:
            订阅结果对象
        """
        self.logger.info(f"开始暂停订阅: subscription_id={params.subscription_id}, user_id={params.user_id}")
        
        try:
            # 获取订阅
            subscription = await self.get_resource_by_id(params.subscription_id)
            if not subscription:
                self.logger.warning(f"订阅不存在: subscription_id={params.subscription_id}")
                return self.resource_not_found_result(params.subscription_id, "SUBSCRIPTION_NOT_FOUND")
            
            # 验证用户权限
            if subscription.user_id != params.user_id:
                self.logger.warning(f"用户权限不足: 期望user_id={subscription.user_id}, 实际user_id={params.user_id}")
                return self.permission_denied_result(params.subscription_id)
            
            # 检查订阅状态
            if subscription.status != "active":
                self.logger.warning(f"订阅状态不允许暂停: status={subscription.status}")
                return self.create_error_result(
                    error_code="INVALID_STATUS",
                    error_message=f"无法暂停状态为 {subscription.status} 的订阅"
                )
            
            self.logger.info(f"执行订阅暂停操作: subscription_id={subscription.id}")
            
            # 使用仓库方法执行暂停
            meta_data = {"resume_at": params.resume_at.isoformat() if params.resume_at else None}
            subscription = await self.subscription_repo.update_status(
                self.db, 
                subscription.id, 
                "paused", 
                meta_data
            )
            
            if not subscription:
                return self.create_error_result(
                    error_code="PAUSE_FAILED",
                    error_message="订阅暂停失败，无法更新状态"
                )
            
            # 触发订阅暂停事件
            dispatch(event_names.SUBSCRIPTION_PAUSED, payload={
                "subscription_id": subscription.id,
                "user_id": subscription.user_id,
                "plan_id": subscription.plan_id,
                "status": subscription.status,
                "resume_at": params.resume_at.isoformat() if params.resume_at else None
            })
            
            self.logger.info(f"订阅已暂停: subscription_id={subscription.id}")
            
            # 加载关联的计划
            plan = await self.subscription_plan_repo.get_by_id(self.db, subscription.plan_id)
            
            # 构建响应
            response = SubscriptionResponse.model_validate(subscription)
            if plan:
                response.plan = SubscriptionPlanResponse.model_validate(plan)
            
            # 更新缓存
            await self._cache_subscription(subscription.id, response)
            
            return self.create_success_result(response)
        except Exception as e:
            self.logger.error(f"订阅暂停失败: subscription_id={params.subscription_id}, 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                error_code="PAUSE_FAILED",
                error_message=f"暂停订阅失败: {str(e)}"
            )
    
    async def resume_subscription(self, params: ResumeSubscriptionParams) -> Result[SubscriptionResponse]:
        """恢复暂停的订阅
        
        Args:
            params: 恢复订阅参数
            
        Returns:
            订阅结果对象
        """
        self.logger.info(f"开始恢复订阅: subscription_id={params.subscription_id}, user_id={params.user_id}")
        
        # 获取订阅
        subscription = await self.get_resource_by_id(params.subscription_id)
        if not subscription:
            self.logger.warning(f"订阅不存在: subscription_id={params.subscription_id}")
            return self.resource_not_found_result(params.subscription_id, "SUBSCRIPTION_NOT_FOUND")
        
        # 验证用户权限
        if subscription.user_id != params.user_id:
            self.logger.warning(f"用户权限不足: 期望user_id={subscription.user_id}, 实际user_id={params.user_id}")
            return self.permission_denied_result(params.subscription_id)
        
        # 检查订阅状态
        if subscription.status != "paused":
            self.logger.warning(f"订阅状态不允许恢复: status={subscription.status}")
            return self.create_error_result(
                error_code="INVALID_STATUS",
                error_message=f"无法恢复状态为 {subscription.status} 的订阅"
            )
        
        try:
            self.logger.info(f"执行订阅恢复操作: subscription_id={subscription.id}")
            
            # 使用仓库方法执行恢复
            subscription = await self.subscription_repo.update_status(self.db, subscription.id, "active")
            if not subscription:
                return self.create_error_result(
                    error_code="RESUME_FAILED",
                    error_message="订阅恢复失败，无法更新状态"
                )
            
            # 触发订阅恢复事件
            dispatch(event_names.SUBSCRIPTION_RESUMED, payload={
                "subscription_id": subscription.id,
                "user_id": subscription.user_id,
                "plan_id": subscription.plan_id,
                "status": subscription.status
            })
            
            self.logger.info(f"订阅已恢复: subscription_id={subscription.id}")
            
            # 加载关联的计划
            plan = await self.subscription_plan_repo.get_by_id(self.db, subscription.plan_id)
            
            # 构建响应
            response = SubscriptionResponse.model_validate(subscription)
            if plan:
                response.plan = SubscriptionPlanResponse.model_validate(plan)
            
            # 更新缓存
            await self._cache_subscription(subscription.id, response)
            
            return self.create_success_result(response)
        except Exception as e:
            self.logger.error(f"订阅恢复失败: subscription_id={subscription.id}, 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                error_code="RESUME_FAILED",
                error_message=f"订阅恢复失败: {str(e)}"
            )
    
    async def upgrade_subscription(self, params: UpgradeSubscriptionParams) -> Result[SubscriptionResponse]:
        """升级订阅到更高级的计划
        
        Args:
            params: 升级订阅参数
            
        Returns:
            订阅结果对象
        """
        self.logger.info(f"开始升级订阅: subscription_id={params.subscription_id}, user_id={params.user_id}, plan_id={params.plan_id}")
        
        try:
            # 获取订阅
            subscription = await self.get_resource_by_id(params.subscription_id)
            if not subscription:
                self.logger.warning(f"订阅不存在: subscription_id={params.subscription_id}")
                return self.resource_not_found_result(params.subscription_id, "SUBSCRIPTION_NOT_FOUND")
            
            # 验证用户权限
            if subscription.user_id != params.user_id:
                self.logger.warning(f"用户权限不足: 期望user_id={subscription.user_id}, 实际user_id={params.user_id}")
                return self.permission_denied_result(params.subscription_id)
            
            # 检查订阅状态
            valid_status = ["active", "trialing", "past_due"]
            if subscription.status not in valid_status:
                self.logger.warning(f"订阅状态不允许升级: status={subscription.status}")
                return self.create_error_result(
                    error_code="INVALID_STATUS",
                    error_message=f"只有状态为 {', '.join(valid_status)} 的订阅可以升级"
                )
            
            # 获取当前计划
            current_plan = await self.subscription_plan_repo.get_by_id(self.db, subscription.plan_id)
            if not current_plan:
                self.logger.warning(f"当前订阅计划不存在: plan_id={subscription.plan_id}")
                return self.create_error_result(
                    error_code="PLAN_NOT_FOUND",
                    error_message="当前订阅计划不存在"
                )
            
            # 获取目标计划
            new_plan = await self.subscription_plan_repo.get_by_id(self.db, params.plan_id)
            if not new_plan:
                self.logger.warning(f"目标订阅计划不存在: plan_id={params.plan_id}")
                return self.create_error_result(
                    error_code=ErrorCode.PLAN_NOT_FOUND,
                    error_message="目标订阅计划不存在"
                )
            
            # 检查是否为升级（价格更高）
            if new_plan.price <= current_plan.price:
                self.logger.warning(f"非升级操作: current_price={current_plan.price}, new_price={new_plan.price}")
                return self.create_error_result(
                    error_code=ErrorCode.OPERATION_FAILED,
                    error_message="新计划价格必须高于当前计划"
                )
            
            # 记录更改前的计划ID和结束时间
            old_plan_id = subscription.plan_id
            old_period_end = subscription.current_period_end
            old_status = subscription.status
            
            # 准备更新数据
            now = get_utc_now_without_tzinfo()
            update_data = {
                "plan_id": params.plan_id,
                "status": "active",  # 升级后直接激活
                "current_period_start": now
            }
            
            # 计算新的结束时间
            if new_plan.interval == "month":
                update_data["current_period_end"] = now + timedelta(days=30 * new_plan.interval_count)
            elif new_plan.interval == "year":
                update_data["current_period_end"] = now + timedelta(days=365 * new_plan.interval_count)
            else:
                update_data["current_period_end"] = now + timedelta(days=30)  # 默认30天
            
            # 清除试用期信息（高级订阅不需要试用）
            update_data["trial_start"] = None
            update_data["trial_end"] = None
            
            # 更新订阅计划
            subscription = await self.subscription_repo.update(
                self.db,
                db_obj=subscription,
                data=update_data
            )
            
            if not subscription:
                return self.create_error_result(
                    error_code="UPGRADE_FAILED",
                    error_message="升级订阅失败，无法更新订阅"
                )
            
            # 构建响应
            response = SubscriptionResponse.model_validate(subscription)
            response.plan = SubscriptionPlanResponse.model_validate(new_plan)
            
            # 更新缓存
            await self._cache_subscription(subscription.id, response)
            
            # 构建事件数据
            event_data = {
                "subscription_id": subscription.id,
                "user_id": subscription.user_id,
                "old_plan_id": old_plan_id,
                "new_plan_id": params.plan_id,
                "old_status": old_status,
                "new_status": subscription.status,
                "old_period_end": old_period_end.isoformat() if old_period_end else None,
                "current_period_start": subscription.current_period_start.isoformat() if subscription.current_period_start else None,
                "current_period_end": subscription.current_period_end.isoformat() if subscription.current_period_end else None,
                "old_plan_name": current_plan.name,
                "old_plan_price": current_plan.price,
                "new_plan_name": new_plan.name,
                "new_plan_price": new_plan.price
            }
            
            # 触发订阅升级事件
            dispatch(event_names.SUBSCRIPTION_UPGRADED, **event_data)
            
            self.logger.info(f"订阅已升级: subscription_id={subscription.id}")
            return self.create_success_result(response)
            
        except Exception as e:
            self.logger.error(f"升级订阅失败: subscription_id={params.subscription_id}, 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.OPERATION_FAILED,
                error_message=f"升级订阅失败: {str(e)}"
            )
    
    def _get_subscription_cache_key(self, subscription_id: int) -> str:
        """获取订阅缓存键
        
        Args:
            subscription_id: 订阅ID
            
        Returns:
            str: 缓存键
        """
        return f"{self.resource_type}:{subscription_id}"
    
    def _get_active_subscription_cache_key(self, user_id: int) -> str:
        """获取用户活跃订阅缓存键
        
        Args:
            user_id: 用户ID
            
        Returns:
            str: 缓存键
        """
        return f"{self.resource_type}:user:{user_id}:active"
    
    def _get_user_subscriptions_cache_key(self, user_id: int) -> str:
        """获取用户订阅列表缓存键
        
        Args:
            user_id: 用户ID
            
        Returns:
            str: 缓存键
        """
        return f"{self.resource_type}:user:{user_id}:list"
    
    def _get_user_subscriptions_list_cache_key(self, user_id: int, skip: int, limit: int) -> str:
        """获取用户订阅分页列表缓存键
        
        Args:
            user_id: 用户ID
            skip: 跳过记录数
            limit: 返回记录数
            
        Returns:
            str: 缓存键
        """
        return f"{self.resource_type}:user:{user_id}:list:{skip}:{limit}"
    
    async def _cache_subscription(self, subscription_id: int, response: SubscriptionResponse) -> None:
        """缓存订阅信息
        
        Args:
            subscription_id: 订阅ID
            response: 订阅响应对象
        """
        if not self.redis:
            self.logger.debug(f"Redis未配置，跳过缓存订阅: subscription_id={subscription_id}")
            return
            
        try:
            # 缓存订阅数据
            key = self._get_subscription_cache_key(subscription_id)
            await self.cache_resource(key, response, CACHE_TTL)
            self.logger.debug(f"订阅缓存成功: key={key}")
            
            # 缓存活跃订阅
            if response.status == "active":
                user_key = self._get_active_subscription_cache_key(response.user_id)
                await self.cache_resource(user_key, response, CACHE_TTL)
                self.logger.debug(f"活跃订阅缓存成功: key={user_key}")
        except Exception as e:
            # 缓存失败不应影响主要业务逻辑
            self.logger.warning(f"缓存订阅失败: subscription_id={subscription_id}, 错误={str(e)}")
    
    async def _get_cached_subscription(self, subscription_id: int) -> Optional[SubscriptionResponse]:
        """从缓存获取订阅信息
        
        Args:
            subscription_id: 订阅ID
            
        Returns:
            订阅响应对象，如果不存在则返回None
        """
        if not self.redis:
            return None
            
        try:
            key = self._get_subscription_cache_key(subscription_id)
            return await self.get_cached_resource(
                key,
                lambda data: SubscriptionResponse.model_validate(data)
            )
        except Exception as e:
            # 获取缓存失败不应影响主要业务逻辑
            self.logger.warning(f"从缓存获取订阅失败: subscription_id={subscription_id}, 错误={str(e)}")
        
        return None
    
    async def _clear_active_subscription_cache(self, user_id: int) -> None:
        """清除用户活跃订阅缓存
        
        Args:
            user_id: 用户ID
        """
        if not self.redis:
            return
            
        try:
            user_key = self._get_active_subscription_cache_key(user_id)
            await self.delete_cache(user_key)
            self.logger.debug(f"清除活跃订阅缓存: key={user_key}")
        except Exception as e:
            # 清除缓存失败不应影响主要业务逻辑
            self.logger.warning(f"清除活跃订阅缓存失败: user_id={user_id}, 错误={str(e)}")

    async def get_user_subscriptions(self, user_id: int) -> UserSubscriptionsResult:
        """获取用户的所有订阅
        
        Args:
            user_id: 用户ID
            
        Returns:
            UserSubscriptionsResult: 包含用户所有订阅的结果对象
        """
        self.logger.info(f"获取用户的所有订阅: user_id={user_id}")
        
        try:
            # 检查缓存
            cache_key = self._get_user_subscriptions_cache_key(user_id)
            if self.redis:
                cached_data = await self.get_cached_list(cache_key)
                if cached_data:
                    self.logger.debug(f"从缓存获取用户订阅列表: key={cache_key}")
                    responses = [SubscriptionResponse.model_validate(item) for item in cached_data]
                    return UserSubscriptionsResult(
                        is_success=True,
                        subscriptions=responses
                    )
            
            # 从数据库获取订阅
            total, subscriptions = await self.subscription_repo.get_by_user_id(
                self.db,
                user_id=user_id,
                skip=0,
                limit=100  # 获取所有订阅，上限设置为100
            )
            
            if not subscriptions:
                self.logger.info(f"用户没有订阅: user_id={user_id}")
                return UserSubscriptionsResult(
                    is_success=True,
                    subscriptions=[]
                )
            
            # 获取计划ID集合
            plan_ids = [s.plan_id for s in subscriptions if s.plan_id]
            
            # 获取所有相关的计划
            plans = {}
            if plan_ids:
                plans_list = await self.subscription_plan_repo.get_by_ids(self.db, plan_ids)
                plans = {p.id: p for p in plans_list}
            
            # 构建响应
            responses = []
            for subscription in subscriptions:
                response = SubscriptionResponse.model_validate(subscription)
                if subscription.plan_id in plans:
                    response.plan = SubscriptionPlanResponse.model_validate(plans[subscription.plan_id])
                responses.append(response)
            
            # 缓存结果
            if self.redis:
                await self.cache_resource(cache_key, [s.model_dump() for s in responses], CACHE_TTL)
                self.logger.debug(f"用户订阅列表缓存成功: key={cache_key}")
            
            return UserSubscriptionsResult(
                is_success=True,
                subscriptions=responses
            )
        except Exception as e:
            self.logger.error(f"获取用户订阅失败: user_id={user_id}, 错误={str(e)}", exc_info=True)
            return UserSubscriptionsResult(
                is_success=False,
                error_code="FETCH_FAILED",
                error_message=f"获取用户订阅失败: {str(e)}"
            ) 