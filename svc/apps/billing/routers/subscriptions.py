"""
订阅管理API路由
包含订阅的创建、查询、更新、取消和续订功能
"""
from typing import Any, List, Optional, Dict

from fastapi import APIRouter, Depends, HTTPException, Query, Path, status


from svc.core.models.result import Result, ResultFactory
from svc.apps.auth.dependencies import get_current_active_user, has_permission, resource_permission
from svc.apps.auth.models.user import User
from svc.apps.billing.schemas.subscription import (
    SubscriptionCreate,
    SubscriptionUpdate,
    GetSubscriptionParams,
    GetSubscriptionsParams,
    CreateSubscriptionParams,
    UpdateSubscriptionParams,
    CancelSubscriptionParams,
    RenewSubscriptionParams,
    PauseSubscriptionParams,
    ResumeSubscriptionParams,
    UpgradeSubscriptionParams,
    SubscriptionResponse
)
from svc.apps.billing.services.subscription import SubscriptionService
from svc.apps.billing.dependencies import get_subscription_service
from svc.core.exceptions.route_error_handler import handle_route_errors, SUBSCRIPTION_ERROR_MAPPING
from svc.core.schemas.base import PageParams


router = APIRouter(tags=["订阅"])


@router.get("/mine", response_model=Result[Dict[str, Any]])
@handle_route_errors(SUBSCRIPTION_ERROR_MAPPING)
async def get_my_subscriptions(
    status: Optional[str] = Query(None, description="订阅状态过滤", alias="status"),
    params: PageParams = Depends(),
    subscription_service: SubscriptionService = Depends(get_subscription_service),
    current_user: User = Depends(get_current_active_user),
) -> Result:
    """获取当前用户的订阅列表"""
    params_obj = GetSubscriptionsParams(
        user_id=current_user.id,
        status=status,
        page_num=params.page_num,
        page_size=params.page_size
    )
    result = await subscription_service.get_subscriptions(params_obj)
    return result


@router.get("/mine/active", response_model=Result[Dict[str, Any]])
@handle_route_errors(SUBSCRIPTION_ERROR_MAPPING)
async def get_my_active_subscriptions(
    params: PageParams = Depends(),
    subscription_service: SubscriptionService = Depends(get_subscription_service),
    current_user: User = Depends(get_current_active_user),
) -> Result:
    """获取当前用户的活跃订阅列表"""
    params_obj = GetSubscriptionsParams(
        user_id=current_user.id,
        status="active",
        page_num=params.page_num,
        page_size=params.page_size
    )
    result = await subscription_service.get_subscriptions(params_obj)
    return result


@router.get("/details/{subscription_id}", response_model=Result[Dict[str, Any]])
@handle_route_errors(SUBSCRIPTION_ERROR_MAPPING)
async def get_my_subscription_details(
    subscription_id: int = Path(..., description="订阅ID", alias="subscriptionId"),
    subscription_service: SubscriptionService = Depends(get_subscription_service),
    current_user: User = Depends(get_current_active_user),
) -> Result:
    """获取当前用户的特定订阅详情"""
    params = GetSubscriptionParams(subscription_id=subscription_id, user_id=current_user.id)
    result = await subscription_service.get_subscription(params)
    return result


@router.post("/create", response_model=Result[Dict[str, Any]])
@handle_route_errors(SUBSCRIPTION_ERROR_MAPPING)
async def create_my_subscription(
    subscription_data: SubscriptionCreate,
    subscription_service: SubscriptionService = Depends(get_subscription_service),
    current_user: User = Depends(get_current_active_user),
) -> Result:
    """为当前用户创建新订阅"""
    if hasattr(subscription_data, 'user_id') and subscription_data.user_id != current_user.id:
         return ResultFactory.permission_denied("不能为其他用户创建订阅")
    params = CreateSubscriptionParams(subscription_data=subscription_data, user_id=current_user.id)
    result = await subscription_service.create_subscription(params)
    return result


@router.post("/cancel/{subscription_id}", response_model=Result[Dict[str, Any]])
@handle_route_errors(SUBSCRIPTION_ERROR_MAPPING)
async def cancel_my_subscription(
    subscription_id: int = Path(..., description="订阅ID", alias="subscriptionId"),
    subscription_service: SubscriptionService = Depends(get_subscription_service),
    current_user: User = Depends(get_current_active_user),
) -> Result:
    """取消当前用户的特定订阅"""
    params = CancelSubscriptionParams(subscription_id=subscription_id, user_id=current_user.id)
    result = await subscription_service.cancel_subscription(params)
    return result


@router.post("/renew/{subscription_id}", response_model=Result[Dict[str, Any]])
@handle_route_errors(SUBSCRIPTION_ERROR_MAPPING)
async def renew_my_subscription(
    subscription_id: int = Path(..., description="订阅ID", alias="subscriptionId"),
    subscription_service: SubscriptionService = Depends(get_subscription_service),
    current_user: User = Depends(get_current_active_user),
) -> Result:
    """续订当前用户的特定订阅"""
    params = RenewSubscriptionParams(subscription_id=subscription_id, user_id=current_user.id)
    result = await subscription_service.renew_subscription(params)
    return result


@router.post("/pause/{subscription_id}", response_model=Result[Dict[str, Any]])
@handle_route_errors(SUBSCRIPTION_ERROR_MAPPING)
async def pause_my_subscription(
    subscription_id: int = Path(..., description="订阅ID", alias="subscriptionId"),
    subscription_service: SubscriptionService = Depends(get_subscription_service),
    current_user: User = Depends(get_current_active_user),
) -> Result:
    """暂停当前用户的特定订阅"""
    params = PauseSubscriptionParams(subscription_id=subscription_id, user_id=current_user.id)
    result = await subscription_service.pause_subscription(params)
    return result


@router.post("/resume/{subscription_id}", response_model=Result[Dict[str, Any]])
@handle_route_errors(SUBSCRIPTION_ERROR_MAPPING)
async def resume_my_subscription(
    subscription_id: int = Path(..., description="订阅ID", alias="subscriptionId"),
    subscription_service: SubscriptionService = Depends(get_subscription_service),
    current_user: User = Depends(get_current_active_user),
) -> Result:
    """恢复当前用户的特定订阅"""
    params = ResumeSubscriptionParams(subscription_id=subscription_id, user_id=current_user.id)
    result = await subscription_service.resume_subscription(params)
    return result


@router.post("/upgrade/{subscription_id}", response_model=Result[Dict[str, Any]])
@handle_route_errors(SUBSCRIPTION_ERROR_MAPPING)
async def upgrade_my_subscription(
    plan_id: int = Query(..., description="目标订阅计划ID", alias="planId"),
    subscription_id: int = Path(..., description="订阅ID", alias="subscriptionId"),
    subscription_service: SubscriptionService = Depends(get_subscription_service),
    current_user: User = Depends(get_current_active_user),
) -> Result:
    """升级当前用户的特定订阅"""
    params = UpgradeSubscriptionParams(subscription_id=subscription_id, user_id=current_user.id, new_plan_id=plan_id)
    result = await subscription_service.upgrade_subscription(params)
    return result


@router.get("/admin/list", response_model=Result[Dict[str, Any]])
@handle_route_errors(SUBSCRIPTION_ERROR_MAPPING)
async def admin_list_subscriptions(
    user_id: Optional[int] = Query(None, description="按用户ID过滤", alias="userId"),
    status: Optional[str] = Query(None, description="按订阅状态过滤", alias="status"),
    params: PageParams = Depends(),
    subscription_service: SubscriptionService = Depends(get_subscription_service),
    current_user: User = Depends(get_current_active_user),
    _: bool = Depends(lambda: has_permission("subscription:read"))
) -> Result:
    """获取订阅列表 (管理端)"""
    params_obj = GetSubscriptionsParams(
        user_id=user_id,
        status=status,
        page_num=params.page_num,
        page_size=params.page_size
    )
    result = await subscription_service.get_subscriptions(params_obj)
    return result


@router.get("/admin/details/{subscription_id}", response_model=Result[Dict[str, Any]])
@handle_route_errors(SUBSCRIPTION_ERROR_MAPPING)
async def admin_get_subscription_details(
    subscription_id: int = Path(..., description="订阅ID", alias="subscriptionId"),
    subscription_service: SubscriptionService = Depends(get_subscription_service),
    current_user: User = Depends(get_current_active_user),
    _: Any = Depends(resource_permission("subscription", "read"))
) -> Result:
    """获取任意订阅详情 (管理端)"""
    params = GetSubscriptionParams(subscription_id=subscription_id, user_id=None)
    result = await subscription_service.get_subscription(params)
    return result


@router.put("/admin/{subscription_id}", response_model=Result[Dict[str, Any]])
@handle_route_errors(SUBSCRIPTION_ERROR_MAPPING)
async def admin_update_subscription(
    subscription_update: SubscriptionUpdate,
    subscription_id: int = Path(..., description="订阅ID", alias="subscriptionId"),
    subscription_service: SubscriptionService = Depends(get_subscription_service),
    current_user: User = Depends(get_current_active_user),
    _: Any = Depends(resource_permission("subscription", "update"))
) -> Result:
    """更新任意订阅信息 (管理端)"""
    params = UpdateSubscriptionParams(
        subscription_id=subscription_id,
        subscription_data=subscription_update
    )
    result = await subscription_service.update_subscription(params)
    return result