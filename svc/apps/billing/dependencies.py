"""
依赖注入模块
提供结算服务的依赖项和配置函数。
"""
from fastapi import Depends
from redis import Redis
from sqlalchemy.ext.asyncio import AsyncSession

from svc.apps.billing.repositories import (InvoiceRepository,
                                           PaymentRepository,
                                           SubscriptionPlanRepository,
                                           SubscriptionRepository)
from svc.apps.billing.services.invoice import InvoiceService
from svc.apps.billing.services.payment import PaymentService
from svc.apps.billing.services.subscription import SubscriptionService
from svc.apps.billing.services.subscription_plan import SubscriptionPlanService
from svc.core.cache.redis import get_redis
from svc.core.database import get_session_for_route
from svc.core.dependencies.auth import configure_auth_dependencies


# 可选：如果有资源类型需要注册到权限系统
def setup_billing_dependencies():
    """
    设置结算依赖项
    
    注册结算相关的资源类型并配置全局认证依赖项。
    应在应用启动时调用此函数。
    """
    try:
        # 如果有模型需要注册到权限系统，可以导入并添加到这里
        from svc.apps.billing.models import (Invoice, Payment, Subscription,
                                             SubscriptionPlan)

        # 配置全局认证依赖项
        configure_auth_dependencies(
            resources={
                # 根据需要添加资源类型
                "subscription_plan": SubscriptionPlan,
                "subscription": Subscription,
                "invoice": Invoice,
                "payment": Payment
            }
        )
    except ImportError:
        # 如果模型未定义或导入失败，记录日志但不中断程序
        import logging
        logger = logging.getLogger(__name__)
        logger.warning("未能注册结算模块资源类型，请检查模型是否正确定义")


async def get_subscription_repository(
    db: AsyncSession = Depends(get_session_for_route)
) -> SubscriptionRepository:
    """
    提供SubscriptionRepository实例的依赖
    
    Args:
        db: 数据库会话
        
    Returns:
        SubscriptionRepository: 订阅仓库实例
    """
    return SubscriptionRepository(db)


async def get_subscription_plan_repository(
    db: AsyncSession = Depends(get_session_for_route)
) -> SubscriptionRepository:
    """
    提供SubscriptionRepository实例的依赖
    
    Args:
        db: 数据库会话
        
    Returns:
        SubscriptionRepository: 订阅仓库实例
    """
    return SubscriptionPlanRepository(db)


async def get_invoice_repository(
        db: AsyncSession = Depends(get_session_for_route)
) -> InvoiceRepository:
    """
    提供BillingRepository实例的依赖
    
    Returns:
        BillingRepository: 计费模块统一仓库实例
    """
    return InvoiceRepository(db)

async def get_payment_repository(
    db: AsyncSession = Depends(get_session_for_route)
) -> PaymentRepository:
    """
    提供PaymentRepository实例的依赖

    Args:
        db: 数据库会话

    Returns:
        PaymentRepository: 支付仓库实例
    """
    return PaymentRepository(db)

async def get_subscription_plan_service(
    redis: Redis = Depends(get_redis),
    plan_repo:SubscriptionPlanRepository=Depends(get_subscription_plan_repository)
) -> SubscriptionPlanService:
    """
    提供SubscriptionPlanService实例的依赖
    
    Args:
        db: 数据库会话
        redis: Redis客户端
        
    Returns:
        SubscriptionPlanService: 订阅计划服务实例
    """
    return SubscriptionPlanService(redis,plan_repo=plan_repo)


async def get_subscription_service(
    redis: Redis = Depends(get_redis),
    subscription_repo: SubscriptionRepository = Depends(get_subscription_repository),
    plan_repo:SubscriptionPlanRepository=Depends(get_subscription_plan_repository)
) -> SubscriptionService:
    """
    提供SubscriptionService实例的依赖
    
    Args:
        db: 数据库会话
        redis: Redis客户端
        subscription_repo: 订阅仓库实例
        
    Returns:
        SubscriptionService: 订阅服务实例
    """
    service = SubscriptionService(redis,subscription_repo=subscription_repo,subscription_plan_repo=plan_repo)
    return service


async def get_invoice_service(
    redis: Redis = Depends(get_redis),
    invoice_repo=Depends(get_invoice_repository)
) -> InvoiceService:
    """
    提供InvoiceService实例的依赖
    
    Args:
        db: 数据库会话
        redis: Redis客户端
        
    Returns:
        InvoiceService: 发票服务实例
    """
    return InvoiceService(redis=redis,repository=invoice_repo)


async def get_payment_service(
    redis: Redis = Depends(get_redis),
    payment_repo=Depends(get_payment_repository)
) -> PaymentService:
    """获取支付服务实例
    
    Args:
        db: 数据库会话
        redis: Redis客户端
        
    Returns:
        PaymentService: 支付服务实例
    """
    return PaymentService(repository=payment_repo, redis=redis)


# 在模块导入时自动调用设置函数
# 注意：如果应用启动顺序有特殊要求，可能需要在应用启动时显式调用此函数
try:
    setup_billing_dependencies()
except Exception as e:
    import logging
    logger = logging.getLogger(__name__)
    logger.error(f"设置结算依赖项失败: {str(e)}") 