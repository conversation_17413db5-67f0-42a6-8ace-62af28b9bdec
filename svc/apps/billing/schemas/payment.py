from typing import Optional, Dict, Any, List
from datetime import datetime

from pydantic import BaseModel, Field, field_validator, ConfigDict
from svc.core.schemas.base import PaginatedResponse
from svc.core.models.base import CamelCaseModel


# 参数模型
class GetPaymentParams(CamelCaseModel):
    """获取支付记录参数"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "payment_id": 1,
                "user_id": None
            }
        }
    )
    
    payment_id: int = Field(description="支付记录ID")
    user_id: Optional[int] = Field(default=None, description="用户ID")


class GetPaymentsParams(BaseModel):
    """获取支付记录列表参数"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "invoice_id": 1,
                "user_id": 1,
                "status": "succeeded",
                "page_num": 1,
                "page_size": 10
            }
        }
    )
    
    invoice_id: Optional[int] = Field(default=None, description="发票ID")
    user_id: Optional[int] = Field(default=None, description="用户ID")
    status: Optional[str] = Field(default=None, description="支付状态")
    page_num: int = Field(default=1, ge=1, description="页码")
    page_size: int = Field(default=10, ge=1, le=1000, description="每页数量")


class CreatePaymentParams(CamelCaseModel):
    """创建支付记录参数"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "payment_data": {}
            }
        }
    )
    
    payment_data: 'PaymentCreate' = Field(description="支付记录创建数据")


class UpdatePaymentParams(BaseModel):
    """更新支付记录参数"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "payment_id": 1,
                "payment_data": {}
            }
        }
    )
    
    payment_id: int = Field(description="支付记录ID")
    payment_data: 'PaymentUpdate' = Field(description="支付记录更新数据")


class ProcessPaymentCallbackParams(BaseModel):
    """处理支付回调参数"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "callback_data": {}
            }
        }
    )
    
    callback_data: 'PaymentCallbackRequest' = Field(description="支付回调数据")


class MarkPaymentSucceededParams(BaseModel):
    """标记支付成功参数"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "payment_id": 1
            }
        }
    )
    
    payment_id: int = Field(description="支付记录ID")


class MarkPaymentFailedParams(CamelCaseModel):
    """标记支付失败参数"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "payment_id": 1,
                "error_message": "支付失败"
            }
        }
    )
    
    payment_id: int = Field(description="支付记录ID")
    error_message: Optional[str] = Field(default=None, description="错误信息")


class RefundPaymentParams(BaseModel):
    """退款参数"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "payment_id": 1
            }
        }
    )
    
    payment_id: int = Field(description="支付记录ID")


class PaymentBase(CamelCaseModel):
    """支付记录基础模型"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "invoice_id": 1,
                "amount": 99.00,
                "payment_method": "alipay",
                "currency": "CNY",
                "payment_id": None,
                "metadata": {}
            }
        }
    )
    
    invoice_id: int = Field(description="发票ID")
    amount: float = Field(description="支付金额")
    payment_method: str = Field(description="支付方式")
    currency: str = Field(default="CNY", description="货币单位")
    payment_id: Optional[str] = Field(default=None, description="支付平台交易ID")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="元数据")


class PaymentCreate(PaymentBase):
    """创建支付记录请求模型"""
    pass


class PaymentUpdate(CamelCaseModel):
    """更新支付记录请求模型"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "payment_id": "2024032412345678",
                "status": "succeeded",
                "error_message": None,
                "metadata": {}
            }
        }
    )
    
    payment_id: Optional[str] = Field(default=None, description="支付平台交易ID")
    status: Optional[str] = Field(default=None, description="支付状态")
    error_message: Optional[str] = Field(default=None, description="错误信息")
    metadata: Optional[Dict[str, Any]] = Field(default=None, description="元数据")


class PaymentInDB(PaymentBase):
    """数据库中的支付记录模型"""
    model_config = ConfigDict(
        from_attributes=True,
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "id": 1,
                "status": "succeeded",
                "error_message": None,
                "paid_at": "2024-03-24T12:00:00",
                "refunded_at": None,
                "created_at": "2024-03-24T12:00:00",
                "updated_at": "2024-03-24T12:00:00"
            }
        }
    )
    
    id: int = Field(description="支付记录ID")
    status: str = Field(description="支付状态")
    error_message: Optional[str] = Field(default=None, description="错误信息")
    paid_at: Optional[datetime] = Field(default=None, description="支付时间")
    refunded_at: Optional[datetime] = Field(default=None, description="退款时间")
    created_at: datetime = Field(description="创建时间")
    updated_at: datetime = Field(description="更新时间")
    
    @field_validator('metadata', mode='before')
    @classmethod
    def validate_metadata(cls, v, info):
        """将meta_data字段映射到metadata"""
        if hasattr(info.data, 'meta_data'):
            return info.data.meta_data
        return v


class PaymentResponse(PaymentInDB):
    """支付记录响应模型"""
    pass


class PaymentListResponse(PaginatedResponse[PaymentResponse]):
    """支付列表响应模型"""
    pass


class PaymentCallbackRequest(BaseModel):
    """支付回调请求模型"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "payment_id": "2024032412345678",
                "status": "succeeded",
                "error_message": None,
                "metadata": {}
            }
        }
    )
    
    payment_id: str = Field(description="支付平台交易ID")
    status: str = Field(description="支付状态")
    error_message: Optional[str] = Field(default=None, description="错误信息")
    metadata: Optional[Dict[str, Any]] = Field(default=None, description="元数据") 