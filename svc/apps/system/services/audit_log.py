"""
审计日志服务实现。
专门负责操作日志的记录、查询、分析等功能。
"""

import logging
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional

from fastapi import Request
from redis.asyncio import Redis
from sqlalchemy.ext.asyncio import AsyncSession

from svc.apps.system.models.audit_log import AuditLog
from svc.apps.system.repositories.audit_log import AuditLogRepository
from svc.apps.system.schemas.audit_log import (AuditLogListResponse,
                                               AuditLogResponse)
from svc.core.exceptions.error_codes import ErrorCode
from svc.core.models.result import Result, ResultFactory
from svc.core.services.base import BaseService
from svc.core.services.mixins.batch_operation import BatchOperationMixin
from svc.core.services.mixins.cache import CacheMixin
from svc.core.services.mixins.error_result import ErrorResultMixin
from svc.core.utils.datetime_utils import (get_utc_now_without_tzinfo,
                                           make_naive)


class AuditLogService(BaseService, ErrorResultMixin, BatchOperationMixin, CacheMixin):
    """
    审计日志服务类，专门处理操作日志相关功能

    主要功能：
    - 记录操作日志
    - 查询和搜索日志
    - 日志统计分析
    - 日志清理和归档
    """

    resource_type = "audit_log"
    
    def __init__(
        self,
        db: AsyncSession,
        redis: Optional[Redis] = None,
        audit_log_repo: Optional[AuditLogRepository] = None
    ):
        """初始化审计日志服务

        Args:
            db: 数据库会话
            redis: Redis客户端（可选）
            audit_log_repo: 审计日志仓库实例，不提供则创建新实例
        """
        BaseService.__init__(self, redis)
        self.db = db
        self.audit_log_repo = audit_log_repo or AuditLogRepository(db)

    def _make_serializable(self, obj: Any) -> Any:
        """将对象转换为可JSON序列化的格式"""
        if isinstance(obj, datetime):
            return obj.isoformat()
        elif isinstance(obj, dict):
            return {k: self._make_serializable(v) for k, v in obj.items()}
        elif isinstance(obj, (list, tuple)):
            return [self._make_serializable(item) for item in obj]
        elif hasattr(obj, '__dict__'):
            # 对于自定义对象，尝试转换为字典
            return self._make_serializable(obj.__dict__)
        else:
            # 对于基本类型（str, int, float, bool, None），直接返回
            return obj
    
    async def get_audit_log(self, log_id: int) -> Result[Dict[str, Any]]:
        """
        获取审计日志详情
        
        Args:
            log_id: 审计日志ID
            
        Returns:
            Result[Dict[str, Any]]: 审计日志详情
        """
        try:
            log = await self.audit_log_repo.get_by_id(self.db, log_id)
            if not log:
                return ResultFactory.error(
                    ErrorCode.NOT_FOUND, 
                    f"审计日志(ID={log_id})不存在"
                )
                
            return ResultFactory.success(log.to_dict())
        except Exception as e:
            return ResultFactory.error(ErrorCode.INTERNAL_ERROR, f"获取审计日志失败: {str(e)}")

    async def get_by_id(self, log_id: int) -> Result[Dict[str, Any]]:
        """
        获取审计日志详情（别名方法，与其他服务保持一致）

        Args:
            log_id: 审计日志ID

        Returns:
            Result[Dict[str, Any]]: 审计日志详情
        """
        return await self.get_audit_log(log_id)

    async def search_audit_logs(
        self,
        action: Optional[str] = None,
        resource_type: Optional[str] = None,
        resource_id: Optional[str] = None,
        user_id: Optional[int] = None,
        username: Optional[str] = None,
        status: Optional[str] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        page_num: int = 1,
        page_size: int = 20
    ) -> Result[Dict[str, Any]]:
        """
        搜索审计日志
        
        Args:
            action: 操作类型
            resource_type: 资源类型
            resource_id: 资源ID
            user_id: 用户ID
            username: 用户名
            status: 操作结果
            start_date: 开始日期
            end_date: 结束日期
            page_num: 页码
            page_size: 每页大小
            
        Returns:
            Result[Dict[str, Any]]: 包含分页信息的审计日志列表结果
        """
        try:
            page_size = page_size or 20
            page_num = page_num or 1
            self.logger.info(f"搜索审计日志: page_num={page_num}, size={page_size}, ... filters ...")

            # 使用工具函数转换为naive datetime以兼容数据库字段（TIMESTAMP WITHOUT TIME ZONE）
            if start_date:
                start_date = make_naive(start_date)
            if end_date:
                end_date = make_naive(end_date)

            logs, total = await self.audit_log_repo.search(
                self.db,
                action=action,
                resource_type=resource_type,
                resource_id=resource_id,
                user_id=user_id,
                username=username,
                status=status,
                start_date=start_date,
                end_date=end_date,
                page_num=page_num,
                page_size=page_size
            )
            
            total_pages = (total + page_size - 1) // page_size if page_size > 0 else 0
            
            paginated_response = AuditLogListResponse(
                items=[AuditLogResponse.model_validate(log) for log in logs],
                total=total,
                page_num=page_num,
                page_size=page_size,
                page_count=total_pages
            )
            
            return ResultFactory.success(paginated_response)
        except Exception as e:
            return ResultFactory.error(ErrorCode.INTERNAL_ERROR, f"搜索审计日志失败: {str(e)}")
    
    async def get_latest_audit_logs(self, limit: int = 10) -> Result[List[Dict[str, Any]]]:
        """
        获取最新的审计日志
        
        Args:
            limit: 返回记录数量
            
        Returns:
            Result[List[Dict[str, Any]]]: 审计日志列表
        """
        try:
            logs = await self.audit_log_repo.get_latest_logs(self.db, limit)
            return ResultFactory.success([log.to_dict() for log in logs])
        except Exception as e:
            return ResultFactory.error(ErrorCode.INTERNAL_ERROR, f"获取最新审计日志失败: {str(e)}")
    
    async def get_resource_audit_logs(
        self, 
        resource_type: str, 
        resource_id: str
    ) -> Result[List[Dict[str, Any]]]:
        """
        获取资源的操作记录
        
        Args:
            resource_type: 资源类型
            resource_id: 资源ID
            
        Returns:
            Result[List[Dict[str, Any]]]: 审计日志列表
        """
        try:
            logs = await self.audit_log_repo.get_actions_by_resource(
                self.db, 
                resource_type, 
                resource_id
            )
            return ResultFactory.success([log.to_dict() for log in logs])
        except Exception as e:
            return ResultFactory.error(ErrorCode.INTERNAL_ERROR, f"获取资源操作记录失败: {str(e)}")
    
    async def create_audit_log(
        self,
        action: str,
        resource_type: str,
        resource_id: Optional[str] = None,
        user_id: Optional[int] = None,
        username: Optional[str] = None,
        request: Optional[Request] = None,
        details: Optional[Dict[str, Any]] = None,
        status: str = "success",
        message: Optional[str] = None
    ) -> Result[Dict[str, Any]]:
        """
        创建审计日志
        
        Args:
            action: 操作类型
            resource_type: 资源类型
            resource_id: 资源ID
            user_id: 用户ID
            username: 用户名
            request: 请求对象，用于获取IP和User-Agent
            details: 操作详情
            status: 操作结果
            message: 额外信息
            
        Returns:
            Result[Dict[str, Any]]: 创建的审计日志
        """
        try:
            # 获取IP和User-Agent
            ip_address = None
            user_agent = None
            if request:
                ip_address = request.client.host if hasattr(request, "client") else None
                user_agent = request.headers.get("user-agent")
            
            # 确保details中的所有值都是可序列化的
            if details:
                details = self._make_serializable(details)

            # 构建审计日志数据，确保字段长度符合数据库限制
            log_data = {
                "action": action[:50] if action else None,  # 限制50字符
                "resource_type": resource_type[:50] if resource_type else None,  # 限制50字符
                "resource_id": resource_id[:100] if resource_id else None,  # 限制100字符
                "user_id": user_id,
                "username": username[:50] if username else None,  # 限制50字符
                "ip_address": ip_address[:50] if ip_address else None,  # 限制50字符
                "user_agent": user_agent[:200] if user_agent else None,  # 限制200字符
                "status": status[:20] if status else "success",  # 限制20字符
                "details": details,
                "message": message[:500] if message else None  # 限制500字符
            }
            
            # 创建审计日志
            log = await self.audit_log_repo.create(self.db, log_data)
            
            return ResultFactory.success(log.to_dict())
        except Exception as e:
            return ResultFactory.error(ErrorCode.INTERNAL_ERROR, f"创建审计日志失败: {str(e)}")

    async def get_user_logs(
        self,
        user_id: int,
        limit: int = 50
    ) -> Result[List[Dict[str, Any]]]:
        """
        获取用户的操作日志

        Args:
            user_id: 用户ID
            limit: 返回数量限制

        Returns:
            Result[List[Dict[str, Any]]]: 审计日志列表
        """
        try:
            logs = await self.audit_log_repo.get_actions_by_user(self.db, user_id, limit)
            return ResultFactory.success([log.to_dict() for log in logs])
        except Exception as e:
            return ResultFactory.error(
                ErrorCode.INTERNAL_ERROR,
                f"获取用户审计日志失败: {str(e)}"
            )

    async def get_statistics(
        self,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None
    ) -> Result[Dict[str, Any]]:
        """
        获取审计日志统计信息

        Args:
            start_date: 开始日期
            end_date: 结束日期

        Returns:
            Result[Dict[str, Any]]: 统计信息
        """
        try:
            # 如果没有指定日期范围，默认统计最近30天
            if not start_date:
                start_date = get_utc_now_without_tzinfo() - timedelta(days=30)
            if not end_date:
                end_date = get_utc_now_without_tzinfo()

            # 使用工具函数转换为naive datetime以兼容数据库字段（TIMESTAMP WITHOUT TIME ZONE）
            start_date = make_naive(start_date)
            end_date = make_naive(end_date)

            # 获取统计数据
            stats = await self.audit_log_repo.get_statistics(
                self.db, start_date, end_date
            )

            return ResultFactory.success(stats)
        except Exception as e:
            return ResultFactory.error(ErrorCode.INTERNAL_ERROR, f"获取审计日志统计失败: {str(e)}")

    async def cleanup_old_logs(self, days_to_keep: int = 90) -> Result[Dict[str, Any]]:
        """
        清理旧的审计日志

        Args:
            days_to_keep: 保留天数，默认90天

        Returns:
            Result[Dict[str, Any]]: 清理结果
        """
        try:
            cutoff_date = get_utc_now_without_tzinfo() - timedelta(days=days_to_keep)
            deleted_count = await self.audit_log_repo.delete_old_logs(self.db, cutoff_date)

            return ResultFactory.success({
                "deleted_count": deleted_count,
                "cutoff_date": cutoff_date.isoformat(),
                "days_kept": days_to_keep
            })
        except Exception as e:
            return ResultFactory.error(ErrorCode.INTERNAL_ERROR, f"清理旧审计日志失败: {str(e)}")

    # 便捷方法，用于记录常见操作

    async def log_login(
        self,
        user_id: int,
        username: str,
        request: Optional[Request] = None,
        status: str = "success"
    ) -> Result[Dict[str, Any]]:
        """记录用户登录日志"""
        return await self.create_audit_log(
            action="login",
            resource_type="user",
            resource_id=str(user_id),
            user_id=user_id,
            username=username,
            request=request,
            status=status,
            message=f"用户 {username} 登录"
        )

    async def log_logout(
        self,
        user_id: int,
        username: str,
        request: Optional[Request] = None
    ) -> Result[Dict[str, Any]]:
        """记录用户登出日志"""
        return await self.create_audit_log(
            action="logout",
            resource_type="user",
            resource_id=str(user_id),
            user_id=user_id,
            username=username,
            request=request,
            message=f"用户 {username} 登出"
        )

    async def log_data_access(
        self,
        resource_type: str,
        resource_id: str,
        user_id: int,
        username: str,
        action: str = "read",
        request: Optional[Request] = None
    ) -> Result[Dict[str, Any]]:
        """记录数据访问日志"""
        return await self.create_audit_log(
            action=action,
            resource_type=resource_type,
            resource_id=resource_id,
            user_id=user_id,
            username=username,
            request=request,
            message=f"用户 {username} {action} {resource_type}:{resource_id}"
        )