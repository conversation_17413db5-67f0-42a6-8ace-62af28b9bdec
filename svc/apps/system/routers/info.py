"""
系统信息路由模块。
提供系统信息查询的API接口。
"""

from typing import Any, Dict

from fastapi import APIRouter, Depends

from svc.apps.auth.dependencies import get_current_superuser, get_current_user
from svc.apps.auth.models.user import User
from svc.apps.system.dependencies import get_monitor_service
from svc.apps.system.services.monitor import SystemMonitorService
from svc.core.models.result import Result

# 创建路由器
router = APIRouter(tags=["系统信息"])

# === 路由处理函数 ===

@router.get("", response_model=Result[Dict[str, Any]], summary="获取系统信息")
async def get_system_info(
    monitor_service: SystemMonitorService = Depends(get_monitor_service),
    current_user: User = Depends(get_current_user)  # 需要登录
):
    """
    获取系统基本信息

    返回系统的基本信息，包括应用信息、系统信息和资源使用情况。

    **需要权限：** 登录用户

    **返回信息：**
    - **application**: 应用信息（名称、版本、环境、运行时间）
    - **system**: 系统信息（平台、Python版本、架构等）
    - **resources**: 资源使用情况（CPU、内存、磁盘）
    - **timestamp**: 获取时间
    """
    return await monitor_service.get_system_info()

@router.get("/version", response_model=Result[Dict[str, str]], summary="获取版本信息")
async def get_version_info(
    monitor_service: SystemMonitorService = Depends(get_monitor_service),
    current_user: User = Depends(get_current_user)  # 需要登录
):
    """
    获取系统版本信息

    返回系统的版本信息，包括应用版本、构建信息等。

    **需要权限：** 登录用户

    **返回信息：**
    - **version**: 系统版本
    - **build_time**: 构建时间
    - **git_commit**: Git提交哈希值
    - **environment**: 运行环境
    """
    # 获取完整系统信息
    result = await monitor_service.get_system_info()
    if not result.is_success:
        return result

    # 过滤只返回版本相关信息
    app_info = result.data.get("application", {})
    version_info = {
        "version": app_info.get("version", "unknown"),
        "environment": app_info.get("environment", "unknown"),
        "build_time": "2025-06-12T00:00:00Z",  # 实际应从构建信息获取
        "git_commit": "latest"  # 实际应从Git信息获取
    }

    return Result(
        is_success=True,
        result_code=200,
        result_msg="获取成功",
        data=version_info
    )

@router.get("/status", response_model=Result[Dict[str, Any]], summary="获取系统状态")
async def get_system_status(
    monitor_service: SystemMonitorService = Depends(get_monitor_service),
    current_user: User = Depends(get_current_user)  # 需要登录
):
    """
    获取系统状态信息

    返回系统的当前状态信息，包括运行状态和资源使用情况。

    **需要权限：** 登录用户

    **返回信息：**
    - **status**: 系统运行状态
    - **uptime**: 系统运行时间
    - **cpu_usage**: CPU使用率
    - **memory_usage**: 内存使用量
    - **disk_usage**: 磁盘使用量
    """
    # 获取完整系统信息
    result = await monitor_service.get_system_info()
    if not result.is_success:
        return result

    # 过滤只返回状态相关信息
    app_info = result.data.get("application", {})
    resources = result.data.get("resources", {})

    status_info = {
        "status": "running",  # 如果能响应请求说明系统正在运行
        "uptime": app_info.get("uptime", "unknown"),
        "cpu_usage": resources.get("cpu", {}).get("usage_percent", "unknown"),
        "memory_usage": resources.get("memory", {}).get("percent", "unknown"),
        "disk_usage": resources.get("disk", {}).get("percent", "unknown")
    }

    return Result(
        is_success=True,
        result_code=200,
        result_msg="获取成功",
        data=status_info
    )


@router.get("/resources", response_model=Result[Dict[str, Any]], summary="获取资源使用情况")
async def get_system_resources(
    monitor_service: SystemMonitorService = Depends(get_monitor_service),
    current_user: User = Depends(get_current_superuser)  # 需要管理员权限
):
    """
    获取系统资源使用情况

    返回详细的系统资源使用情况，包括CPU、内存、磁盘等信息。

    **需要权限：** 超级管理员

    **返回信息：**
    - **cpu**: CPU使用情况（使用率、核心数等）
    - **memory**: 内存使用情况（总量、已用、可用等）
    - **disk**: 磁盘使用情况（总量、已用、可用等）
    """
    # 获取完整系统信息
    result = await monitor_service.get_system_info()
    if not result.is_success:
        return result

    # 只返回资源相关信息
    resources = result.data.get("resources", {})

    return Result(
        is_success=True,
        result_code=200,
        result_msg="获取成功",
        data=resources
    )