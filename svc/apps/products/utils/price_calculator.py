"""
价格计算器工具类
提供商品价格相关的计算功能，包括折扣、税费、批量价格等计算
"""

import logging
from decimal import ROUND_HALF_UP, Decimal
from typing import Any, Dict, List, Optional, Union

from svc.apps.products.models.product import Product
from svc.apps.products.models.spec import ProductSpecCombination


class PriceCalculator:
    """
    价格计算器类，提供各种价格计算功能
    
    主要功能：
    - 基础价格计算
    - 折扣价格计算
    - 批量价格计算
    - 税费计算
    - 利润率计算
    - 价格比较和分析
    """
    
    def __init__(self, currency: str = "CNY", precision: int = 2):
        """
        初始化价格计算器
        
        Args:
            currency: 货币单位，默认为CNY
            precision: 价格精度，默认保留2位小数
        """
        self.currency = currency
        self.precision = precision
    
    def calculate_base_price(
        self, 
        product: Union[Product, ProductSpecCombination], 
        quantity: int = 1
    ) -> Decimal:
        """
        计算基础价格
        
        Args:
            product: 商品或变体对象
            quantity: 数量
            
        Returns:
            Decimal: 基础价格
        """
        try:
            if isinstance(product, ProductSpecCombination):
                unit_price = product.price
            else:
                unit_price = product.price or 0.0
            
            base_price = Decimal(str(unit_price)) * quantity
            return self._round_price(base_price)
        except Exception as e:
            return Decimal('0.00')
    
    def calculate_discount_price(
        self,
        original_price: Union[float, Decimal],
        discount_type: str,
        discount_value: Union[float, Decimal],
        max_discount: Optional[Union[float, Decimal]] = None
    ) -> Dict[str, Any]:
        """
        计算折扣价格
        
        Args:
            original_price: 原价
            discount_type: 折扣类型 ('percentage', 'fixed', 'buy_x_get_y')
            discount_value: 折扣值
            max_discount: 最大折扣金额限制
            
        Returns:
            Dict: 包含折扣后价格、折扣金额、折扣率等信息
        """
        try:
            original = Decimal(str(original_price))
            discount_val = Decimal(str(discount_value))
            
            if discount_type == 'percentage':
                # 百分比折扣
                discount_amount = original * (discount_val / 100)
            elif discount_type == 'fixed':
                # 固定金额折扣
                discount_amount = discount_val
            else:
                raise ValueError(f"不支持的折扣类型: {discount_type}")
            
            # 应用最大折扣限制
            if max_discount is not None:
                max_disc = Decimal(str(max_discount))
                discount_amount = min(discount_amount, max_disc)
            
            # 确保折扣不超过原价
            discount_amount = min(discount_amount, original)
            
            final_price = original - discount_amount
            discount_rate = (discount_amount / original * 100) if original > 0 else Decimal('0')
            
            return {
                'original_price': self._round_price(original),
                'discount_amount': self._round_price(discount_amount),
                'final_price': self._round_price(final_price),
                'discount_rate': self._round_price(discount_rate),
                'savings': self._round_price(discount_amount)
            }
        except Exception as e:
            return {
                'original_price': Decimal(str(original_price)),
                'discount_amount': Decimal('0.00'),
                'final_price': Decimal(str(original_price)),
                'discount_rate': Decimal('0.00'),
                'savings': Decimal('0.00')
            }
    
    def calculate_bulk_price(
        self,
        unit_price: Union[float, Decimal],
        quantity: int,
        bulk_rules: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        计算批量价格
        
        Args:
            unit_price: 单价
            quantity: 数量
            bulk_rules: 批量规则列表，格式：[{'min_qty': 10, 'discount_type': 'percentage', 'discount_value': 5}]
            
        Returns:
            Dict: 批量价格计算结果
        """
        try:
            unit = Decimal(str(unit_price))
            original_total = unit * quantity
            
            # 找到适用的批量规则
            applicable_rule = None
            for rule in sorted(bulk_rules, key=lambda x: x['min_qty'], reverse=True):
                if quantity >= rule['min_qty']:
                    applicable_rule = rule
                    break
            
            if not applicable_rule:
                return {
                    'unit_price': self._round_price(unit),
                    'quantity': quantity,
                    'original_total': self._round_price(original_total),
                    'final_total': self._round_price(original_total),
                    'discount_amount': Decimal('0.00'),
                    'effective_unit_price': self._round_price(unit),
                    'bulk_rule_applied': None
                }
            
            # 应用批量折扣
            discount_result = self.calculate_discount_price(
                original_total,
                applicable_rule['discount_type'],
                applicable_rule['discount_value']
            )
            
            effective_unit_price = discount_result['final_price'] / quantity
            
            return {
                'unit_price': self._round_price(unit),
                'quantity': quantity,
                'original_total': self._round_price(original_total),
                'final_total': discount_result['final_price'],
                'discount_amount': discount_result['discount_amount'],
                'effective_unit_price': self._round_price(effective_unit_price),
                'bulk_rule_applied': applicable_rule
            }
        except Exception as e:
            unit = Decimal(str(unit_price))
            total = unit * quantity
            return {
                'unit_price': self._round_price(unit),
                'quantity': quantity,
                'original_total': self._round_price(total),
                'final_total': self._round_price(total),
                'discount_amount': Decimal('0.00'),
                'effective_unit_price': self._round_price(unit),
                'bulk_rule_applied': None
            }
    
    def calculate_tax(
        self,
        price: Union[float, Decimal],
        tax_rate: Union[float, Decimal],
        tax_inclusive: bool = False
    ) -> Dict[str, Any]:
        """
        计算税费
        
        Args:
            price: 价格
            tax_rate: 税率（百分比）
            tax_inclusive: 是否含税价格
            
        Returns:
            Dict: 税费计算结果
        """
        try:
            price_decimal = Decimal(str(price))
            rate = Decimal(str(tax_rate)) / 100
            
            if tax_inclusive:
                # 含税价格，计算税前价格和税额
                tax_exclusive_price = price_decimal / (1 + rate)
                tax_amount = price_decimal - tax_exclusive_price
                tax_inclusive_price = price_decimal
            else:
                # 不含税价格，计算税额和含税价格
                tax_exclusive_price = price_decimal
                tax_amount = price_decimal * rate
                tax_inclusive_price = price_decimal + tax_amount
            
            return {
                'tax_exclusive_price': self._round_price(tax_exclusive_price),
                'tax_amount': self._round_price(tax_amount),
                'tax_inclusive_price': self._round_price(tax_inclusive_price),
                'tax_rate': Decimal(str(tax_rate))
            }
        except Exception as e:
            price_decimal = Decimal(str(price))
            return {
                'tax_exclusive_price': self._round_price(price_decimal),
                'tax_amount': Decimal('0.00'),
                'tax_inclusive_price': self._round_price(price_decimal),
                'tax_rate': Decimal('0.00')
            }
    
    def calculate_profit_margin(
        self,
        selling_price: Union[float, Decimal],
        cost_price: Union[float, Decimal]
    ) -> Dict[str, Any]:
        """
        计算利润率
        
        Args:
            selling_price: 销售价格
            cost_price: 成本价格
            
        Returns:
            Dict: 利润率计算结果
        """
        try:
            selling = Decimal(str(selling_price))
            cost = Decimal(str(cost_price))
            
            if cost == 0:
                return {
                    'selling_price': self._round_price(selling),
                    'cost_price': self._round_price(cost),
                    'profit_amount': self._round_price(selling),
                    'profit_margin': Decimal('100.00'),
                    'markup': Decimal('0.00')
                }
            
            profit_amount = selling - cost
            profit_margin = (profit_amount / selling * 100) if selling > 0 else Decimal('0')
            markup = (profit_amount / cost * 100)
            
            return {
                'selling_price': self._round_price(selling),
                'cost_price': self._round_price(cost),
                'profit_amount': self._round_price(profit_amount),
                'profit_margin': self._round_price(profit_margin),
                'markup': self._round_price(markup)
            }
        except Exception as e:
            selling = Decimal(str(selling_price))
            cost = Decimal(str(cost_price))
            return {
                'selling_price': self._round_price(selling),
                'cost_price': self._round_price(cost),
                'profit_amount': Decimal('0.00'),
                'profit_margin': Decimal('0.00'),
                'markup': Decimal('0.00')
            }
    
    def compare_prices(
        self,
        prices: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        比较价格
        
        Args:
            prices: 价格列表，格式：[{'name': '商品A', 'price': 100.00}, ...]
            
        Returns:
            Dict: 价格比较结果
        """
        try:
            if not prices:
                return {'error': '价格列表为空'}
            
            price_values = [Decimal(str(p['price'])) for p in prices]
            min_price = min(price_values)
            max_price = max(price_values)
            avg_price = sum(price_values) / len(price_values)
            
            # 找到最低价和最高价的商品
            min_product = next(p for p in prices if Decimal(str(p['price'])) == min_price)
            max_product = next(p for p in prices if Decimal(str(p['price'])) == max_price)
            
            # 计算价格差异
            price_range = max_price - min_price
            price_variance = price_range / avg_price * 100 if avg_price > 0 else Decimal('0')
            
            return {
                'min_price': self._round_price(min_price),
                'max_price': self._round_price(max_price),
                'avg_price': self._round_price(avg_price),
                'price_range': self._round_price(price_range),
                'price_variance': self._round_price(price_variance),
                'min_price_product': min_product,
                'max_price_product': max_product,
                'total_products': len(prices)
            }
        except Exception as e:
            return {'error': f'价格比较失败: {str(e)}'}
    
    def _round_price(self, price: Decimal) -> Decimal:
        """
        按指定精度四舍五入价格
        
        Args:
            price: 价格
            
        Returns:
            Decimal: 四舍五入后的价格
        """
        return price.quantize(
            Decimal('0.01') if self.precision == 2 else Decimal('0.001'),
            rounding=ROUND_HALF_UP
        )
