"""
库存计算器工具类
提供库存相关的计算功能，包括库存预测、安全库存、周转率等计算
"""

from typing import Any, Dict, List

from svc.apps.products.models.inventory import Inventory, InventoryStatus


class InventoryCalculator:
    """
    库存计算器类，提供各种库存计算功能
    
    主要功能：
    - 可用库存计算
    - 安全库存计算
    - 库存周转率计算
    - 库存预测
    - 缺货风险评估
    - 库存价值计算
    """
    
    def __init__(self):
        """初始化库存计算器"""
    
    def calculate_available_stock(
        self,
        inventories: List[Inventory],
        include_reserved: bool = False
    ) -> Dict[str, Any]:
        """
        计算可用库存
        
        Args:
            inventories: 库存记录列表
            include_reserved: 是否包含预留库存
            
        Returns:
            Dict: 可用库存计算结果
        """
        try:
            total_quantity = 0
            available_quantity = 0
            reserved_quantity = 0
            expired_quantity = 0
            damaged_quantity = 0
            
            by_status = {}
            by_location = {}
            
            for inventory in inventories:
                total_quantity += inventory.quantity
                reserved_quantity += inventory.reserved_quantity
                
                # 按状态统计
                status = inventory.status
                if status not in by_status:
                    by_status[status] = 0
                by_status[status] += inventory.quantity
                
                # 按位置统计
                location = inventory.warehouse_location or 'unknown'
                if location not in by_location:
                    by_location[location] = 0
                by_location[location] += inventory.quantity
                
                # 计算可用库存
                if inventory.is_available():
                    if include_reserved:
                        available_quantity += inventory.quantity
                    else:
                        available_quantity += inventory.available_quantity
                elif inventory.is_expired():
                    expired_quantity += inventory.quantity
                elif inventory.status == InventoryStatus.DAMAGED:
                    damaged_quantity += inventory.quantity
            
            return {
                'total_quantity': total_quantity,
                'available_quantity': available_quantity,
                'reserved_quantity': reserved_quantity,
                'expired_quantity': expired_quantity,
                'damaged_quantity': damaged_quantity,
                'by_status': by_status,
                'by_location': by_location,
                'inventory_count': len(inventories)
            }
        except Exception as e:
            return {
                'total_quantity': 0,
                'available_quantity': 0,
                'reserved_quantity': 0,
                'expired_quantity': 0,
                'damaged_quantity': 0,
                'by_status': {},
                'by_location': {},
                'inventory_count': 0
            }
    
    def calculate_safety_stock(
        self,
        average_demand: float,
        lead_time_days: int,
        demand_variability: float = 0.2,
        service_level: float = 0.95
    ) -> Dict[str, Any]:
        """
        计算安全库存
        
        Args:
            average_demand: 平均日需求量
            lead_time_days: 供应商交货时间（天）
            demand_variability: 需求变异系数，默认0.2
            service_level: 服务水平，默认95%
            
        Returns:
            Dict: 安全库存计算结果
        """
        try:
            # 简化的安全库存计算公式
            # 安全库存 = Z值 × √(交货时间) × 需求标准差
            
            # 根据服务水平确定Z值（正态分布）
            z_scores = {
                0.90: 1.28,
                0.95: 1.65,
                0.97: 1.88,
                0.99: 2.33
            }
            z_value = z_scores.get(service_level, 1.65)
            
            # 计算需求标准差
            demand_std = average_demand * demand_variability
            
            # 计算安全库存
            safety_stock = z_value * (lead_time_days ** 0.5) * demand_std
            
            # 计算再订货点
            reorder_point = (average_demand * lead_time_days) + safety_stock
            
            # 计算最大库存
            max_stock = reorder_point + (average_demand * lead_time_days)
            
            return {
                'safety_stock': round(safety_stock, 2),
                'reorder_point': round(reorder_point, 2),
                'max_stock': round(max_stock, 2),
                'average_demand': average_demand,
                'lead_time_days': lead_time_days,
                'service_level': service_level,
                'z_value': z_value
            }
        except Exception as e:
            return {
                'safety_stock': 0,
                'reorder_point': 0,
                'max_stock': 0,
                'average_demand': average_demand,
                'lead_time_days': lead_time_days,
                'service_level': service_level,
                'z_value': 0
            }
    
    def calculate_turnover_rate(
        self,
        cost_of_goods_sold: float,
        average_inventory_value: float,
        period_days: int = 365
    ) -> Dict[str, Any]:
        """
        计算库存周转率
        
        Args:
            cost_of_goods_sold: 销货成本
            average_inventory_value: 平均库存价值
            period_days: 计算周期天数，默认365天
            
        Returns:
            Dict: 库存周转率计算结果
        """
        try:
            if average_inventory_value == 0:
                return {
                    'turnover_rate': 0,
                    'turnover_days': 0,
                    'cost_of_goods_sold': cost_of_goods_sold,
                    'average_inventory_value': average_inventory_value,
                    'period_days': period_days
                }
            
            # 库存周转率 = 销货成本 / 平均库存价值
            turnover_rate = cost_of_goods_sold / average_inventory_value
            
            # 库存周转天数 = 计算周期 / 库存周转率
            turnover_days = period_days / turnover_rate if turnover_rate > 0 else 0
            
            return {
                'turnover_rate': round(turnover_rate, 2),
                'turnover_days': round(turnover_days, 2),
                'cost_of_goods_sold': cost_of_goods_sold,
                'average_inventory_value': average_inventory_value,
                'period_days': period_days
            }
        except Exception as e:
            return {
                'turnover_rate': 0,
                'turnover_days': 0,
                'cost_of_goods_sold': cost_of_goods_sold,
                'average_inventory_value': average_inventory_value,
                'period_days': period_days
            }
    
    def predict_stock_needs(
        self,
        historical_sales: List[Dict[str, Any]],
        forecast_days: int = 30,
        growth_rate: float = 0.0
    ) -> Dict[str, Any]:
        """
        预测库存需求
        
        Args:
            historical_sales: 历史销售数据，格式：[{'date': '2023-01-01', 'quantity': 10}, ...]
            forecast_days: 预测天数
            growth_rate: 增长率（年化），默认0%
            
        Returns:
            Dict: 库存需求预测结果
        """
        try:
            if not historical_sales:
                return {
                    'predicted_demand': 0,
                    'daily_average': 0,
                    'forecast_days': forecast_days,
                    'confidence': 'low'
                }
            
            # 计算日均销量
            total_quantity = sum(sale['quantity'] for sale in historical_sales)
            total_days = len(historical_sales)
            daily_average = total_quantity / total_days if total_days > 0 else 0
            
            # 应用增长率
            daily_growth_rate = (1 + growth_rate) ** (1/365) - 1
            adjusted_daily_average = daily_average * (1 + daily_growth_rate * forecast_days / 2)
            
            # 预测需求
            predicted_demand = adjusted_daily_average * forecast_days
            
            # 计算置信度
            if total_days >= 30:
                confidence = 'high'
            elif total_days >= 14:
                confidence = 'medium'
            else:
                confidence = 'low'
            
            # 计算趋势
            if len(historical_sales) >= 7:
                recent_avg = sum(sale['quantity'] for sale in historical_sales[-7:]) / 7
                overall_avg = daily_average
                trend = 'increasing' if recent_avg > overall_avg * 1.1 else 'decreasing' if recent_avg < overall_avg * 0.9 else 'stable'
            else:
                trend = 'unknown'
            
            return {
                'predicted_demand': round(predicted_demand, 2),
                'daily_average': round(daily_average, 2),
                'adjusted_daily_average': round(adjusted_daily_average, 2),
                'forecast_days': forecast_days,
                'confidence': confidence,
                'trend': trend,
                'historical_days': total_days,
                'growth_rate': growth_rate
            }
        except Exception as e:
            return {
                'predicted_demand': 0,
                'daily_average': 0,
                'adjusted_daily_average': 0,
                'forecast_days': forecast_days,
                'confidence': 'low',
                'trend': 'unknown',
                'historical_days': 0,
                'growth_rate': growth_rate
            }
    
    def assess_stockout_risk(
        self,
        current_stock: int,
        daily_demand: float,
        lead_time_days: int,
        safety_stock: int = 0
    ) -> Dict[str, Any]:
        """
        评估缺货风险
        
        Args:
            current_stock: 当前库存
            daily_demand: 日均需求
            lead_time_days: 交货时间
            safety_stock: 安全库存
            
        Returns:
            Dict: 缺货风险评估结果
        """
        try:
            # 计算库存可用天数
            days_of_stock = current_stock / daily_demand if daily_demand > 0 else float('inf')
            
            # 计算到缺货的天数
            days_to_stockout = max(0, days_of_stock)
            
            # 评估风险等级
            if days_to_stockout <= lead_time_days:
                risk_level = 'critical'
            elif days_to_stockout <= lead_time_days + safety_stock / daily_demand:
                risk_level = 'high'
            elif days_to_stockout <= lead_time_days * 2:
                risk_level = 'medium'
            else:
                risk_level = 'low'
            
            # 计算建议补货量
            target_stock = (daily_demand * lead_time_days) + safety_stock
            suggested_reorder = max(0, target_stock - current_stock)
            
            return {
                'risk_level': risk_level,
                'days_of_stock': round(days_of_stock, 2),
                'days_to_stockout': round(days_to_stockout, 2),
                'suggested_reorder': round(suggested_reorder, 2),
                'current_stock': current_stock,
                'daily_demand': daily_demand,
                'lead_time_days': lead_time_days,
                'safety_stock': safety_stock
            }
        except Exception as e:
            return {
                'risk_level': 'unknown',
                'days_of_stock': 0,
                'days_to_stockout': 0,
                'suggested_reorder': 0,
                'current_stock': current_stock,
                'daily_demand': daily_demand,
                'lead_time_days': lead_time_days,
                'safety_stock': safety_stock
            }
    
    def calculate_inventory_value(
        self,
        inventories: List[Inventory],
        valuation_method: str = 'fifo'
    ) -> Dict[str, Any]:
        """
        计算库存价值
        
        Args:
            inventories: 库存记录列表
            valuation_method: 估值方法 ('fifo', 'lifo', 'average', 'specific')
            
        Returns:
            Dict: 库存价值计算结果
        """
        try:
            total_quantity = 0
            total_value = 0
            by_status = {}
            
            for inventory in inventories:
                quantity = inventory.quantity
                unit_cost = inventory.unit_cost or 0
                
                total_quantity += quantity
                inventory_value = quantity * unit_cost / 100  # 转换为元
                total_value += inventory_value
                
                # 按状态统计价值
                status = inventory.status
                if status not in by_status:
                    by_status[status] = {'quantity': 0, 'value': 0}
                by_status[status]['quantity'] += quantity
                by_status[status]['value'] += inventory_value
            
            average_unit_cost = total_value / total_quantity if total_quantity > 0 else 0
            
            return {
                'total_quantity': total_quantity,
                'total_value': round(total_value, 2),
                'average_unit_cost': round(average_unit_cost, 2),
                'by_status': by_status,
                'valuation_method': valuation_method,
                'inventory_count': len(inventories)
            }
        except Exception as e:
            return {
                'total_quantity': 0,
                'total_value': 0,
                'average_unit_cost': 0,
                'by_status': {},
                'valuation_method': valuation_method,
                'inventory_count': 0
            }
