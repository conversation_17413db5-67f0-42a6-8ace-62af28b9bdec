"""
分类API路由
包含分类的创建、查询、更新和删除功能
"""
from typing import Any, List, Optional

from fastapi import APIRouter, Depends, Query, Path, status

from svc.core.models.result import Result
from svc.apps.auth.dependencies import (
    get_current_active_user,
    has_permission,
    resource_permission
)
from svc.apps.auth.models.user import User
from svc.apps.products.schemas.category import (
    CategoryCreate,
    CategoryUpdate,
    CategoryListResponse,
    CategoryTreeResponse,
    GetCategoriesParams,
    UserCategoryResponse,
    UserCategoryListResponse
)
from svc.apps.products.services.category import CategoryService
from svc.apps.products.dependencies import get_category_service
from svc.core.exceptions.route_error_handler import handle_route_errors, CATEGORY_ERROR_MAPPING
from svc.core.exceptions.error_codes import ErrorCode
from svc.core.schemas.base import PageParams

# 创建路由器
router = APIRouter(
    tags=["分类管理"]
)

# === 管理端路由 (Admin Routes) ===

@router.get("/admin/list", response_model=Result[CategoryListResponse])
@handle_route_errors(CATEGORY_ERROR_MAPPING)
async def admin_list_categories(
    params: PageParams = Depends(),
    status: Optional[str] = Query(None, description="分类状态"),
    parent_id: Optional[int] = Query(None, description="父分类ID"),
    level: Optional[int] = Query(None, description="分类层级"),
    is_featured: Optional[bool] = Query(None, description="是否推荐分类"),
    search_term: Optional[str] = Query(None, description="搜索关键词"),
    order_by: Optional[str] = Query("sort_order", description="排序字段"),
    order_desc: Optional[bool] = Query(False, description="是否降序"),
    category_service: CategoryService = Depends(get_category_service),
    current_user: User = Depends(get_current_active_user),
    _: bool = Depends(lambda: has_permission("category:read"))
) -> Result[CategoryListResponse]:
    """获取分类列表 (管理端)"""
    params_obj = GetCategoriesParams(
        page_num=params.page_num,
        page_size=params.page_size,
        status=status,
        parent_id=parent_id,
        level=level,
        is_featured=is_featured,
        search_term=search_term,
        order_by=order_by,
        order_desc=order_desc
    )
    result = await category_service.get_categories(params=params_obj)
    return result

@router.get("/admin/tree", response_model=Result[List[CategoryTreeResponse]])
@handle_route_errors(CATEGORY_ERROR_MAPPING)
async def admin_get_category_tree(
    parent_id: Optional[int] = Query(None, description="父分类ID"),
    category_service: CategoryService = Depends(get_category_service),
    current_user: User = Depends(get_current_active_user),
    _: bool = Depends(lambda: has_permission("category:read"))
) -> Result[List[CategoryTreeResponse]]:
    """获取分类树 (管理端)"""
    result = await category_service.get_category_tree(parent_id=parent_id)
    return result

@router.get("/admin/{category_id}", response_model=Result)
@handle_route_errors(CATEGORY_ERROR_MAPPING)
async def admin_get_category_details(
    category_id: int = Path(..., description="分类ID"),
    category_service: CategoryService = Depends(get_category_service),
    current_user: User = Depends(get_current_active_user),
    _: Any = Depends(resource_permission("category", "read")),
) -> Result:
    """获取分类详情 (管理端)"""
    result = await category_service.get_category(category_id=category_id)
    return result

@router.post("/admin/create", response_model=Result, status_code=status.HTTP_201_CREATED)
@handle_route_errors(CATEGORY_ERROR_MAPPING)
async def admin_create_category(
    category_data: CategoryCreate,
    category_service: CategoryService = Depends(get_category_service),
    current_user: User = Depends(get_current_active_user),
    _: bool = Depends(lambda: has_permission("category:create")),
) -> Result:
    """创建分类 (管理端)"""
    result = await category_service.create_category(params=category_data)
    return result

@router.put("/admin/{category_id}", response_model=Result)
@handle_route_errors(CATEGORY_ERROR_MAPPING)
async def admin_update_category(
    category_in: CategoryUpdate,
    category_id: int = Path(..., description="分类ID"),
    category_service: CategoryService = Depends(get_category_service),
    current_user: User = Depends(get_current_active_user),
    _: Any = Depends(resource_permission("category", "update")),
) -> Result:
    """更新分类 (管理端)"""
    result = await category_service.update_category(category_id=category_id, params=category_in)
    return result

# === 客户端路由 (Client Routes) ===

@router.get("/list", response_model=Result[UserCategoryListResponse])
@handle_route_errors(CATEGORY_ERROR_MAPPING)
async def list_categories(
    params: PageParams = Depends(),
    parent_id: Optional[int] = Query(None, description="父分类ID"),
    level: Optional[int] = Query(None, description="分类层级"),
    category_service: CategoryService = Depends(get_category_service),
) -> Result[UserCategoryListResponse]:
    """获取分类列表 (客户端)"""
    params_obj = GetCategoriesParams(
        page_num=params.page_num,
        page_size=params.page_size,
        status="active",  # 客户端只显示启用分类
        parent_id=parent_id,
        level=level
    )
    result = await category_service.get_categories(params=params_obj, user_mode=True)
    return result

@router.get("/tree", response_model=Result[List[CategoryTreeResponse]])
@handle_route_errors(CATEGORY_ERROR_MAPPING)
async def get_category_tree(
    parent_id: Optional[int] = Query(None, description="父分类ID"),
    category_service: CategoryService = Depends(get_category_service),
) -> Result[List[CategoryTreeResponse]]:
    """获取分类树 (客户端)"""
    result = await category_service.get_category_tree(parent_id=parent_id)
    return result

@router.get("/recommended", response_model=Result[CategoryListResponse])
@handle_route_errors(CATEGORY_ERROR_MAPPING)
async def list_recommended_categories(
    params: PageParams = Depends(),
    category_service: CategoryService = Depends(get_category_service),
) -> Result[CategoryListResponse]:
    """获取推荐分类列表 (客户端)"""
    params_obj = GetCategoriesParams(
        page_num=params.page_num,
        page_size=params.page_size,
        status="active",
        is_featured=True
    )
    result = await category_service.get_categories(params=params_obj)
    return result

@router.get("/{category_id}", response_model=Result)
@handle_route_errors(CATEGORY_ERROR_MAPPING)
async def get_category_details(
    category_id: int = Path(..., description="分类ID"),
    category_service: CategoryService = Depends(get_category_service),
) -> Result:
    """获取分类详情 (客户端)"""
    result = await category_service.get_category(category_id=category_id)
    return result
