"""
商品规格API路由
包含规格和规格选项的创建、查询、更新和删除功能
"""
from typing import Any, Optional

from fastapi import APIRouter, Depends, Path, Query, status

from svc.apps.auth.dependencies import (get_current_active_user,
                                        has_permission, resource_permission)
from svc.apps.auth.models.user import User
from svc.apps.products.dependencies import (get_spec_option_service,
                                            get_spec_service)
from svc.apps.products.schemas.spec import (SpecCreate, SpecListResponse,
                                            SpecOptionCreate,
                                            SpecOptionListResponse,
                                            SpecOptionResponse,
                                            SpecOptionUpdate, SpecResponse,
                                            SpecUpdate)
from svc.apps.products.services.spec import SpecOptionService, SpecService
from svc.core.exceptions.route_error_handler import handle_route_errors
from svc.core.models.result import Result
from svc.core.schemas.base import PageParams

# 创建路由器
router = APIRouter(
    tags=["商品规格管理"]
)

# 错误映射
SPEC_ERROR_MAPPING = {}

# === 管理端路由 (Admin Routes) ===

@router.get("/admin/specs", response_model=Result[SpecListResponse])
@handle_route_errors(SPEC_ERROR_MAPPING)
async def admin_list_specs(
    params: PageParams = Depends(),
    status: Optional[str] = Query(None, description="规格状态"),
    search_term: Optional[str] = Query(None, description="搜索关键词", alias="searchTerm"),
    spec_service: SpecService = Depends(get_spec_service),
    current_user: User = Depends(get_current_active_user),
    _: bool = Depends(lambda: has_permission("spec:read"))
) -> Result[SpecListResponse]:
    """获取规格列表 (管理端)"""
    result = await spec_service.get_specs_list(
        page_num=params.page_num,
        page_size=params.page_size,
        status=status,
        search_term=search_term
    )
    return result

@router.get("/admin/specs/{spec_id}", response_model=Result[SpecResponse])
@handle_route_errors(SPEC_ERROR_MAPPING)
async def admin_get_spec(
    spec_id: int = Path(..., description="规格ID"),
    spec_service: SpecService = Depends(get_spec_service),
    current_user: User = Depends(get_current_active_user),
    _: bool = Depends(lambda: has_permission("spec:read"))
) -> Result[SpecResponse]:
    """获取规格详情 (管理端)"""
    result = await spec_service.get_spec(spec_id=spec_id, user_mode=False)
    return result

@router.post("/admin/specs", response_model=Result[SpecResponse], status_code=status.HTTP_201_CREATED)
@handle_route_errors(SPEC_ERROR_MAPPING)
async def admin_create_spec(
    spec_data: SpecCreate,
    spec_service: SpecService = Depends(get_spec_service),
    current_user: User = Depends(get_current_active_user),
    _: bool = Depends(lambda: has_permission("spec:create"))
) -> Result[SpecResponse]:
    """创建规格 (管理端)"""
    result = await spec_service.create_spec(params=spec_data)
    return result

@router.put("/admin/specs/{spec_id}", response_model=Result[SpecResponse])
@handle_route_errors(SPEC_ERROR_MAPPING)
async def admin_update_spec(
    spec_data: SpecUpdate,
    spec_id: int = Path(..., description="规格ID"),
    spec_service: SpecService = Depends(get_spec_service),
    current_user: User = Depends(get_current_active_user),
    _: Any = Depends(resource_permission("spec", "update"))
) -> Result[SpecResponse]:
    """更新规格 (管理端)"""
    result = await spec_service.update_spec(spec_id=spec_id, params=spec_data)
    return result

@router.delete("/admin/specs/{spec_id}", response_model=Result)
@handle_route_errors(SPEC_ERROR_MAPPING)
async def admin_delete_spec(
    spec_id: int = Path(..., description="规格ID"),
    spec_service: SpecService = Depends(get_spec_service),
    current_user: User = Depends(get_current_active_user),
    _: Any = Depends(resource_permission("spec", "delete"))
) -> Result:
    """删除规格 (管理端)"""
    result = await spec_service.delete_spec(spec_id=spec_id)
    return result

# === 规格选项管理端路由 ===

@router.get("/admin/specs/{spec_id}/options", response_model=Result[SpecOptionListResponse])
@handle_route_errors(SPEC_ERROR_MAPPING)
async def admin_list_spec_options(
    spec_id: int = Path(..., description="规格ID"),
    params: PageParams = Depends(),
    spec_option_service: SpecOptionService = Depends(get_spec_option_service),
    current_user: User = Depends(get_current_active_user),
    _: bool = Depends(lambda: has_permission("spec:read"))
) -> Result[SpecOptionListResponse]:
    """获取规格选项列表 (管理端)"""
    result = await spec_option_service.get_options_by_spec(
        spec_id=spec_id,
        page_num=params.page_num,
        page_size=params.page_size
    )
    return result

@router.post("/admin/specs/{spec_id}/options", response_model=Result[SpecOptionResponse], status_code=status.HTTP_201_CREATED)
@handle_route_errors(SPEC_ERROR_MAPPING)
async def admin_create_spec_option(
    option_data: SpecOptionCreate,
    spec_id: int = Path(..., description="规格ID"),
    spec_option_service: SpecOptionService = Depends(get_spec_option_service),
    current_user: User = Depends(get_current_active_user),
    _: bool = Depends(lambda: has_permission("spec:create"))
) -> Result[SpecOptionResponse]:
    """创建规格选项 (管理端)"""
    # 设置规格ID
    option_data.spec_id = spec_id
    result = await spec_option_service.create_spec_option(params=option_data)
    return result

# === 客户端路由 (Client Routes) ===

@router.get("/specs", response_model=Result[SpecListResponse])
@handle_route_errors(SPEC_ERROR_MAPPING)
async def list_specs(
    params: PageParams = Depends(),
    category_id: Optional[int] = Query(None, description="分类ID", alias="categoryId"),
    spec_service: SpecService = Depends(get_spec_service)
) -> Result[SpecListResponse]:
    """获取规格列表 (客户端)"""
    result = await spec_service.get_specs_list(
        page_num=params.page_num,
        page_size=params.page_size,
        status="active",
        category_id=category_id
    )
    return result

@router.get("/specs/{spec_id}/options", response_model=Result[SpecOptionListResponse])
@handle_route_errors(SPEC_ERROR_MAPPING)
async def list_spec_options(
    spec_id: int = Path(..., description="规格ID"),
    params: PageParams = Depends(),
    spec_option_service: SpecOptionService = Depends(get_spec_option_service)
) -> Result[SpecOptionListResponse]:
    """获取规格选项列表 (客户端)"""
    result = await spec_option_service.get_options_by_spec(
        spec_id=spec_id,
        page_num=params.page_num,
        page_size=params.page_size
    )
    return result
