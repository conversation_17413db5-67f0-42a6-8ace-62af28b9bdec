from typing import List, Optional

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select

from svc.apps.products.models.spec import Spec, SpecOption
from svc.core.repositories.base import BaseRepository


class SpecRepository(BaseRepository[Spec, None, None]):
    """
    规格仓库，复用BaseRepository，暴露适合服务层调用的接口
    """
    def __init__(self, db: AsyncSession):
        super().__init__(db, Spec)

    async def get_specs_by_category(self, category_id: int) -> List[Spec]:
        """按分类ID查找所有规格"""
        stmt = select(Spec).join(Spec.categories).where(
            Spec.deleted_at.is_(None),
            Spec.status == "active",
            Spec.categories.any(id=category_id)
        )
        result = await self.db.execute(stmt)
        return result.scalars().all()

    async def get_active_specs(self) -> List[Spec]:
        """获取所有活跃的规格"""
        stmt = select(Spec).where(
            Spec.deleted_at.is_(None),
            Spec.status == "active"
        ).order_by(Spec.sort_order.asc(), Spec.created_at.desc())
        result = await self.db.execute(stmt)
        return result.scalars().all()


class SpecOptionRepository(BaseRepository[SpecOption, None, None]):
    """
    规格选项仓库，复用BaseRepository，暴露适合服务层调用的接口
    """
    def __init__(self, db: AsyncSession):
        super().__init__(db, SpecOption)

    async def get_options_by_spec(self, spec_id: int) -> List[SpecOption]:
        """查找某规格下所有选项"""
        stmt = select(SpecOption).where(
            SpecOption.spec_id == spec_id,
            SpecOption.deleted_at.is_(None),
            SpecOption.status == "active"
        ).order_by(SpecOption.sort_order.asc(), SpecOption.created_at.desc())
        result = await self.db.execute(stmt)
        return result.scalars().all()

    async def get_active_options(self) -> List[SpecOption]:
        """获取所有活跃的规格选项"""
        stmt = select(SpecOption).where(
            SpecOption.deleted_at.is_(None),
            SpecOption.status == "active"
        ).order_by(SpecOption.sort_order.asc(), SpecOption.created_at.desc())
        result = await self.db.execute(stmt)
        return result.scalars().all()

    async def get_options_by_ids(self, option_ids: List[int]) -> List[SpecOption]:
        """根据ID列表获取规格选项"""
        stmt = select(SpecOption).where(
            SpecOption.id.in_(option_ids),
            SpecOption.deleted_at.is_(None),
            SpecOption.status == "active"
        )
        result = await self.db.execute(stmt)
        return result.scalars().all()

    async def check_option_exists(self, spec_id: int, value: str, exclude_id: Optional[int] = None) -> bool:
        """检查规格选项值是否已存在"""
        stmt = select(SpecOption).where(
            SpecOption.spec_id == spec_id,
            SpecOption.value == value,
            SpecOption.deleted_at.is_(None)
        )
        if exclude_id:
            stmt = stmt.where(SpecOption.id != exclude_id)

        result = await self.db.execute(stmt)
        return result.scalar() is not None