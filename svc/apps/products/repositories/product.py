"""
商品数据访问层。
负责商品模型的数据库访问操作，实现数据访问与业务逻辑分离。
"""

from typing import Any, Dict, List, Optional, Tuple, Union

from sqlalchemy import and_, asc, desc, func, or_, update
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy.orm import selectinload

from svc.apps.albums.models.album import Album
from svc.apps.products.models.category import Category
from svc.apps.products.models.product import Product, ProductStatus
from svc.apps.products.schemas.product import ProductCreate, ProductUpdate
from svc.core.repositories import BaseRepository
from svc.core.utils.datetime_utils import get_utc_now_without_tzinfo


class ProductRepository(BaseRepository[Product, ProductCreate, ProductUpdate]):
    """商品仓库类，提供商品数据访问方法
    
    该仓库类实现了Product模型的数据访问操作，
    包括商品的基本CRUD操作以及特定的数据查询和统计功能。
    """
    
    def __init__(self, db: AsyncSession):
        """初始化商品仓库"""
        super().__init__(db, Product)
    
    async def create_with_album(self, product_data: ProductCreate, album_id: int) -> Product:
        """创建商品并关联图册ID"""
        new_product = Product(**product_data.model_dump(), album_id=album_id)
        self.db.add(new_product)
        await self.db.flush()
        await self.db.refresh(new_product)
        return new_product

    async def get_products_by_category(self, category_id: int) -> List[Product]:
        """按分类ID查找所有商品"""
        stmt = select(Product).where(
            Product.category_id == category_id,
            Product.deleted_at.is_(None),
            Product.status == "active"
        )
        result = await self.db.execute(stmt)
        return result.scalars().all()

    async def get_featured(self) -> List[Product]:
        """查找所有推荐商品"""
        stmt = select(Product).where(
            Product.is_featured == True,
            Product.deleted_at.is_(None),
            Product.status == "active"
        )
        result = await self.db.execute(stmt)
        return result.scalars().all()
    
    async def increment_view_count(self, product_id: int) -> Optional[Product]:
        """增加商品浏览次数
        
        Args:
            product_id: 商品ID
            
        Returns:
            Optional[Product]: 更新后的商品对象，不存在则返回None
        """
        product = await self.get_by_id(product_id)
        if not product:
            return None
            
        update_data = {"view_count": product.view_count + 1}
        return await self.update(product, update_data)
    
    async def increment_sales_count(self, product_id: int, quantity: int = 1) -> Optional[Product]:
        """增加商品销售次数
        
        Args:
            product_id: 商品ID
            quantity: 销售数量
            
        Returns:
            Optional[Product]: 更新后的商品对象，不存在则返回None
        """
        product = await self.get_by_id(product_id)
        if not product:
            return None
            
        update_data = {"sales_count": product.sales_count + quantity}
        return await self.update(product, update_data)
    
    async def update_stock_quantity(
        self, 
        product_id: int, 
        quantity_change: int
    ) -> Optional[Product]:
        """更新商品库存数量
        
        Args:
            product_id: 商品ID
            quantity_change: 库存变化量（正数为增加，负数为减少）
            
        Returns:
            Optional[Product]: 更新后的商品对象，不存在则返回None
        """
        product = await self.get_by_id(product_id)
        if not product:
            return None
            
        new_quantity = max(0, product.stock_quantity + quantity_change)
        update_data = {"stock_quantity": new_quantity}
        return await self.update(product, update_data)

    async def get_by_id(self, id: int) -> Optional[Product]:
        query = select(self.model).where(self.model.id == id).options(
            selectinload(self.model.album).selectinload(Album.cover_image)
        )
        result = await self.db.execute(query)
        return result.scalars().first()

    async def get_products(
        self,
        skip: int = 0,
        limit: int = 10,
        status: Optional[str] = None,
        category_id: Optional[int] = None,
        is_featured: Optional[bool] = None,
        search_term: Optional[str] = None,
        min_price: Optional[float] = None,
        max_price: Optional[float] = None,
        order_by: str = "created_at",
        order_desc: bool = True
    ) -> Tuple[List[Product], int]:
        """获取商品列表及总数

        Args:
            skip: 跳过记录数
            limit: 返回记录数
            status: 商品状态筛选
            category_id: 分类ID筛选
            is_featured: 是否推荐商品筛选
            search_term: 搜索关键词
            min_price: 最低价格筛选
            max_price: 最高价格筛选
            order_by: 排序字段
            order_desc: 是否降序排序

        Returns:
            Tuple[List[Product], int]: 商品列表和总记录数
        """
        # 构建查询条件
        filters = {}
        if status:
            filters["status"] = status
        if category_id:
            filters["category_id"] = category_id
        if is_featured is not None:
            filters["is_featured"] = is_featured

        # 构建高级过滤条件
        advanced_filters = {}
        if search_term:
            advanced_filters["name__icontains"] = search_term
        if min_price is not None:
            advanced_filters["price__gte"] = min_price
        if max_price is not None:
            advanced_filters["price__lte"] = max_price

        # 使用BaseRepository的get_paginated方法
        products, total = await self.get_paginated(
            page_num=(skip // limit) + 1,
            page_size=limit,
            order_by=order_by,
            order_direction="desc" if order_desc else "asc",
            filters=advanced_filters,
            **filters
        )

        return products, total


