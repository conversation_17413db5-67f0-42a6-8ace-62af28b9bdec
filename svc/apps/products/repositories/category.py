"""
分类数据访问层。
负责分类模型的数据库访问操作，实现数据访问与业务逻辑分离。
"""

from typing import Optional, List, Tuple, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import and_, desc, asc, func, or_

from svc.core.repositories import BaseRepository
from svc.apps.products.models.category import Category, CategoryStatus
from svc.apps.products.models.product import Product
from svc.apps.products.schemas.category import CategoryCreate, CategoryUpdate

class CategoryRepository(BaseRepository[Category, CategoryCreate, CategoryUpdate]):
    """分类仓库类，提供分类数据访问方法
    
    该仓库类实现了Category模型的数据访问操作，
    包括分类的基本CRUD操作以及特定的数据查询和统计功能。
    """
    
    def __init__(self, db: AsyncSession):
        """初始化分类仓库"""
        super().__init__(db, Category)
    
    async def get_by_slug(self, slug: str) -> Optional[Category]:
        """通过别名获取分类
        
        Args:
            slug: 分类别名
            
        Returns:
            Optional[Category]: 分类对象，不存在则返回None
        """
        return await self.get_one(slug=slug)
    
    async def get_by_name(self, name: str) -> Optional[Category]:
        """通过名称获取分类
        
        Args:
            name: 分类名称
            
        Returns:
            Optional[Category]: 分类对象，不存在则返回None
        """
        return await self.get_one(name=name)
    
    async def get_categories(
        self,
        skip: int = 0,
        limit: int = 10,
        status: Optional[str] = None,
        parent_id: Optional[int] = None,
        level: Optional[int] = None,
        is_featured: Optional[bool] = None,
        search_term: Optional[str] = None,
        order_by: str = "sort_order",
        order_desc: bool = False
    ) -> Tuple[List[Category], int]:
        """获取分类列表及总数
        
        Args:
            skip: 跳过记录数
            limit: 返回记录数
            status: 分类状态筛选
            parent_id: 父分类ID筛选
            level: 分类层级筛选
            is_featured: 是否推荐分类筛选
            search_term: 搜索关键词
            order_by: 排序字段
            order_desc: 是否降序排序
            
        Returns:
            Tuple[List[Category], int]: 分类列表和总记录数
        """
        filters = {}
        if status:
            filters["status"] = status
        if parent_id is not None:
            filters["parent_id"] = parent_id
        if level is not None:
            filters["level"] = level
        if is_featured is not None:
            filters["is_featured"] = is_featured
            
        # 使用基类的方法
        if search_term:
            # 包含搜索条件时，需要自定义查询
            query = select(self.model)
            
            # 添加过滤条件
            conditions = []
            if status:
                conditions.append(self.model.status == status)
            if parent_id is not None:
                conditions.append(self.model.parent_id == parent_id)
            if level is not None:
                conditions.append(self.model.level == level)
            if is_featured is not None:
                conditions.append(self.model.is_featured == is_featured)
            
            # 添加搜索条件
            if search_term:
                conditions.append(
                    or_(
                        self.model.name.ilike(f"%{search_term}%"),
                        self.model.description.ilike(f"%{search_term}%"),
                        self.model.slug.ilike(f"%{search_term}%")
                    )
                )
            
            if conditions:
                query = query.where(and_(*conditions))
            
            # 添加排序
            if order_by:
                if hasattr(self.model, order_by):
                    order_column = getattr(self.model, order_by)
                    if order_desc:
                        query = query.order_by(desc(order_column))
                    else:
                        query = query.order_by(asc(order_column))
            
            # 添加分页
            query = query.offset(skip).limit(limit)
            result = await self.db.execute(query)
            categories = result.scalars().all()
            
            # 获取总数的查询
            count_query = select(func.count()).select_from(self.model)
            if conditions:
                count_query = count_query.where(and_(*conditions))
            count_result = await self.db.execute(count_query)
            total = count_result.scalar() or 0
            
            return categories, total
        else:
            # 无搜索条件时，直接使用get_list获取列表和count获取总数
            items = await self.get_list(
                skip=skip,
                limit=limit,
                order_by=order_by,
                order_direction="desc" if order_desc else "asc",
                **filters
            )
            total = await self.count(**filters)
            return items, total
    
    async def get_root_categories(
        self, 
        limit: int = 10, 
        skip: int = 0
    ) -> Tuple[List[Category], int]:
        """获取所有根分类（分页）
        
        Args:
            limit: 返回结果数量限制
            skip: 结果偏移量
            
        Returns:
            Tuple[List[Category], int]: 分类列表和总数
        """
        conditions = [
            self.model.status == CategoryStatus.ACTIVE,
            self.model.parent_id.is_(None)
        ]
        
        # Get paginated list
        query = select(self.model).where(
            *conditions
        ).order_by(asc(self.model.sort_order)).offset(skip).limit(limit)
        result = await self.db.execute(query)
        categories = result.scalars().all()
        
        # Get total count
        count_query = select(func.count()).select_from(self.model).where(*conditions)
        count_result = await self.db.execute(count_query)
        total = count_result.scalar() or 0
        
        return categories, total
    
    async def get_children_categories(
        self, 
        parent_id: int,
        limit: int = 10, 
        skip: int = 0
    ) -> Tuple[List[Category], int]:
        """获取指定分类的子分类
        
        Args:
            parent_id: 父分类ID
            limit: 返回结果数量限制
            skip: 结果偏移量
            
        Returns:
            Tuple[List[Category], int]: 分类列表和总数
        """
        conditions = [
            self.model.status == CategoryStatus.ACTIVE,
            self.model.parent_id == parent_id
        ]
        
        # Get paginated list
        query = select(self.model).where(
            *conditions
        ).order_by(asc(self.model.sort_order)).offset(skip).limit(limit)
        result = await self.db.execute(query)
        categories = result.scalars().all()
        
        # Get total count
        count_query = select(func.count()).select_from(self.model).where(*conditions)
        count_result = await self.db.execute(count_query)
        total = count_result.scalar() or 0
        
        return categories, total
    
    async def get_featured_categories(
        self, 
        limit: int = 10, 
        skip: int = 0
    ) -> Tuple[List[Category], int]:
        """获取所有推荐分类（分页）
        
        Args:
            limit: 返回结果数量限制
            skip: 结果偏移量
            
        Returns:
            Tuple[List[Category], int]: 分类列表和总数
        """
        conditions = [
            self.model.status == CategoryStatus.ACTIVE,
            self.model.is_featured == True
        ]
        
        # Get paginated list
        query = select(self.model).where(
            *conditions
        ).order_by(asc(self.model.sort_order)).offset(skip).limit(limit)
        result = await self.db.execute(query)
        categories = result.scalars().all()
        
        # Get total count
        count_query = select(func.count()).select_from(self.model).where(*conditions)
        count_result = await self.db.execute(count_query)
        total = count_result.scalar() or 0
        
        return categories, total
    
    async def get_category_tree(self, parent_id: Optional[int] = None) -> List[Category]:
        """获取分类树结构
        
        Args:
            parent_id: 父分类ID，为None时获取完整树
            
        Returns:
            List[Category]: 分类树列表
        """
        if parent_id is None:
            # 获取根分类
            query = select(self.model).where(
                self.model.status == CategoryStatus.ACTIVE,
                self.model.parent_id.is_(None)
            ).order_by(asc(self.model.sort_order))
        else:
            # 获取指定父分类的子分类
            query = select(self.model).where(
                self.model.status == CategoryStatus.ACTIVE,
                self.model.parent_id == parent_id
            ).order_by(asc(self.model.sort_order))
        
        result = await self.db.execute(query)
        return result.scalars().all()
    
    async def update_product_count(self, category_id: int) -> Optional[Category]:
        """更新分类的商品数量
        
        Args:
            category_id: 分类ID
            
        Returns:
            Optional[Category]: 更新后的分类对象，不存在则返回None
        """
        category = await self.get_by_id(category_id)
        if not category:
            return None
        
        # 计算该分类下的商品数量
        count_query = select(func.count()).select_from(Product).where(
            Product.category_id == category_id,
            Product.status != "deleted"
        )
        count_result = await self.db.execute(count_query)
        product_count = count_result.scalar() or 0
        
        update_data = {"product_count": product_count}
        return await self.update(category, update_data)
    
    async def get_category_path(self, category_id: int) -> List[Category]:
        """获取分类的完整路径
        
        Args:
            category_id: 分类ID
            
        Returns:
            List[Category]: 从根分类到当前分类的路径列表
        """
        path = []
        current_category = await self.get_by_id(category_id)
        
        while current_category:
            path.insert(0, current_category)
            if current_category.parent_id:
                current_category = await self.get_by_id(current_category.parent_id)
            else:
                break
        
        return path

    async def get_children(self, parent_id: int) -> List[Category]:
        """查找某父分类下所有子分类"""
        stmt = select(Category).where(
            Category.parent_id == parent_id,
            Category.deleted_at.is_(None),
            Category.status == "active"
        )
        result = await self.db.execute(stmt)
        return result.scalars().all()

    async def get_featured(self) -> List[Category]:
        """查找所有推荐分类"""
        stmt = select(Category).where(
            Category.is_featured == True,
            Category.deleted_at.is_(None),
            Category.status == "active"
        )
        result = await self.db.execute(stmt)
        return result.scalars().all()
