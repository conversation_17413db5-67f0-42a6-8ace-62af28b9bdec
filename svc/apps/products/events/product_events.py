"""
商品事件处理器

该模块处理商品相关的事件，包括：
- 商品创建事件
- 商品更新事件
- 商品删除事件
- 商品状态变更事件
"""

import logging
from typing import Any, Dict

from fastapi_events.handlers.local import local_handler
from fastapi_events.typing import Event

from svc.apps.products.repositories.category import CategoryRepository
from svc.core.database.utils import get_session_for_script

logger = logging.getLogger(__name__)

class ProductEventHandler:
    """商品事件处理器类"""
    
    @staticmethod
    @local_handler.register(event_name="products:product:created")
    async def handle_product_created(event: Event):
        """处理商品创建事件
        
        当商品被创建时触发，执行以下操作：
        1. 更新分类的商品数量
        2. 发送通知
        3. 记录日志
        
        Args:
            event: 事件对象，包含商品数据
        """
        try:
            logger.info(f"处理商品创建事件: {event}")
            
            product_data = event[1]  # 获取事件数据
            product_id = product_data.get("id")
            category_id = product_data.get("category_id")
            product_name = product_data.get("name")
            
            logger.info(f"商品已创建: ID={product_id}, 名称={product_name}, 分类ID={category_id}")
            
            # 如果商品有分类，更新分类的商品数量
            if category_id:
                async with get_session_for_script() as db:
                    category_repo = CategoryRepository(db)
                    await category_repo.update_product_count(category_id)
                    await db.commit()
                    logger.info(f"已更新分类商品数量: category_id={category_id}")
            
            # 这里可以添加其他业务逻辑，如：
            # - 发送邮件通知
            # - 更新搜索索引
            # - 同步到其他系统
            
        except Exception as e:
            logger.error(f"处理商品创建事件失败: {str(e)}", exc_info=True)
    
    @staticmethod
    @local_handler.register(event_name="products:product:updated")
    async def handle_product_updated(event: Event):
        """处理商品更新事件
        
        当商品被更新时触发，执行以下操作：
        1. 检查分类是否变更，更新相关分类的商品数量
        2. 清除相关缓存
        3. 记录日志
        
        Args:
            event: 事件对象，包含商品数据和更新字段
        """
        try:
            logger.info(f"处理商品更新事件: {event}")
            
            product_data = event[1]  # 获取事件数据
            product_id = product_data.get("id")
            updated_fields = product_data.get("updated_fields", [])
            product_name = product_data.get("name")
            
            logger.info(f"商品已更新: ID={product_id}, 名称={product_name}, 更新字段={updated_fields}")
            
            # 如果分类发生变更，需要更新相关分类的商品数量
            if "category_id" in updated_fields:
                # 这里需要获取原分类ID和新分类ID，然后分别更新
                # 为简化示例，这里只记录日志
                logger.info(f"商品分类已变更: product_id={product_id}")
            
            # 如果状态发生变更，可能需要特殊处理
            if "status" in updated_fields:
                status = product_data.get("status")
                logger.info(f"商品状态已变更: product_id={product_id}, status={status}")
            
            # 这里可以添加其他业务逻辑，如：
            # - 清除缓存
            # - 更新搜索索引
            # - 同步到其他系统
            
        except Exception as e:
            logger.error(f"处理商品更新事件失败: {str(e)}", exc_info=True)
    
    @staticmethod
    @local_handler.register(event_name="products:product:deleted")
    async def handle_product_deleted(event: Event):
        """处理商品删除事件
        
        当商品被删除时触发，执行以下操作：
        1. 更新分类的商品数量
        2. 清除相关缓存
        3. 记录日志
        
        Args:
            event: 事件对象，包含被删除的商品数据
        """
        try:
            logger.info(f"处理商品删除事件: {event}")
            
            product_data = event[1]  # 获取事件数据
            product_id = product_data.get("id")
            category_id = product_data.get("category_id")
            product_name = product_data.get("name")
            
            logger.info(f"商品已删除: ID={product_id}, 名称={product_name}, 分类ID={category_id}")
            
            # 如果商品有分类，更新分类的商品数量
            if category_id:
                async with get_session_for_script() as db:
                    category_repo = CategoryRepository(db)
                    await category_repo.update_product_count(category_id)
                    await db.commit()
                    logger.info(f"已更新分类商品数量: category_id={category_id}")
            
            # 这里可以添加其他业务逻辑，如：
            # - 清除缓存
            # - 从搜索索引中删除
            # - 同步到其他系统
            
        except Exception as e:
            logger.error(f"处理商品删除事件失败: {str(e)}", exc_info=True)
    
    @staticmethod
    @local_handler.register(event_name="products:product:status_changed")
    async def handle_product_status_changed(event: Event):
        """处理商品状态变更事件
        
        当商品状态发生变更时触发，执行以下操作：
        1. 根据状态变更执行相应逻辑
        2. 发送通知
        3. 记录日志
        
        Args:
            event: 事件对象，包含商品数据和状态变更信息
        """
        try:
            logger.info(f"处理商品状态变更事件: {event}")
            
            product_data = event[1]  # 获取事件数据
            product_id = product_data.get("id")
            old_status = product_data.get("old_status")
            new_status = product_data.get("new_status")
            product_name = product_data.get("name")
            
            logger.info(f"商品状态已变更: ID={product_id}, 名称={product_name}, {old_status} -> {new_status}")
            
            # 根据状态变更执行不同的业务逻辑
            if new_status == "active":
                logger.info(f"商品已上架: product_id={product_id}")
                # 可以发送上架通知、更新搜索索引等
            elif new_status == "inactive":
                logger.info(f"商品已下架: product_id={product_id}")
                # 可以发送下架通知、从搜索索引中移除等
            elif new_status == "deleted":
                logger.info(f"商品已删除: product_id={product_id}")
                # 执行删除相关的清理工作
            
        except Exception as e:
            logger.error(f"处理商品状态变更事件失败: {str(e)}", exc_info=True)
