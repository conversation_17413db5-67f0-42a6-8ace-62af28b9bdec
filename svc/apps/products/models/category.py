"""
分类模型，支持与规格多对多关联
"""
import enum
from typing import Dict, Optional

from sqlalchemy import (JSON, BigInteger, Boolean, Column, DateTime,
                        ForeignKey, Integer, String, Table, Text)
from sqlalchemy.orm import relationship

from svc.core.models.base import Base
from svc.core.utils.datetime_utils import get_utc_now_without_tzinfo


class CategoryStatus(str, enum.Enum):
    ACTIVE = "active"
    INACTIVE = "inactive"
    DELETED = "deleted"

# 多对多中间表：分类-规格
category_spec_table = Table(
    "category_specs",
    Base.metadata,
    Column("category_id", BigInteger, ForeignKey("categories.id"), primary_key=True),
    Column("spec_id", BigInteger, ForeignKey("specs.id"), primary_key=True)
)

class Category(Base):
    """
    分类模型，支持与规格多对多关联
    """
    __tablename__ = "categories"
    
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    name = Column(String(200), nullable=False, comment="分类名称")
    description = Column(Text, nullable=True, comment="分类描述")
    slug = Column(String(200), unique=True, nullable=True, comment="URL别名")
    parent_id = Column(BigInteger, ForeignKey("categories.id"), nullable=True, comment="父分类ID")
    level = Column(Integer, nullable=False, default=1, comment="分类层级")
    status = Column(String(20), nullable=False, default=CategoryStatus.ACTIVE, comment="分类状态")
    sort_order = Column(Integer, nullable=False, default=0, comment="排序权重")
    is_featured = Column(Boolean, nullable=False, default=False, comment="是否推荐")
    meta_data = Column(JSON, default=dict, nullable=False, comment="元数据")
    created_at = Column(DateTime, nullable=False, default=get_utc_now_without_tzinfo)
    updated_at = Column(DateTime, nullable=False, default=get_utc_now_without_tzinfo, onupdate=get_utc_now_without_tzinfo)
    deleted_at = Column(DateTime, nullable=True, comment="删除时间（软删除）")
    
    # 关系
    parent = relationship("Category", remote_side=[id], backref="children", lazy="selectin", foreign_keys=[parent_id])
    specs = relationship("Spec", secondary=category_spec_table, back_populates="categories", lazy="selectin")
    products = relationship("Product", back_populates="category", lazy="selectin")
    
    def is_active(self) -> bool:
        return self.status == CategoryStatus.ACTIVE
        
    def to_dict(self) -> Dict:
        return {
            "id": self.id,
            "name": self.name,
            "description": self.description,
            "slug": self.slug,
            "parent_id": self.parent_id,
            "level": self.level,
            "status": self.status,
            "sort_order": self.sort_order,
            "is_featured": self.is_featured,
            "meta_data": self.meta_data,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "deleted_at": self.deleted_at.isoformat() if self.deleted_at else None
        }
