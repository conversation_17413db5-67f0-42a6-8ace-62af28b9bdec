from typing import Any, Dict, Optional

from fastapi_events.dispatcher import dispatch
from redis.asyncio import Redis
from sqlalchemy.ext.asyncio import AsyncSession

from svc.apps.albums.schemas.album import AlbumCreate, UserAlbumResponse
from svc.apps.albums.services.album import AlbumService
from svc.apps.shops.models.shop import Shop, ShopStatus
from svc.apps.shops.repositories.shop import ShopRepository
from svc.apps.shops.schemas.shop import (GetShopsParams, PaginatedShopResponse,
                                         ShopCreate, ShopResponse, ShopUpdate,
                                         UserShopListResponse,
                                         UserShopResponse)
from svc.core.exceptions.error_codes import ErrorCode
from svc.core.models.result import Result
from svc.core.services.base import BaseService
from svc.core.services.mixins.batch_operation import BatchOperationMixin
from svc.core.services.mixins.cache import CacheMixin
from svc.core.services.mixins.error_result import ErrorResultMixin
from svc.core.utils.datetime_utils import get_utc_now_without_tzinfo

# 缓存配置 (可以根据门店模块的特性调整)
CACHE_TTL = 3600  # 默认缓存过期时间（1小时）
CACHE_TTL_SHORT = 300  # 短期缓存过期时间（5分钟）

class ShopService(BaseService, ErrorResultMixin, BatchOperationMixin, CacheMixin):
    """门店服务类，提供门店的创建、查询和管理功能

    该服务类负责：
    1. 门店的创建和管理
    2. 门店状态更新
    3. 门店数据查询

    服务类依赖StoreRepository进行数据访问，
    实现业务逻辑与数据访问的分离。
    """

    resource_type = "shop"

    def __init__(
        self,
        redis: Optional[Redis] = None,
        shop_repo: Optional[ShopRepository] = None,
        album_service: Optional[AlbumService] = None
    ):
        """初始化门店服务

        Args:
            redis: Redis客户端，用于缓存
            shop_repo: 门店仓库实例，不提供则创建新实例
            album_service: 图册服务实例
        """
        BaseService.__init__(self, redis)
        self.shop_repo = shop_repo
        self.album_service = album_service

    async def _ensure_repo(self, db: AsyncSession):
        """确保仓库实例已初始化"""
        if not self.shop_repo:
            self.shop_repo = ShopRepository(db)
        elif self.shop_repo.db != db: # 如果传入的db与仓库的db不同，则重新初始化
            self.shop_repo = ShopRepository(db)

    async def get_resource_by_id(self, shop_id: int) -> Optional[Shop]:
        """获取指定ID的门店资源

        Args:
            shop_id: 门店ID

        Returns:
            Optional[Shop]: 门店对象，不存在时返回None
        """
        # 注意：BaseService 中的 get_resource_by_id 依赖 self.repository
        # 这里我们确保 self.shop_repo 被正确设置和使用
        if not self.shop_repo:
            # 这是一个潜在问题，如果服务在没有 db session 的情况下被调用
            # 或者仓库没有被正确注入。实际应用中应通过依赖注入解决。
            self.logger.error("ShopRepository not initialized before calling get_resource_by_id")
            return None
        return await self.shop_repo.get_by_id(shop_id)

    async def get_shop(self, shop_id: int, user_mode: bool = False) -> Result[ShopResponse]:
        """获取门店信息

        Args:
            shop_id: 门店ID

        Returns:
            Result[ShopResponse]: 结果对象
        """
        try:
            self.logger.info(f"获取门店: id={shop_id}")

            cache_key = self._get_resource_cache_key(shop_id)
            cached_shop = await self.get_cached_resource(
                cache_key,
                lambda data: ShopResponse.model_validate(data)
            )
            if cached_shop:
                self.logger.debug(f"从缓存获取到门店: id={shop_id}")
                return self.create_success_result(cached_shop)

            shop = await self.get_resource_by_id(shop_id)
            if not shop:
                self.logger.warning(f"门店不存在: id={shop_id}")
                return self.resource_not_found_result(shop_id)
            shop_response = ShopResponse.model_validate(shop) 
            if user_mode:
                # 详情接口：返回album，image_url可选
                album = None
                image_url = None
                if shop.album:
                    # 取封面URL
                    image_url = shop.album.cover_image.url if getattr(shop.album, 'cover_image', None) else None
                    # 组装精简图册
                    images = [img.url for img in getattr(shop.album, 'images', [])] if hasattr(shop.album, 'images') else []
                    album = UserAlbumResponse(
                        id=shop.album.id,
                        name=shop.album.name,
                        images=images
                    )
                user_shop_response = UserShopResponse(
                    id=shop.id,
                    name=shop.name,
                    description=shop.description,
                    address_line1=shop.address_line1,
                    city=shop.city,
                    country=shop.country,
                    phone_number=getattr(shop, 'phone_number', None),
                    website=getattr(shop, 'website', None),
                    opening_hours=getattr(shop, 'opening_hours', None),
                    is_franchise=shop.is_franchise,
                    latitude=getattr(shop, 'latitude', None),
                    longitude=getattr(shop, 'longitude', None),
                    extra_info=getattr(shop, 'extra_info', None),
                    image_url=image_url,
                    album=album
                )
                await self.cache_resource(cache_key, user_shop_response, expire=CACHE_TTL)
                self.logger.debug(f"门店信息已缓存: id={shop_id}")
                return self.create_success_result(user_shop_response)
            # 使用 from_attributes=True
            await self.cache_resource(cache_key, shop_response, expire=CACHE_TTL)
            self.logger.debug(f"门店信息已缓存: id={shop_id}")

            return self.create_success_result(shop_response)
        except Exception as e:
            self.logger.error(f"获取门店失败: id={shop_id}, 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.INTERNAL_ERROR, # 更通用的错误码
                error_message=f"获取门店失败: {str(e)}"
            )

    async def get_shops(self, params: GetShopsParams, user_mode: bool = False):
        """获取门店列表，user_mode=True时返回用户端精简schema"""
        try:
            self.logger.info(f"获取门店列表: page={params.page_num}, size={params.page_size}, params={params.model_dump_json(exclude_none=True)}")
            shops, total_count = await self.shop_repo.get_shops(params)
            pages = (total_count + params.page_size - 1) // params.page_size if params.page_size else 1
            if user_mode:
                # 列表接口：只返回image_url，不返回album
                user_shops = []
                for shop in shops:
                    image_url = None
                    if shop.album and getattr(shop.album, 'cover_image', None):
                        image_url = shop.album.cover_image.url
                    user_shops.append(UserShopResponse(
                        id=shop.id,
                        name=shop.name,
                        description=shop.description,
                        address_line1=shop.address_line1,
                        city=shop.city,
                        country=shop.country,
                        phone_number=getattr(shop, 'phone_number', None),
                        website=getattr(shop, 'website', None),
                        opening_hours=getattr(shop, 'opening_hours', None),
                        is_franchise=shop.is_franchise,
                        latitude=getattr(shop, 'latitude', None),
                        longitude=getattr(shop, 'longitude', None),
                        extra_info=getattr(shop, 'extra_info', None),
                        image_url=image_url,
                        album=None
                    ))
                return self.create_success_result(
                    UserShopListResponse(
                        items=user_shops,
                        total=total_count,
                        page_num=params.page_num,
                        page_size=params.page_size,
                        page_count=pages
                    )
                )
            shop_responses = [ShopResponse.model_validate(shop) for shop in shops]
            return self.create_success_result(
                PaginatedShopResponse(
                    items=shop_responses,
                    total=total_count,
                    page_num=params.page_num,
                    page_size=params.page_size,
                    page_count=pages
                )
            )
        except Exception as e:
            self.logger.error(f"获取门店列表失败: {str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.INTERNAL_ERROR,
                error_message=f"获取门店列表失败: {str(e)}"
            )

    async def create_shop(self, shop_data: ShopCreate) -> Result[ShopResponse]:
        """创建新门店, 并为其关联一个新图册"""
        try:
            self.logger.info(f"创建新门店: name={shop_data.name}")

            # 检查门店是否已存在 (例如，根据名称和城市)
            existing_shop = await self.shop_repo.get_by_name_and_city(shop_data.name, shop_data.city)
            if existing_shop:
                self.logger.warning(f"门店已存在: name={shop_data.name}, city={shop_data.city}")
                return self.create_error_result(
                    error_code=ErrorCode.RESOURCE_EXISTS,
                    error_message=f"名为 '{shop_data.name}' 的门店在城市 '{shop_data.city}' 已存在"
                )
            
            # 1. 创建图册
            album_create_data = AlbumCreate(name=f"店铺图册 - {shop_data.name}", tags=["shop"])
            album_result = await self.album_service.create_album(album_create_data)
            if not album_result.success:
                self.logger.error(f"为新店铺创建图册失败: {album_result.error_message}")
                return self.create_error_result(
                    error_code=ErrorCode.CREATE_FAILED,
                    error_message=f"创建关联图册失败: {album_result.error_message}"
                )
            
            created_album = album_result.data

            # 2. 创建门店并关联图册
            new_shop = await self.shop_repo.create_with_album(shop_data, created_album.id)
            shop_response = ShopResponse.model_validate(new_shop)

            # 创建成功后可以清除相关列表缓存，或更精细化处理
            # await self.clear_cache_by_prefix(f"{self.resource_type}:list:")
            dispatch(f"{self.resource_type}_created", payload=shop_response.model_dump())
            self.logger.info(f"门店创建成功: id={new_shop.id}, name={new_shop.name}, album_id={new_shop.album_id}")
            return self.create_success_result(shop_response, status_code=201) # HTTP 201 Created

        except Exception as e:
            self.logger.error(f"创建门店失败: {str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.CREATE_FAILED,
                error_message=f"创建门店失败: {str(e)}"
            )

    async def update_shop(self, shop_id: int, shop_data: ShopUpdate) -> Result[ShopResponse]:
        """更新门店信息

        Args:
            shop_id: 要更新的门店ID
            shop_data: 门店更新数据模型

        Returns:
            Result[ShopResponse]: 更新后的门店信息
        """
        try:
            self.logger.info(f"更新门店: id={shop_id}")
            shop_to_update = await self.get_resource_by_id(shop_id)
            if not shop_to_update:
                self.logger.warning(f"尝试更新不存在的门店: id={shop_id}")
                return self.resource_not_found_result(shop_id)

            update_data = shop_data.model_dump(exclude_unset=True)
            if not update_data:
                self.logger.warning(f"无有效更新字段: id={shop_id}")
                return self.create_error_result(
                    error_code=ErrorCode.UPDATE_FAILED,
                    error_message="未提供任何需要更新的字段"
                )
            updated_shop = await self.shop_repo.update(shop_to_update, update_data)
            if not updated_shop:
                 # 理论上如果 get_resource_by_id 成功，这里不应该为 None，除非并发删除
                self.logger.error(f"更新门店后未能获取到对象: id={shop_id}")
                return self.resource_not_found_result(shop_id, error_message="更新后门店数据未能获取")

            shop_response = ShopResponse.model_validate(updated_shop)

            await self.delete_cache(self._get_resource_cache_key(shop_id))
            dispatch(f"{self.resource_type}_updated", payload=shop_response.model_dump())
            self.logger.info(f"门店更新成功: id={shop_id}")
            return self.create_success_result(shop_response)

        except Exception as e:
            self.logger.error(f"更新门店失败: id={shop_id}, 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.UPDATE_FAILED,
                error_message=f"更新门店失败: {str(e)}"
            )

    async def delete_shop(self, shop_id: int) -> Result[Dict[str, Any]]:
        """逻辑删除门店，并同步删除关联的图册"""
        try:
            self.logger.info(f"删除门店: id={shop_id}")
            shop_to_delete = await self.get_resource_by_id(shop_id)
            if not shop_to_delete:
                self.logger.warning(f"尝试删除不存在的门店: id={shop_id}")
                return self.resource_not_found_result(shop_id)

            # 1. 逻辑删除关联的图册 (如果存在)
            if shop_to_delete.album_id:
                album_result = await self.album_service.delete_album(shop_to_delete.album_id)
                if not album_result.success:
                    self.logger.error(f"删除门店时，删除关联图册失败: album_id={shop_to_delete.album_id}, error={album_result.error_message}")
                    # 根据业务决定是否要因为图册删除失败而中断门店删除
                    # 这里选择继续删除门店，但记录错误
            
            # 2. 逻辑删除门店
            update_data = ShopUpdate(status=ShopStatus.DELETED).model_dump(exclude_unset=True)
            deleted_shop = await self.shop_repo.update(shop_to_delete, update_data)
            if not deleted_shop:
                self.logger.error(f"逻辑删除门店失败: id={shop_id}")
                return self.create_error_result(
                    error_code=ErrorCode.DELETE_FAILED,
                    error_message="逻辑删除门店失败"
                )

            await self.delete_cache(self._get_resource_cache_key(shop_id))
            dispatch(f"{self.resource_type}_deleted", payload={"id": shop_id, "album_id": shop_to_delete.album_id})
            self.logger.info(f"门店逻辑删除成功: id={shop_id}")

            return self.create_success_result({"id": shop_id, "album_id": shop_to_delete.album_id})
        except Exception as e:
            self.logger.error(f"删除门店失败: id={shop_id}, 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.DELETE_FAILED,
                error_message=f"删除门店失败: {str(e)}"
            )