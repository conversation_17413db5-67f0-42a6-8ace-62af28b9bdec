"""
依赖注入模块
提供营销服务的依赖项和配置函数。
"""
from fastapi import Depends
from redis.asyncio import Redis
from sqlalchemy.ext.asyncio import AsyncSession

from svc.apps.auth import UserService, get_user_service
from svc.apps.marketing.repositories.campaign import CampaignRepository
from svc.apps.marketing.repositories.invitation import InvitationRepository
from svc.apps.marketing.repositories.reward import (RewardRecordRepository,
                                                    RewardStrategyRepository)
from svc.apps.marketing.services.campaign import CampaignService
from svc.apps.marketing.services.invitation import InvitationService
from svc.apps.marketing.services.reward import (RewardRecordService,
                                                RewardStrategyService)
from svc.core.cache.redis import get_redis
from svc.core.database import get_session_for_route
from svc.core.dependencies.auth import configure_auth_dependencies


# 可选：如果有资源类型需要注册到权限系统
def setup_marketing_dependencies():
    """
    设置营销依赖项
    
    注册营销相关的资源类型并配置全局认证依赖项。
    应在应用启动时调用此函数。
    """
    try:
        # 如果有模型需要注册到权限系统，可以导入并添加到这里
        from svc.apps.marketing.models import (Campaign, Invitation,
                                               RewardRecord, RewardStrategy)

        # 配置全局认证依赖项
        configure_auth_dependencies(
            resources={
                # 根据需要添加资源类型
                "campaign": Campaign,
                "invitation": Invitation,
                "reward_strategy": RewardStrategy,
                "reward_record": RewardRecord
            }
        )
    except ImportError:
        # 如果模型未定义或导入失败，记录日志但不中断程序
        import logging
        logger = logging.getLogger(__name__)
        logger.warning("未能注册营销模块资源类型，请检查模型是否正确定义")


async def get_campaign_repository(
    db: AsyncSession = Depends(get_session_for_route)
) -> CampaignRepository:
    """
    提供CampaignRepository实例的依赖
    
    Args:
        db: 数据库会话
        
    Returns:
        CampaignRepository: 活动仓库实例
    """
    return CampaignRepository(db)


async def get_invitation_repository(
    db: AsyncSession = Depends(get_session_for_route)
) -> InvitationRepository:
    """
    提供InvitationRepository实例的依赖
    
    Args:
        db: 数据库会话
        
    Returns:
        InvitationRepository: 邀请仓库实例
    """
    return InvitationRepository(db)


async def get_reward_strategy_repository(
    db: AsyncSession = Depends(get_session_for_route)
) -> RewardStrategyRepository:
    """
    提供RewardStrategyRepository实例的依赖

    Args:
        db: 数据库会话

    Returns:
        RewardStrategyRepository: 奖励策略仓库实例  
    """
    return RewardStrategyRepository(db)


async def get_reward_record_repository(
    db: AsyncSession = Depends(get_session_for_route)
) -> RewardRecordRepository:
    """
    提供RewardRecordRepository实例的依赖

    Args:
        db: 数据库会话

    Returns:        
        RewardRecordRepository: 奖励记录仓库实例
    """
    return RewardRecordRepository(db)


async def get_campaign_service(
    db: AsyncSession = Depends(get_session_for_route),
    redis: Redis = Depends(get_redis),
    campaign_repo: CampaignRepository = Depends(get_campaign_repository)
) -> CampaignService:
    """
    提供CampaignService实例的依赖
    
    Args:
        db: 数据库会话
        redis: Redis客户端
        campaign_repo: 活动仓库实例
        
    Returns:
        CampaignService: 活动服务实例
    """
    service = CampaignService(redis=redis,campaign_repo=campaign_repo)
    return service





async def get_reward_strategy_service(
    redis: Redis = Depends(get_redis),
    reward_strategy_repo: RewardStrategyRepository = Depends(get_reward_strategy_repository)
) -> RewardStrategyService:
    """
    提供RewardStrategyService实例的依赖
    
    Args:
        db: 数据库会话
        redis: Redis客户端
        
    Returns:
        RewardStrategyService: 奖励策略服务实例
    """
    return RewardStrategyService(redis=redis, reward_strategy_repo=reward_strategy_repo)


async def get_reward_record_service(
    redis: Redis = Depends(get_redis),
    reward_record_repo: RewardRecordRepository = Depends(get_reward_record_repository),
    invitation_repo: InvitationRepository = Depends(get_invitation_repository),
    campaign_service: CampaignService = Depends(get_campaign_service),
    strategy_service: RewardStrategyService = Depends(get_reward_strategy_service)
) -> RewardRecordService:
    """
    提供RewardRecordService实例的依赖
    
    Args:
        db: 数据库会话
        redis: Redis客户端
        reward_record_repo: 奖励记录仓库实例
    Returns:
        RewardRecordService: 奖励记录服务实例
    """
    return RewardRecordService(redis=redis, reward_record_repo=reward_record_repo, invitation_repo=invitation_repo, campaign_service=campaign_service, strategy_service=strategy_service)


async def get_invitation_service(
    redis: Redis = Depends(get_redis),
    user_service: UserService = Depends(get_user_service),
    invitation_repo: InvitationRepository = Depends(get_invitation_repository),
    campaign_service: CampaignService = Depends(get_campaign_service),
    reward_record_service: RewardRecordService = Depends(get_reward_record_service)
) -> InvitationService:
    """
    提供InvitationService实例的依赖
    
    Args:
        db: 数据库会话
        redis: Redis客户端
        user_service: 用户服务实例 # 添加文档
        invitation_repo: 邀请仓库实例
        campaign_service: 活动服务实例
        reward_record_service: 奖励记录服务实例 # 确保 RewardRecordService 类型被导入
        
    Returns:
        InvitationService: 邀请服务实例
    """

    return InvitationService(
        user_service=user_service,
        redis=redis, 
        invitation_repo=invitation_repo, 
        campaign_service=campaign_service, 
        reward_record_service=reward_record_service
    )

# 在模块导入时自动调用设置函数
# 注意：如果应用启动顺序有特殊要求，可能需要在应用启动时显式调用此函数
try:
    setup_marketing_dependencies()
except Exception as e:
    import logging
    logger = logging.getLogger(__name__)
    logger.error(f"设置营销依赖项失败: {str(e)}") 