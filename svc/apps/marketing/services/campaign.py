from typing import Any, Dict, Optional

from fastapi_events.dispatcher import dispatch
from redis.asyncio import Redis
from sqlalchemy.ext.asyncio import AsyncSession

from svc.apps.marketing.models.campaign import Campaign, CampaignStatus
from svc.apps.marketing.repositories.campaign import CampaignRepository
from svc.apps.marketing.schemas.campaign import (CampaignCreate,
                                                 CampaignListResponse,
                                                 CampaignResponse,
                                                 CampaignUpdate,
                                                 GetCampaignsParams)
from svc.core.events.event_names import \
    SYSTEM_AUDIT_LOG_RECORDED  # Assuming audit is needed
from svc.core.events.event_names import \
    SYSTEM_CACHE_INVALIDATION_REQUESTED  # Assuming cache invalidation
from svc.core.events.event_names import (MARKETING_CAMPAIGN_CREATED,
                                         MARKETING_CAMPAIGN_DELETED,
                                         MARKETING_CAMPAIGN_STATUS_CHANGED,
                                         MARKETING_CAMPAIGN_UPDATED)
from svc.core.exceptions.error_codes import ErrorCode
from svc.core.models.result import Result
from svc.core.services.base import BaseService
from svc.core.services.mixins.batch_operation import BatchOperationMixin
from svc.core.services.mixins.cache import CacheMixin
from svc.core.services.mixins.error_result import ErrorResultMixin
from svc.core.utils.datetime_utils import get_utc_now_without_tzinfo

# 缓存配置
CACHE_TTL = 3600  # 默认缓存过期时间（1小时）
CACHE_TTL_SHORT = 300  # 短期缓存过期时间（5分钟）
CACHE_TTL_LONG = 86400  # 长期缓存过期时间（24小时）


class CampaignService(BaseService, ErrorResultMixin, BatchOperationMixin, CacheMixin):
    """营销活动服务类，提供活动的创建、查询和管理功能
    
    该服务类负责：
    1. 活动的创建和管理
    2. 活动资格检查
    3. 活动数据统计
    4. 活动状态更新
    
    服务类依赖CampaignRepository进行数据访问，
    实现业务逻辑与数据访问的分离。
    """
    
    # 设置资源类型名称
    resource_type = "campaign"
    
    def __init__(
        self, 
        redis: Optional[Redis] = None, 
        campaign_repo: Optional[CampaignRepository] = None
    ):
        """初始化活动服务
        
        Args:
            db: 数据库会话
            redis: Redis客户端，用于缓存和分布式锁
            campaign_repo: 活动仓库实例，不提供则创建新实例
        """
        BaseService.__init__(self, redis)
        self.campaign_repo = campaign_repo
    
    async def get_resource_by_id(self, campaign_id: int) -> Optional[Campaign]:
        """获取指定ID的活动资源
        
        Args:
            campaign_id: 活动ID
            
        Returns:
            Optional[Campaign]: 活动对象，不存在时返回None
        """
        return await self.campaign_repo.get_by_id( campaign_id)
    
    async def get_campaign(self, campaign_id: int, include_invitations: bool = False, invitation_limit: int = 10) -> Result[CampaignResponse]:
        """获取活动信息
        
        Args:
            campaign_id: 活动ID
            include_invitations: 是否包含邀请记录
            invitation_limit: 返回的邀请记录数量限制
            
        Returns:
            Result[CampaignResponse]: 结果对象
        """
        try:
            self.logger.info(f"获取活动: id={campaign_id}, include_invitations={include_invitations}")
            
            # 先尝试从缓存获取
            if self.redis:
                try:
                    cache_key = self._get_resource_cache_key(campaign_id)
                    cached_campaign = await self.get_cached_resource(
                        cache_key,
                        lambda data: CampaignResponse.model_validate(data)
                    )
                    if cached_campaign:
                        self.logger.debug(f"从缓存获取到活动: id={campaign_id}")
                        return self.create_success_result(cached_campaign)
                except Exception as e:
                    self.logger.warning(f"从缓存获取活动失败: id={campaign_id}, 错误={str(e)}")
            
            # 查询数据库
            if include_invitations:
                # 使用仓库方法获取活动及其邀请记录
                self.logger.debug(f"使用仓库方法获取活动及邀请记录: campaign_id={campaign_id}")
                campaign, invitations = await self.campaign_repo.get_campaign_with_invitations(
                     
                    campaign_id,
                    skip=0, 
                    limit=invitation_limit
                )
                
                if not campaign:
                    self.logger.warning(f"活动不存在: id={campaign_id}")
                    return self.resource_not_found_result(campaign_id)
                    
                # 构建响应数据
                campaign_response = CampaignResponse.model_validate(campaign.to_dict())
                campaign_response.invitations = [invitation.to_dict() for invitation in invitations]
                
                # 缓存活动
                await self._cache_campaign(campaign_id, campaign_response)
                
                return self.create_success_result(campaign_response)
            else:
                # 常规方式获取活动
                campaign = await self.get_resource_by_id(campaign_id)
                if not campaign:
                    self.logger.warning(f"活动不存在: id={campaign_id}")
                    return self.resource_not_found_result(campaign_id)
                
                # 构建活动响应
                campaign_response = CampaignResponse.model_validate(campaign.to_dict())
                
                # 缓存活动
                await self._cache_campaign(campaign_id, campaign_response)
                
                return self.create_success_result(campaign_response)
        except Exception as e:
            self.logger.error(f"获取活动失败: id={campaign_id}, 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.NOT_FOUND,
                error_message=f"获取活动失败: {str(e)}"
            )
    
    async def get_campaigns(self, params: GetCampaignsParams) -> Result[CampaignListResponse]:
        """获取活动列表
        
        Args:
            params: 查询参数对象，包含分页和过滤条件
            
        Returns:
            Result[CampaignListResponse]: 包含分页信息的活动列表结果
        """
        try:
            # Extract parameters from the params object
            page_num = params.page_num
            page_size = params.page_size
            status = params.status
            skip = (page_num - 1) * page_size
            self.logger.info(f"获取活动列表: page={page_num}, size={page_size}, status={status}")
            
            # 使用仓库类获取活动列表和总数
            campaigns, total = await self.campaign_repo.get_campaigns(
                skip=skip,
                limit=page_size,
                status=status
            )
            
            # 构建活动响应列表
            campaign_responses = []
            for campaign in campaigns:
                campaign_response = CampaignResponse.model_validate(campaign.to_dict())
                campaign_responses.append(campaign_response)
                # 缓存每个活动 (按需)
                # await self._cache_campaign(campaign.id, campaign_response)
                
            # 计算总页数
            total_pages = (total + page_size - 1) // page_size if page_size > 0 else 0
            
            # 构建分页响应
            paginated_response = CampaignListResponse(
                items=campaign_responses,
                total=total,
                page_num=page_num,
                page_size=page_size,
                page_count=total_pages
            )
            
            return self.create_success_result(paginated_response)
        except Exception as e:
            self.logger.error(f"获取活动列表失败: 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.OPERATION_FAILED,
                error_message=f"获取活动列表失败: {str(e)}"
            )
    
    async def check_campaign_eligibility(self, campaign_id: int, user_id: int) -> Result:
        """检查用户是否符合活动资格
        
        Args:
            campaign_id: 活动ID
            user_id: 用户ID
            
        Returns:
            Result[Dict[str, Any]]: 检查结果
        """
        try:
            self.logger.info(f"检查用户资格: campaign_id={campaign_id}, user_id={user_id}")
            
            # 获取活动
            campaign = await self.get_resource_by_id(campaign_id)
            if not campaign:
                self.logger.warning(f"活动不存在: id={campaign_id}")
                return self.resource_not_found_result(campaign_id)
            
            # 检查活动状态
            if campaign.status != CampaignStatus.ACTIVE:
                self.logger.info(f"活动状态不可用: status={campaign.status}")
                return self.create_error_result(
                    error_code=ErrorCode.INVALID_STATUS,
                    error_message=f"活动状态为 {campaign.status}，不可参与"
                )
            
            # 检查活动日期
            current_time = get_utc_now_without_tzinfo()
            
            if campaign.start_date and current_time < campaign.start_date:
                self.logger.info(f"活动尚未开始: start_date={campaign.start_date}")
                return self.create_error_result(
                    error_code=ErrorCode.INVALID_STATUS,
                    error_message="活动尚未开始"
                )
            
            if campaign.end_date and current_time > campaign.end_date:
                self.logger.info(f"活动已结束: end_date={campaign.end_date}")
                return self.create_error_result(
                    error_code=ErrorCode.INVALID_STATUS,
                    error_message="活动已结束"
                )
            
            # 检查参与人数限制
            if campaign.max_participants:
                current_participants = await self.campaign_repo.count_participants( campaign_id)
                
                if current_participants >= campaign.max_participants:
                    self.logger.info(f"活动参与人数已满: current={current_participants}, max={campaign.max_participants}")
                    return self.create_error_result(
                        error_code=ErrorCode.RESOURCE_EXHAUSTED,
                        error_message="活动参与人数已满"
                    )
            
            # 检查用户是否已参与
            has_participated = await self.campaign_repo.check_user_participation( campaign_id, user_id)
            if has_participated:
                self.logger.info(f"用户已参与活动: user_id={user_id}, campaign_id={campaign_id}")
                return self.create_error_result(
                    error_code=ErrorCode.CAMPAIGN_FULL,
                    error_message="您已参与此活动"
                )
            
            # 通过所有检查
            return self.create_success_result({"is_eligible": True})
        except Exception as e:
            self.logger.error(f"检查用户资格失败: campaign_id={campaign_id}, user_id={user_id}, 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.OPERATION_FAILED,
                error_message=f"检查用户资格失败: {str(e)}"
            )
    
    async def create_campaign(self, params: CampaignCreate) -> Result[CampaignResponse]:
        """创建营销活动
        
        Args:
            params: 活动创建参数
            
        Returns:
            Result[CampaignResponse]: 结果对象
        """
        try:
            self.logger.info(f"创建活动: name={params.name}")
            
            # 检查同名活动是否存在
            existing_campaign = await self.campaign_repo.get_by_name( params.name)
            if existing_campaign:
                self.logger.warning(f"活动名称已存在: {params.name}")
                return self.create_error_result(
                    error_code=ErrorCode.ALREADY_EXISTS,
                    error_message=f"活动名称 {params.name} 已被使用"
                )
            
            # 创建活动
            campaign = await self.campaign_repo.create( **params.model_dump())
            
            # 构建活动响应
            campaign_response = CampaignResponse.model_validate(campaign.to_dict())
            
            # 缓存活动
            await self._cache_campaign(campaign.id, campaign_response)
            
            # 触发活动创建事件
            event_data = campaign_response.model_dump()
            event_data["created_by_user_id"] = params.user_id # Assuming user_id is passed in params
            dispatch(MARKETING_CAMPAIGN_CREATED, payload=event_data)
            
            # 触发审计和缓存事件 (可选，根据需要添加)
            dispatch(SYSTEM_AUDIT_LOG_RECORDED, payload={
                "user_id": params.user_id,
                "action": "campaign_created",
                "resource_type": self.resource_type,
                "resource_id": campaign.id,
                "metadata": { "name": campaign.name }
            })
            dispatch(SYSTEM_CACHE_INVALIDATION_REQUESTED, payload={"resource_type": self.resource_type, "resource_id": campaign.id})
            await self._delete_list_cache() # Also invalidate list cache

            self.logger.info(f"活动创建成功: id={campaign.id}, name={campaign.name}")
            return self.create_success_result(campaign_response)
        except ValueError as ve:
            # 处理潜在的数据库错误
            self.logger.exception(f"创建活动时发生值错误 (可能来自DB): {ve}")
            await self.db.rollback() # Ensure rollback on specific DB errors
            return self.create_error_result(
                error_code=ErrorCode.DATABASE_ERROR,
                error_message=f"创建活动时数据库操作失败: {ve}"
            )
        except Exception as e:
            self.logger.exception(f"创建活动时发生未知错误: {str(e)}")
            await self.db.rollback()
            return self.create_error_result(
                error_code=ErrorCode.OPERATION_FAILED,
                error_message=f"创建活动失败: {str(e)}"
            )
    
    async def update_campaign(self,campaign_id: int, params: CampaignUpdate) -> Result[CampaignResponse]:
        """更新营销活动
        
        Args:
            params: 活动更新参数
            
        Returns:
            Result[CampaignResponse]: 结果对象
        """
        try:
            self.logger.info(f"更新活动: id={campaign_id}")
            
            # 获取活动
            campaign = await self.get_resource_by_id(campaign_id)
            if not campaign:
                self.logger.warning(f"活动不存在: {campaign_id}")
                return self.resource_not_found_result(campaign_id)
            
            # 如果更新活动名，检查是否已存在
            if params.name is not None and params.name != campaign.name:
                existing_campaign = await self.campaign_repo.get_by_name( params.name)
                if existing_campaign and existing_campaign.id != campaign.id:
                    self.logger.warning(f"活动名称已存在: {params.name}")
                    return self.create_error_result(
                        error_code=ErrorCode.ALREADY_EXISTS,
                        error_message=f"活动名称 {params.name} 已被使用"
                    )
            
            # 更新活动
            update_data = params.model_dump(exclude={"campaign_id"}, exclude_unset=True)
            campaign = await self.campaign_repo.update( campaign, data=update_data)
            
            # 构建活动响应
            campaign_response = CampaignResponse.model_validate(campaign.to_dict())
            
            # 缓存活动
            await self._cache_campaign(campaign.id, campaign_response)
            
            # 触发活动更新事件
            event_data = campaign_response.model_dump()
            event_data["updated_by_user_id"] = params.user_id # Assuming user_id is passed in params
            event_data["updated_fields"] = list(update_data.keys())
            dispatch(MARKETING_CAMPAIGN_UPDATED, payload=event_data)
            
            # 触发审计和缓存事件
            dispatch(SYSTEM_AUDIT_LOG_RECORDED, payload={
                "user_id": params.user_id,
                "action": "campaign_updated",
                "resource_type": self.resource_type,
                "resource_id": campaign.id,
                "metadata": { "name": campaign.name, "updated_fields": list(update_data.keys()) }
            })
            dispatch(SYSTEM_CACHE_INVALIDATION_REQUESTED, payload={"resource_type": self.resource_type, "resource_id": campaign.id})
            await self._delete_list_cache() # Also invalidate list cache

            self.logger.info(f"活动更新成功: id={campaign.id}, name={campaign.name}")
            return self.create_success_result(campaign_response)
        except Exception as e:
            self.logger.error(f"更新活动失败: id={campaign_id}, 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.INTERNAL_ERROR,
                error_message=f"更新活动失败: {str(e)}"
            )
    
    async def activate_campaign(self,user_id: int, campaign_id: int) -> Result[CampaignResponse]:
        """激活营销活动
        
        Args:
            campaign_id: 活动ID
            
        Returns:
            Result[CampaignResponse]: 结果对象
        """
        try:
            self.logger.info(f"激活活动: id={campaign_id}")
            
            # 获取活动
            campaign = await self.get_resource_by_id(campaign_id)
            if not campaign:
                self.logger.warning(f"活动不存在: {campaign_id}")
                return self.resource_not_found_result(campaign_id)
            
            # 更新活动状态
            campaign = await self.campaign_repo.update(
                 
                campaign,
                status=CampaignStatus.ACTIVE
            )
            
            # 构建活动响应
            campaign_response = CampaignResponse.model_validate(campaign.to_dict())
            
            # 缓存活动
            await self._cache_campaign(campaign.id, campaign_response)
            
            # 触发活动状态变更事件
            event_data = campaign_response.model_dump()
            event_data["changed_by_user_id"] = user_id # Assuming user_id context
            event_data["new_status"] = "active"
            dispatch(MARKETING_CAMPAIGN_STATUS_CHANGED, payload=event_data)
            
            # 触发审计和缓存事件
            dispatch(SYSTEM_AUDIT_LOG_RECORDED, payload={
                "user_id": user_id,
                "action": "campaign_activated",
                "resource_type": self.resource_type,
                "resource_id": campaign.id,
                "metadata": { "name": campaign.name }
            })
            dispatch(SYSTEM_CACHE_INVALIDATION_REQUESTED, payload={"resource_type": self.resource_type, "resource_id": campaign.id})
            await self._delete_list_cache()

            self.logger.info(f"活动 {campaign_id} 已激活")
            return self.create_success_result(campaign_response)
        except Exception as e:
            self.logger.error(f"激活活动失败: id={campaign_id}, 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.INTERNAL_ERROR,
                error_message=f"激活活动失败: {str(e)}"
            )
    
    async def deactivate_campaign(self,user_id: int, campaign_id: int) -> Result[CampaignResponse]:
        """停用营销活动
        
        Args:
            campaign_id: 活动ID
            
        Returns:
            Result[CampaignResponse]: 结果对象
        """
        try:
            self.logger.info(f"停用活动: id={campaign_id}")
            
            # 获取活动
            campaign = await self.get_resource_by_id(campaign_id)
            if not campaign:
                self.logger.warning(f"活动不存在: {campaign_id}")
                return self.resource_not_found_result(campaign_id)
            
            # 更新活动状态
            campaign = await self.campaign_repo.update(
                 
                campaign,
                status=CampaignStatus.INACTIVE
            )
            
            # 构建活动响应
            campaign_response = CampaignResponse.model_validate(campaign.to_dict())
            
            # 缓存活动
            await self._cache_campaign(campaign.id, campaign_response)
            
            # 触发活动状态变更事件
            event_data = campaign_response.model_dump()
            event_data["changed_by_user_id"] = user_id # Assuming user_id context
            event_data["new_status"] = "inactive"
            dispatch(MARKETING_CAMPAIGN_STATUS_CHANGED, payload=event_data)
            
            # 触发审计和缓存事件
            dispatch(SYSTEM_AUDIT_LOG_RECORDED, payload={
                "user_id": user_id,
                "action": "campaign_deactivated",
                "resource_type": self.resource_type,
                "resource_id": campaign.id,
                "metadata": { "name": campaign.name }
            })
            dispatch(SYSTEM_CACHE_INVALIDATION_REQUESTED, payload={"resource_type": self.resource_type, "resource_id": campaign.id})
            await self._delete_list_cache()

            self.logger.info(f"活动 {campaign_id} 已停用")
            return self.create_success_result(campaign_response)
        except Exception as e:
            self.logger.error(f"停用活动失败: id={campaign_id}, 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.INTERNAL_ERROR,
                error_message=f"停用活动失败: {str(e)}"
            )
    
    async def delete_campaign(self,user_id: int, campaign_id: int) -> Result[Dict[str, Any]]:
        """删除营销活动
        
        Args:
            campaign_id: 活动ID
            
        Returns:
            Result[Dict[str, Any]]: 结果对象
        """
        try:
            self.logger.info(f"删除活动: id={campaign_id}")
            
            # 获取活动
            campaign = await self.get_resource_by_id(campaign_id)
            if not campaign:
                self.logger.warning(f"活动不存在: {campaign_id}")
                return self.resource_not_found_result(campaign_id)
            
            # 检查活动是否有关联的邀请和奖励
            # TODO: 需要实现关联检查
            
            # 删除活动
            await self.campaign_repo.delete( campaign)
            
            # 触发活动删除事件
            event_data = campaign.to_dict()
            event_data["deleted_by_user_id"] = user_id # Assuming user_id context
            dispatch(MARKETING_CAMPAIGN_DELETED, payload=event_data)
            
            # 触发审计和缓存事件
            dispatch(SYSTEM_AUDIT_LOG_RECORDED, payload={
                "user_id": user_id,
                "action": "campaign_deleted",
                "resource_type": self.resource_type,
                "resource_id": campaign_id,
                "metadata": { "name": campaign.name } # Log name before deletion
            })
            dispatch(SYSTEM_CACHE_INVALIDATION_REQUESTED, payload={"resource_type": self.resource_type, "resource_id": campaign_id})
            await self._delete_list_cache()
            
            self.logger.info(f"活动 {campaign_id} 已成功删除")
            return self.create_success_result({"message": f"活动 {campaign_id} 已删除"})
        except Exception as e:
            self.logger.error(f"删除活动失败: id={campaign_id}, 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.INTERNAL_ERROR,
                error_message=f"删除活动失败: {str(e)}"
            )
    
    async def _cache_campaign(self, campaign_id: int, campaign_response: CampaignResponse) -> None:
        """缓存活动信息
        
        Args:
            campaign_id: 活动ID
            campaign_response: 活动响应对象
        """
        if self.redis:
            try:
                cache_key = self._get_resource_cache_key(campaign_id)
                await self.cache_resource(cache_key, campaign_response.model_dump(), CACHE_TTL)
                self.logger.debug(f"活动缓存成功: id={campaign_id}")
            except Exception as e:
                self.logger.warning(f"缓存活动失败: id={campaign_id}, 错误={str(e)}")
    
    def _get_resource_cache_key(self, resource_id: int) -> str:
        """获取资源缓存键
        
        Args:
            resource_id: 资源ID
            
        Returns:
            str: 缓存键
        """
        return f"campaign:{resource_id}"
    
    async def invalidate_resource_cache(self, resource_id: int) -> None:
        """清除资源缓存
        
        Args:
            resource_id: 资源ID
        """
        if self.redis:
            try:
                cache_key = self._get_resource_cache_key(resource_id)
                await self.delete_cache(cache_key)
                self.logger.debug(f"清除活动缓存成功: id={resource_id}")
            except Exception as e:
                self.logger.warning(f"清除活动缓存失败: id={resource_id}, 错误={str(e)}")

    async def delete_campaign_cache(self, campaign_id: int) -> None:
        """清除活动缓存
        
        Args:
            campaign_id: 活动ID
        """
        if self.redis:
            try:
                cache_key = self._get_resource_cache_key(campaign_id)
                await self.delete_cache(cache_key)
                self.logger.debug(f"清除活动缓存成功: id={campaign_id}")
            except Exception as e:
                self.logger.warning(f"清除活动缓存失败: id={campaign_id}, 错误={str(e)}")

    async def _delete_list_cache(self) -> None:
        """清除活动列表缓存
        """
        if self.redis:
            try:
                # Implement logic to delete list cache
                self.logger.debug("清除活动列表缓存")
            except Exception as e:
                self.logger.warning(f"清除活动列表缓存失败: 错误={str(e)}")
