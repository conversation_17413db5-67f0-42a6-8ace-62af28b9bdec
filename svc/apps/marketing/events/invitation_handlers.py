"""
邀请相关的后台任务 (Arq Tasks)
"""

import logging

# Keep service/repo imports for instantiation inside tasks
from fastapi import Depends
from fastapi_events.dispatcher import dispatch
from fastapi_events.handlers.local import local_handler
from fastapi_events.typing import Event

from svc.apps.auth import UserService, get_user_service
from svc.apps.marketing import InvitationService, get_invitation_service
from svc.core.database.utils import get_session_for_script
# Import task names (event type constants)
from svc.core.events.event_names import (
    MARKETING_INVITATION_COMPLETED, MARKETING_INVITATION_CREATED,
    MARKETING_INVITATION_PROCESS_REQUESTED, MARKETING_REWARD_ISSUE_REQUESTED,
    SYSTEM_AUDIT_LOG_RECORDED, SYSTEM_STATS_UPDATE_REQUESTED)

logger = logging.getLogger(__name__)




@local_handler.register(event_name=MARKETING_INVITATION_CREATED)
async def handle_invitation_created(
    event: Event,

):
    """处理邀请创建事件 (Arq Task)，缓存邀请信息并触发审计/统计。"""
    event_name, payload = event
    invitation_id = payload.get('invitation_id')
    code = payload.get('code')
    inviter_id = payload.get('inviter_id')
    campaign_id = payload.get('campaign_id')
    client_ip = payload.get('client_ip')
    client_device = payload.get('client_device')
    logger.info(f"处理邀请创建事件: id={invitation_id}, code={code}")


    # 2. 触发审计日志任务
    dispatch(
        SYSTEM_AUDIT_LOG_RECORDED, 
        payload={
            "user_id": inviter_id,
            "inviter_id": inviter_id,
            "campaign_id": campaign_id,
            "code": code,
            "client_ip": client_ip,
            "client_device": client_device
        }
    )
    
    # 3. 触发统计更新任务
    dispatch(
        SYSTEM_STATS_UPDATE_REQUESTED,
        entity_type="campaign",
        entity_id=campaign_id,
        metric_type="invitation_generated",
        increment_value=1,
        user_id=inviter_id
    )
            


@local_handler.register(event_name=MARKETING_INVITATION_PROCESS_REQUESTED)
async def handle_invitation_process_requested(
    event: Event,
    invitation_service:InvitationService=Depends(get_invitation_service)
):
    """处理邀请关系创建请求 (Arq Task)，创建邀请记录并触发完成事件。"""
    event_name, payload = event
    inviter_id = payload.get('inviter_id')
    invitee_id = payload.get('invitee_id')
    client_ip = payload.get('client_ip')
    client_device = payload.get('client_device')

    logger.info(f"处理邀请关系创建请求: 邀请人ID={inviter_id}, 被邀请人ID={invitee_id}")
    

    # 调用服务层方法处理邀请关系
    result = await invitation_service.process_new_invitation_relationship(
        inviter_id=inviter_id,
        invitee_id=invitee_id,
        client_ip=client_ip,
        client_device=client_device,
        campaign_id=1
    )

    if not result.is_success:
        logger.error(f"处理邀请关系创建请求失败: inviter={inviter_id}, invitee={invitee_id}, 原因: {result.result_msg}")

        return


@local_handler.register(event_name=MARKETING_INVITATION_COMPLETED)
async def handle_invitation_completed(
    event: Event,
):
    """处理邀请完成事件 (Arq Task)，为邀请人和被邀请人触发奖励发放请求。"""
    event_name, payload = event
    invitation_id = payload.get('invitation_id')
    inviter_id = payload.get('inviter_id')
    invitee_id = payload.get('invitee_id')
    campaign_id = payload.get('campaign_id')
    logger.info(f"处理邀请完成事件，准备触发奖励: invitation_id={invitation_id}, inviter={inviter_id}, invitee={invitee_id}")


    try:
        dispatch(
            MARKETING_REWARD_ISSUE_REQUESTED,
            payload={
                "invitation_id": invitation_id,
                "campaign_id": campaign_id,
            }
        )


    except Exception as e:
        logger.error(f"处理邀请完成事件失败: id={invitation_id}, 错误={str(e)}", exc_info=True)
        # Let arq handle retry
        raise # Re-raise to signal failure to arq