from typing import Any, Optional, Dict
from fastapi import APIRouter, Depends, Query, Path, status
from svc.core.models.result import Result
from svc.apps.auth.dependencies import (
    get_current_active_user,
    has_permission,
    resource_permission
)
from svc.apps.auth.models.user import User
from svc.apps.albums.schemas.album_image import (
    AlbumImageCreate, AlbumImageUpdate, AlbumImageListResponse, GetAlbumImagesParams
)
from svc.apps.albums.services.album_image import AlbumImageService
from svc.apps.albums.dependencies import get_album_image_service
from svc.core.exceptions.route_error_handler import handle_route_errors, ALBUM_ERROR_MAPPING
from svc.core.schemas.base import PageParams

router = APIRouter(
    tags=["图册图片管理（管理端）"]
)

@router.get("/admin/{album_id}/images", response_model=Result[Dict[str, Any]])
@handle_route_errors()
async def admin_list_album_images(
    album_id: int = Path(..., description="图册ID"),
    params: PageParams = Depends(),
    status: Optional[str] = Query(None, description="图片状态"),
    search_term: Optional[str] = Query(None, description="搜索关键词"),
    order_by: Optional[str] = Query("sort_order", description="排序字段"),
    order_desc: Optional[bool] = Query(False, description="是否降序"),
    image_service: AlbumImageService = Depends(get_album_image_service),
    current_user: User = Depends(get_current_active_user),
    _: bool = Depends(lambda: has_permission("album_image:read"))
) -> Result[AlbumImageListResponse]:
    """获取图册图片列表 (管理端)"""
    params_obj = GetAlbumImagesParams(
        page_num=params.page_num,
        page_size=params.page_size,
        album_id=album_id,
        status=status,
        search_term=search_term,
        order_by=order_by,
        order_desc=order_desc
    )
    return await image_service.get_album_images(params=params_obj)

@router.post("/admin/images", response_model=Result[Dict[str, Any]], status_code=status.HTTP_201_CREATED)
@handle_route_errors()
async def admin_create_album_image(
    album_id: int = Path(..., description="图册ID"),
    image_data: AlbumImageCreate = Depends(),
    image_service: AlbumImageService = Depends(get_album_image_service),
    current_user: User = Depends(get_current_active_user),
    _: bool = Depends(lambda: has_permission("album_image:create")),
) -> Result:
    """上传图片 (管理端)"""
    # 强制album_id一致
    image_data.album_id = album_id
    return await image_service.create_album_image(params=image_data)

@router.put("/admin/images/{image_id}", response_model=Result[Dict[str, Any]])
@handle_route_errors()
async def admin_update_album_image(
    album_id: int = Path(..., description="图册ID"),
    image_id: int = Path(..., description="图片ID"),
    image_in: AlbumImageUpdate = Depends(),
    image_service: AlbumImageService = Depends(get_album_image_service),
    current_user: User = Depends(get_current_active_user),
    _: Any = Depends(resource_permission("album_image", "update")),
) -> Result:
    """更新图片 (管理端)"""
    return await image_service.update_album_image(image_id=image_id, params=image_in)

@router.delete("/admin/images/{image_id}", response_model=Result[Dict[str, Any]])
@handle_route_errors()
async def admin_delete_album_image(
    album_id: int = Path(..., description="图册ID"),
    image_id: int = Path(..., description="图片ID"),
    image_service: AlbumImageService = Depends(get_album_image_service),
    current_user: User = Depends(get_current_active_user),
    _: Any = Depends(resource_permission("album_image", "delete")),
) -> Result:
    """删除图片 (管理端)"""
    return await image_service.delete_album_image(image_id=image_id) 