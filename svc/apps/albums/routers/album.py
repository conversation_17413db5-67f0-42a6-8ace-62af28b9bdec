from typing import Any, Optional, Dict
from fastapi import APIRouter, Depends, Query, Path, status
from svc.core.models.result import Result
from svc.apps.auth.dependencies import (
    get_current_active_user,
    has_permission,
    resource_permission
)
from svc.apps.auth.models.user import User
from svc.apps.albums.schemas.album import (
    AlbumCreate, AlbumUpdate, AlbumListResponse, GetAlbumsParams
)
from svc.apps.albums.services.album import AlbumService
from svc.apps.albums.dependencies import get_album_service
from svc.core.exceptions.route_error_handler import handle_route_errors, ALBUM_ERROR_MAPPING
from svc.core.schemas.base import PageParams

router = APIRouter(
    tags=["图册管理（管理端）"]
)

@router.get("/admin", response_model=Result[Dict[str, Any]])
@handle_route_errors()
async def admin_list_albums(
    params: PageParams = Depends(),
    status: Optional[str] = Query(None, description="图册状态"),
    tags: Optional[str] = Query(None, description="标签（逗号分隔）"),
    search_term: Optional[str] = Query(None, description="搜索关键词"),
    order_by: Optional[str] = Query("sort_order", description="排序字段"),
    order_desc: Optional[bool] = Query(False, description="是否降序"),
    album_service: AlbumService = Depends(get_album_service),
    current_user: User = Depends(get_current_active_user),
    _: bool = Depends(lambda: has_permission("album:read"))
) -> Result[AlbumListResponse]:
    """获取图册列表 (管理端)"""
    params_obj = GetAlbumsParams(
        page_num=params.page_num,
        page_size=params.page_size,
        status=status,
        tags=tags.split(",") if tags else None,
        search_term=search_term,
        order_by=order_by,
        order_desc=order_desc
    )
    return await album_service.get_albums(params=params_obj)

@router.get("/admin/{album_id}", response_model=Result[Dict[str, Any]])
@handle_route_errors()
async def admin_get_album_details(
    album_id: int = Path(..., description="图册ID"),
    album_service: AlbumService = Depends(get_album_service),
    current_user: User = Depends(get_current_active_user),
    _: Any = Depends(resource_permission("album", "read")),
) -> Result:
    """获取图册详情 (管理端)"""
    return await album_service.get_album(album_id=album_id)

@router.post("/admin", response_model=Result[Dict[str, Any]], status_code=status.HTTP_201_CREATED)
@handle_route_errors()
async def admin_create_album(
    album_data: AlbumCreate,
    album_service: AlbumService = Depends(get_album_service),
    current_user: User = Depends(get_current_active_user),
    _: bool = Depends(lambda: has_permission("album:create")),
) -> Result:
    """创建图册 (管理端)"""
    return await album_service.create_album(params=album_data)

@router.put("/admin/{album_id}", response_model=Result[Dict[str, Any]])
@handle_route_errors()
async def admin_update_album(
    album_in: AlbumUpdate,
    album_id: int = Path(..., description="图册ID"),
    album_service: AlbumService = Depends(get_album_service),
    current_user: User = Depends(get_current_active_user),
    _: Any = Depends(resource_permission("album", "update")),
) -> Result:
    """更新图册 (管理端)"""
    return await album_service.update_album(album_id=album_id, params=album_in)

@router.delete("/admin/{album_id}", response_model=Result[Dict[str, Any]])
@handle_route_errors()
async def admin_delete_album(
    album_id: int = Path(..., description="图册ID"),
    album_service: AlbumService = Depends(get_album_service),
    current_user: User = Depends(get_current_active_user),
    _: Any = Depends(resource_permission("album", "delete")),
) -> Result:
    """删除图册 (管理端)"""
    return await album_service.delete_album(album_id=album_id) 