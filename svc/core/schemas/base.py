from typing import Generic, TypeVar, List, Optional
from pydantic import BaseModel, Field

from svc.core.models.base import CamelCaseModel
from svc.core.utils.datetime_utils import get_timestamp


DataType = TypeVar('DataType')

class PageParams(CamelCaseModel):
    """
    通用分页参数模型
    """
    page_num: int = Field(1, ge=1, description="页码（从1开始）")
    page_size: int = Field(10, ge=1, le=1000, description="每页数量")

class BaseResponse(CamelCaseModel):
    """基础响应模型，可以包含通用字段"""
    is_success: bool = Field(default=True, description="操作是否成功")
    timestamp: int = Field(default=get_timestamp(), description="时间戳")
    result_code: Optional[int] = Field(default=200, description="结果代码")
    result_msg: Optional[str] = Field(default='ok', description="结果消息")


class PaginatedResponse(CamelCaseModel, Generic[DataType]):
    """
    通用分页响应模型
    """
    items: List[DataType] = Field(..., description="当前页的数据列表")
    total: int = Field(..., description="总记录数")
    page_num: int = Field(..., description="当前页码 (从1开始)")
    page_size: int = Field(..., description="每页记录数")
    page_count: int = Field(..., description="总页数") 