"""
定义应用中使用的事件名称常量。
"""

# --- Auth Events ---
# 来源于 svc/apps/auth/services/auth.py, svc/apps/auth/services/user.py 等
# 旧标识符可能为 EventType.USER_REGISTERED, EventType.USER_LOGGED_IN 等
AUTH_USER_REGISTERED = "auth:user:registered"
AUTH_USER_LOGGED_IN = "auth:user:logged_in"
AUTH_USER_AUTHENTICATION_SUCCESS = "auth:user:authentication_success"
AUTH_USER_AUTHENTICATION_FAILED = "auth:user:authentication_failed"
AUTH_PASSWORD_RESET_REQUESTED = "auth:password:reset_requested"
AUTH_USER_UPDATED = "auth:user:updated"
AUTH_USER_DELETED = "auth:user:deleted"
AUTH_USER_ROLE_ASSIGNED = "auth:user:role_assigned"
AUTH_USER_ROLE_REMOVED = "auth:user:role_removed"
AUTH_PASSWORD_RESET_COMPLETED = "auth:password:reset_completed"
AUTH_USER_LOGGED_OUT = "auth:user:logged_out"
AUTH_USER_ACTIVATED = "auth:user:activated"
AUTH_USER_DEACTIVATED = "auth:user:deactivated"
AUTH_USER_PASSWORD_CHANGED = "auth:user:password_changed"
AUTH_ROLE_CREATED = "auth:role:created" # 推断自 svc/apps/auth/services/role.py
AUTH_ROLE_UPDATED = "auth:role:updated" # 推断
AUTH_ROLE_DELETED = "auth:role:deleted" # 推断

# --- Billing Events ---
# 来源于 svc/apps/billing/services/invoice.py, payment.py, subscription.py 等
# 旧标识符可能为 EventType.INVOICE_CREATED, EventType.PAYMENT_SUCCESSFUL 等
BILLING_INVOICE_CREATED = "billing:invoice:created"
BILLING_INVOICE_UPDATED = "billing:invoice:updated" # 推断
BILLING_INVOICE_PAID = "billing:invoice:paid" # 推断，替代 payment event?
BILLING_INVOICE_FAILED = "billing:invoice:failed" # 推断
BILLING_PAYMENT_RECEIVED = "billing:payment:received"
BILLING_SUBSCRIPTION_PLAN_CREATED = "billing:subscription_plan:created" # 推断自 subscription_plan.py
BILLING_SUBSCRIPTION_STARTED = "billing:subscription:started"
BILLING_SUBSCRIPTION_CANCELED = "billing:subscription:canceled" # 推断
BILLING_SUBSCRIPTION_RENEWED = "billing:subscription:renewed" # 推断
BILLING_PAYMENT_CREATED = "billing:payment:created" # 新增 (来自 payment.py)
BILLING_PAYMENT_FAILED = "billing:payment:failed" # 新增 (来自 payment.py)
BILLING_PAYMENT_REFUNDED = "billing:payment:refunded" # 新增 (来自 payment.py)
BILLING_SUBSCRIPTION_CREATED = "billing:subscription:created" # 新增 (来自 subscription.py, 替代 started?)
BILLING_SUBSCRIPTION_PAUSED = "billing:subscription:paused" # 新增 (来自 subscription.py)
BILLING_SUBSCRIPTION_RESUMED = "billing:subscription:resumed" # 新增 (来自 subscription.py)
BILLING_SUBSCRIPTION_REACTIVATED = "billing:subscription:reactivated" # 新增 (来自 subscription.py)
BILLING_SUBSCRIPTION_PLAN_CHANGED = "billing:subscription:plan_changed" # 新增 (来自 subscription.py)
BILLING_SUBSCRIPTION_UPGRADED = "billing:subscription:upgraded" # 新增 (来自 subscription.py)
BILLING_SUBSCRIPTION_PLAN_UPDATED = "billing:subscription_plan:updated" # 新增 (来自 subscription_plan.py)
BILLING_SUBSCRIPTION_PLAN_DELETED = "billing:subscription_plan:deleted" # 新增 (来自 subscription_plan.py)

# --- Marketing Events ---
# 来源于 svc/apps/marketing/services/campaign.py, reward.py, invitation.py 等
# 旧标识符可能为 event_types.CAMPAIGN_TRIGGERED, event_types.REWARD_ISSUED 等
MARKETING_LEAD_CAPTURED = "marketing:lead:captured" # 推断自 lead_handlers.py
MARKETING_CAMPAIGN_CREATED = "marketing:campaign:created"          # 新增 (来自 campaign.py)
MARKETING_CAMPAIGN_TRIGGERED = "marketing:campaign:triggered"
MARKETING_REWARD_ISSUED = "marketing:reward:issued"
MARKETING_INVITATION_CREATED = "marketing:invitation:created" # 推断自 invitation.py
MARKETING_INVITATION_COMPLETED = "marketing:invitation:completed" # 推断自 invitation.py
MARKETING_INVITATION_FAILED = "marketing:invitation:failed" # 推断自 invitation.py

MARKETING_INVITATION_SENT = "marketing:invitation:sent" # 推断自 invitation.py
MARKETING_INVITATION_ACCEPTED = "marketing:invitation:accepted" # 推断自 invitation.py
MARKETING_INVITATION_PROCESS_REQUESTED = "marketing:invitation:process_requested" # 新增 (来自 user_handlers.py)
MARKETING_CAMPAIGN_UPDATED = "marketing:campaign:updated"          # 新增 (来自 campaign.py)
MARKETING_CAMPAIGN_STATUS_CHANGED = "marketing:campaign:status_changed" # 新增 (来自 campaign.py)
MARKETING_CAMPAIGN_DELETED = "marketing:campaign:deleted"          # 新增 (来自 campaign.py)
MARKETING_REWARD_ISSUE_REQUESTED = "marketing:reward:issue_requested"
MARKETING_REWARD_RECORD_CREATED = "marketing:reward:record_created"
MARKETING_REWARD_ISSUED = "marketing:reward:issued"
MARKETING_REWARD_FAILED = "marketing:reward:failed"

# 删除自定义的产品事件常量，使用现有的事件系统

# --- System Events ---
# 来源于 svc/apps/system/events/__init__.py
SYSTEM_NOTIFICATION_SEND_REQUESTED = "system:notification:send_requested"
SYSTEM_NOTIFICATION_SENT = "system:notification:sent" # 推断
SYSTEM_BACKGROUND_JOB_COMPLETED = "system:background_job:completed" # 推断
SYSTEM_STATS_UPDATE_REQUESTED = "system:stats:update_requested"
SYSTEM_SECURITY_SUSPICIOUS_ACTIVITY = "system:security:suspicious_activity"
SYSTEM_USER_NOTIFICATION_REQUESTED = "system:user:notification_requested"
SYSTEM_AUDIT_LOG_RECORDED = "system:audit:log_recorded"
SYSTEM_AUDIT_LOG_REQUESTED = "system:audit:log_requested"
SYSTEM_TASK_SCHEDULED = "system:task:scheduled" # 新增 (来自 user_handlers.py)
SYSTEM_CACHE_INVALIDATION_REQUESTED = "system:cache:invalidation_requested" # 新增 (来自 reward.py)
SYSTEM_CACHE_INVALIDATED = "system:cache:invalidated" # 新增 (来自 reward.py)
# --- WeChat Events ---
# 来源于 svc/core/services/wechat.py
WECHAT_MESSAGE_RECEIVED = "wechat:message:received" # 推断
WECHAT_USER_BOUND = "wechat:user:bound" # 推断


