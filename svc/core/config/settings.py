"""
配置管理模块。
提供统一的配置访问接口和环境配置加载功能。
"""
import json
import logging
import os
from enum import Enum
from functools import lru_cache
from pathlib import Path
from typing import Any, List, Optional, Union

from dotenv import load_dotenv
from pydantic import BaseModel, Field, field_validator
from pydantic_settings import BaseSettings, SettingsConfigDict

logger = logging.getLogger(__name__)

class EnvironmentType(str, Enum):
    """环境类型枚举"""
    DEVELOPMENT = "development"
    TEST = "test"
    PRODUCTION = "production"

class Settings(BaseSettings):
    """应用配置类"""
    # 环境配置
    env: str = Field(default="development", alias="APP_ENV")
    debug: bool = Field(default=False, alias="DEBUG")
    
    # 应用基础配置
    project_name: str = Field(default="FastAPI App", alias="PROJECT_NAME")
    api_prefix: str = Field(default="/api/v1", alias="API_V1_STR")
    version: str = Field(default="0.1.0", alias="VERSION")
    
    # 安全配置
    secret_key: str = Field(default="", alias="SECRET_KEY")
    access_token_expire_minutes: int = Field(default=11520, alias="ACCESS_TOKEN_EXPIRE_MINUTES")
    refresh_token_expire_minutes: int = Field(default=11520, alias="REFRESH_TOKEN_EXPIRE_MINUTES")
    password_reset_token_expire_minutes: int = Field(default=10, alias="PASSWORD_RESET_TOKEN_EXPIRE_MINUTES")
    algorithm: str = Field(default="HS256", alias="API_ALGORITHM")
    api_username: str = Field(default="", alias="API_USERNAME")
    api_password: str = Field(default="", alias="API_PASSWORD")
    admin_username: str = Field(default="", alias="ADMIN_USERNAME")
    admin_password: str = Field(default="", alias="ADMIN_PASSWORD")

    # 微信配置
    wechat_app_id: str = Field(default="", alias="WECHAT_APP_ID")
    wechat_app_secret: str = Field(default="", alias="WECHAT_APP_SECRET")
    wechat_auto_create_user: bool = Field(default=True, alias="WECHAT_AUTO_CREATE_USER")
    wechat_enabled: bool = Field(default=False, alias="WECHAT_ENABLED")
    
    # CORS配置
    cors_origins: List[str] = Field(default_factory=list, alias="BACKEND_CORS_ORIGINS")
    
    # 数据库配置
    db_server: str = Field(default="localhost", alias="POSTGRES_SERVER")
    db_user: str = Field(default="postgres", alias="POSTGRES_USER")
    db_password: str = Field(default="", alias="POSTGRES_PASSWORD")
    db_name: str = Field(default="postgres", alias="POSTGRES_DB")
    db_uri: str = Field(default="", alias="SQLALCHEMY_DATABASE_URI")
    db_test_uri: str = Field(default="sqlite+aiosqlite://", alias="SQLALCHEMY_DATABASE_TEST_URI")
    db_echo: bool = Field(default=False, alias="SQLALCHEMY_ECHO")
    db_pool_size: int = Field(default=10, alias="SQLALCHEMY_POOL_SIZE")
    db_max_overflow: int = Field(default=20, alias="SQLALCHEMY_MAX_OVERFLOW")
    db_pool_timeout: int = Field(default=30, alias="SQLALCHEMY_POOL_TIMEOUT")
    db_pool_recycle: int = Field(default=3600, alias="SQLALCHEMY_POOL_RECYCLE")
    
    # Redis配置
    redis_host: str = Field(default="localhost", alias="REDIS_HOST")
    redis_port: str = Field(default="6379", alias="REDIS_PORT")
    redis_db: str = Field(default="0", alias="REDIS_DB")
    redis_password: str = Field(default="", alias="REDIS_PASSWORD")
    
    # 租户配置
    multi_tenant_enabled: bool = Field(default=False, alias="MULTI_TENANT_ENABLED")
    
    # API响应配置
    use_camel_case: bool = Field(default=True, alias="USE_CAMEL_CASE")
    
    @field_validator("cors_origins", mode="before")
    def assemble_cors_origins(cls, v: Union[str, List[str]]) -> List[str]:
        """解析CORS配置"""
        if isinstance(v, str):
            try:
                return json.loads(v)
            except json.JSONDecodeError:
                return v.split(",")
        return v
    
    @field_validator("env", mode="after")
    def validate_environment(cls, v: str) -> str:
        """验证环境类型"""
        try:
            EnvironmentType(v.lower())
            return v.lower()
        except ValueError:
            logger.warning(f"未知环境类型: {v}，使用开发环境")
            return EnvironmentType.DEVELOPMENT.value
    
    @field_validator("db_uri", mode="after")
    def validate_db_uri(cls, v: str, values: dict) -> str:
        """验证数据库URI"""
        if not v and values.data.get("env") != EnvironmentType.TEST.value:
            # 尝试构建数据库URI
            db_server = values.data.get("db_server", "localhost")
            db_user = values.data.get("db_user", "postgres")
            db_password = values.data.get("db_password", "")
            db_name = values.data.get("db_name", "postgres")
            v = f"postgresql+asyncpg://{db_user}:{db_password}@{db_server}/{db_name}"
            logger.info(f"自动构建数据库URI: {v}")
        return v

    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=True,
        extra="allow",
        populate_by_name=True
    )

def get_environment_type() -> EnvironmentType:
    """获取当前环境类型"""
    env = os.getenv("APP_ENV", "development").lower()
    try:
        return EnvironmentType(env)
    except ValueError:
        logger.warning(f"未知环境类型: {env}，使用开发环境")
        return EnvironmentType.DEVELOPMENT

def load_env_file(env_file: Path) -> None:
    """加载环境配置文件"""
    try:
        if env_file.exists():
            logger.info(f"加载配置文件: {env_file}")
            load_dotenv(env_file, override=True)
            logger.debug(f"已加载的环境变量: {', '.join(k for k in os.environ if k.startswith(('APP_', 'PROJECT_', 'API_', 'POSTGRES_', 'REDIS_', 'SQLALCHEMY_', 'SECRET_', 'VERSION')))}")
        else:
            logger.debug(f"配置文件不存在: {env_file}")
    except Exception as e:
        logger.error(f"加载配置文件失败 {env_file}: {str(e)}")

def load_environment_config() -> None:
    """加载环境配置"""
    try:
        # 获取项目根目录
        root_dir = Path(__file__).parent.parent.parent.parent
        logger.info(f"项目根目录: {root_dir}")
        
        # 获取当前环境
        env = get_environment_type()
        
        # 按优先级加载配置文件
        # 1. 默认.env文件（最低优先级）
        load_env_file(root_dir / ".env")
        
        # 2. 环境特定的.env文件
        if env != EnvironmentType.PRODUCTION:
            load_env_file(root_dir / f".env.{env.value}")
        
        # 3. 本地开发配置文件（如果存在）
        load_env_file(root_dir / ".env.local")
        
        logger.info(f"当前运行环境: {env.value}")
    except Exception as e:
        logger.error(f"加载环境配置失败: {str(e)}")
        raise

@lru_cache()
def get_settings() -> Settings:
    """获取应用配置（单例模式）"""
    try:
        # 加载环境配置
        load_environment_config()
        
        # 创建配置实例
        settings = Settings()
        
        # 设置调试模式
        env_type = EnvironmentType(settings.env)
        if env_type == EnvironmentType.DEVELOPMENT:
            settings.debug = True
        elif env_type == EnvironmentType.PRODUCTION:
            settings.debug = False
        
        # 验证必要的配置
        validate_required_settings(settings)
        
        # 输出所有配置项，帮助调试
        logger.debug("当前配置项:")
        for key, value in settings.model_dump().items():
            if key not in ("secret_key", "api_password", "db_password", "redis_password"):
                logger.debug(f"{key}: {value}")
        
        logger.info(f"配置加载成功: {settings.project_name} v{settings.version}")
        return settings
    except Exception as e:
        logger.error(f"配置加载失败: {str(e)}")
        raise

def validate_required_settings(settings: Settings) -> None:
    """验证必要的配置项"""
    env_type = EnvironmentType(settings.env)
    
    # 只在非测试环境验证
    if env_type != EnvironmentType.TEST:
        errors = []
        
        # 验证数据库URI
        if not settings.db_uri:
            errors.append("数据库URI未配置")
        
        # 验证安全配置
        if not settings.secret_key:
            errors.append("安全密钥未配置")
        
        # 验证API凭证
        if not (settings.api_username and settings.api_password):
            errors.append("API凭证未配置")
        
        if errors:
            raise ValueError("配置验证失败:\n" + "\n".join(f"- {error}" for error in errors))

def get_env_value(key: str, default: Any = None) -> Any:
    """获取环境变量值"""
    return os.getenv(key, default)

# 便捷访问函数
def get_db_uri() -> str:
    """获取数据库URI"""
    return get_settings().db_uri

def get_redis_config() -> dict:
    """获取Redis配置"""
    settings = get_settings()
    return {
        "host": settings.redis_host,
        "port": settings.redis_port,
        "db": settings.redis_db,
        "password": settings.redis_password
    }

def is_debug_mode() -> bool:
    """是否为调试模式"""
    return get_settings().debug 