"""
统一的服务层结果对象模块。
提供一致的结果返回格式和错误处理。
"""
from datetime import datetime
from typing import Any, Dict, Generic, Optional, TypeVar, Union
from pydantic import  Field, ConfigDict

from svc.core.exceptions.error_codes import <PERSON>rrorCode
from svc.core.models.base import CamelCaseModel
from svc.core.schemas.base import BaseResponse

# 定义泛型类型变量
T = TypeVar('T')


class Result(BaseResponse, Generic[T]):
    """统一的服务结果对象"""
    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "is_success": True,
                "result_code": None,
                "result_msg": None,
                "data": {"id": 1, "name": "示例数据"}
            }
        }
    )
    
    data: Optional[T] = Field(default=None, description="返回数据")


class ResultFactory:
    """结果对象工厂，用于创建各种结果实例"""

    @staticmethod
    def success(data: Optional[T] = None) -> Result[T]:
        """创建成功结果

        Args:
            data: 返回数据

        Returns:
            结果对象
        """
        return Result(is_success=True, data=data)

    @staticmethod
    def error(
        result_code: int,
        result_msg: str,
        data: Optional[Any] = None
    ) -> Result:
        """创建错误结果

        Args:
            result_code: 错误代码
            result_msg: 错误消息
            data: 附加数据

        Returns:
            结果对象
        """
        return Result(
            is_success=False,
            result_code=result_code,
            result_msg=result_msg,
            data=data
        )

    @staticmethod
    def from_exception(
        exception: Exception,
        error_code: str = "INTERNAL_ERROR"
    ) -> Result:
        """从异常创建错误结果

        Args:
            exception: 异常对象
            error_code: 错误代码

        Returns:
            结果对象
        """
        return Result(
            is_success=False,
            result_code=error_code,
            result_msg=str(exception)
        )

    @classmethod
    def resource_not_found(
        cls,
        resource_type: str,
        resource_id: Union[str, int],
        result_code: int = ErrorCode.NOT_FOUND
    ) -> Result:
        """资源不存在错误

        Args:
            resource_type: 资源类型
            resource_id: 资源ID
            result_code: 错误代码

        Returns:
            结果对象
        """
        return cls.error(
            result_code=result_code,
            result_msg=f"{resource_type}不存在: ID={resource_id}"
        )

    @classmethod
    def permission_denied(
        cls,
        resource_type: Optional[str] = None,
        resource_id: Optional[Union[str, int]] = None,
        result_code: int = ErrorCode.PERMISSION_DENIED
    ) -> Result:
        """权限不足错误

        Args:
            resource_type: 资源类型
            resource_id: 资源ID
            result_code: 错误代码

        Returns:
            结果对象
        """
        message = "权限不足"
        if resource_type and resource_id:
            message += f": 无权访问{resource_type} (ID={resource_id})"

        return cls.error(
            result_code=result_code,
            result_msg=message
        )

    @classmethod
    def validation_error(
        cls,
        errors: Dict[str, Any],
        result_code: int = ErrorCode.VALIDATION_ERROR
    ) -> Result:
        """验证错误

        Args:
            errors: 验证错误信息
            result_code: 错误代码

        Returns:
            结果对象
        """
        return cls.error(
            result_code=result_code,
            result_msg="输入数据验证失败",
            data=errors
        ) 