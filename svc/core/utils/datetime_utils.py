"""
日期时间工具模块，提供统一的时间处理函数。
避免混用带时区和不带时区的datetime对象。
"""
from datetime import datetime, timezone, timedelta
from typing import Optional, Union, Tuple

def get_timestamp(dt: Optional[datetime] = None) -> int:
    """
    获取指定datetime的UNIX时间戳（秒）。
    如果不传入datetime则返回当前时间的时间戳。
    
    Args:
        dt: datetime对象，可选，默认为None表示当前时间
        
    Returns:
        UNIX时间戳（整数，单位：秒）
    """
    if dt is None:
        dt = get_utc_now()
    dt = make_aware(dt)  # 确保有时区信息
    return int(dt.timestamp())


def get_millisecond_timestamp(dt: Optional[datetime] = None) -> int:
    """
    获取指定datetime的UNIX时间戳（毫秒）。
    如果不传入datetime则返回当前时间的时间戳。
    
    Args:
        dt: datetime对象，可选，默认为None表示当前时间
        
    Returns:
        UNIX时间戳（整数，单位：毫秒）
    """
    return get_timestamp(dt) * 1000


def get_utc_now() -> datetime:
    """
    获取当前UTC时间，带时区信息。
    用于应用层中需要明确时区的场景。
    
    Returns:
        带UTC时区的当前时间
    """
    return datetime.now(timezone.utc)


def get_utc_now_without_tzinfo() -> datetime:
    """
    获取当前UTC时间，不带时区信息。
    适用于数据库存储，因为数据库通常不存储时区信息。
    
    Returns:
        不带时区的当前UTC时间
    """
    return datetime.now()


def make_aware(dt: datetime) -> datetime:
    """
    将不带时区的datetime转换为带UTC时区的datetime。
    如果已经带有时区信息，则直接返回。
    
    Args:
        dt: 需要转换的datetime对象
        
    Returns:
        带UTC时区的datetime对象
    """
    if dt.tzinfo is None:
        return dt.replace(tzinfo=timezone.utc)
    return dt


def make_naive(dt: datetime) -> datetime:
    """
    将带时区的datetime转换为不带时区的UTC datetime。
    如果已经是不带时区的，则直接返回。
    
    Args:
        dt: 需要转换的datetime对象
        
    Returns:
        不带时区的UTC datetime对象
    """
    if dt.tzinfo is not None:
        return dt.astimezone(timezone.utc).replace(tzinfo=None)
    return dt


def compare_datetimes(dt1: Optional[datetime], dt2: Optional[datetime]) -> int:
    """
    安全比较两个datetime对象，确保它们有相同的时区属性。
    返回 -1 表示dt1<dt2，0 表示dt1==dt2，1 表示dt1>dt2。
    如果任一参数为None，则根据None的比较规则处理。
    
    Args:
        dt1: 第一个datetime对象，可以为None
        dt2: 第二个datetime对象，可以为None
        
    Returns:
        比较结果：-1, 0, 或 1
    """
    if dt1 is None and dt2 is None:
        return 0
    if dt1 is None:
        return -1
    if dt2 is None:
        return 1
    
    # 确保两个时间都是一致的类型（都有时区或都没有）
    if dt1.tzinfo is not None and dt2.tzinfo is None:
        dt2 = make_aware(dt2)
    elif dt1.tzinfo is None and dt2.tzinfo is not None:
        dt1 = make_aware(dt1)
    
    if dt1 < dt2:
        return -1
    elif dt1 > dt2:
        return 1
    else:
        return 0


def is_before(dt1: Optional[datetime], dt2: Optional[datetime]) -> bool:
    """
    检查dt1是否早于dt2，安全处理时区差异。
    
    Args:
        dt1: 第一个datetime对象，可以为None
        dt2: 第二个datetime对象，可以为None
        
    Returns:
        如果dt1早于dt2，返回True；否则返回False
    """
    return compare_datetimes(dt1, dt2) < 0


def is_after(dt1: Optional[datetime], dt2: Optional[datetime]) -> bool:
    """
    检查dt1是否晚于dt2，安全处理时区差异。
    
    Args:
        dt1: 第一个datetime对象，可以为None
        dt2: 第二个datetime对象，可以为None
        
    Returns:
        如果dt1晚于dt2，返回True；否则返回False
    """
    return compare_datetimes(dt1, dt2) > 0


def is_equal(dt1: Optional[datetime], dt2: Optional[datetime]) -> bool:
    """
    检查dt1是否等于dt2，安全处理时区差异。
    
    Args:
        dt1: 第一个datetime对象，可以为None
        dt2: 第二个datetime对象，可以为None
        
    Returns:
        如果dt1等于dt2，返回True；否则返回False
    """
    return compare_datetimes(dt1, dt2) == 0


def add_timezone_if_naive(dt: datetime) -> datetime:
    """
    如果datetime对象不带时区，则添加UTC时区；否则直接返回。
    
    Args:
        dt: 原始datetime对象
        
    Returns:
        确保带有时区信息的datetime对象
    """
    if dt.tzinfo is None:
        return dt.replace(tzinfo=timezone.utc)
    return dt 