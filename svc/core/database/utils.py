"""数据库工具模块 - 简化版本"""
import contextlib
import logging
from typing import Any, Callable, Dict

from .dependencies import get_db
from .session import create_session, get_session

logger = logging.getLogger(__name__)

# 为了保持向后兼容，提供简单的别名
get_session_for_route = get_db

@contextlib.asynccontextmanager
async def get_session_for_script():
    """获取用于脚本的数据库会话上下文管理器"""
    async with get_session() as session:
        yield session

# 为事件处理器提供上下文管理器
@contextlib.asynccontextmanager
async def get_session_for_event():
    """获取用于事件处理器的数据库会话上下文管理器"""
    async with get_session() as session:
        yield session

async def resolve_dependencies_for_event(**dependency_factories: Callable) -> Dict[str, Any]:
    """
    为事件处理器解析依赖注入

    Args:
        **dependency_factories: 依赖工厂函数的字典

    Returns:
        Dict[str, Any]: 解析后的依赖实例字典
    """
    resolved_deps = {}

    # 创建数据库会话
    session = await create_session()

    try:
        # 解析每个依赖
        for name, factory in dependency_factories.items():
            try:
                # 调用依赖工厂函数，传入数据库会话
                if callable(factory):
                    resolved_deps[name] = await factory(session)
                else:
                    resolved_deps[name] = factory
            except Exception as e:
                logger.error(f"解析依赖 {name} 失败: {e}")
                raise

        return resolved_deps

    except Exception:
        # 如果解析失败，关闭会话
        await session.close()
        raise