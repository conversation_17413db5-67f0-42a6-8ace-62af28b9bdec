"""
数据库模块。
提供数据库连接、模型基类和会话管理功能。
"""

# 核心功能导入
from svc.core.database.dependencies import get_db
from svc.core.database.engine import (check_database_health, close_engine,
                                      get_engine)
# 异常处理
from svc.core.database.exceptions import (ConnectionError, DatabaseError,
                                          DataValidationError,
                                          ForeignKeyConstraintError,
                                          IntegrityConstraintError,
                                          MultipleRecordsFoundError,
                                          QueryExecutionError,
                                          RecordNotFoundError, SessionError,
                                          UniqueConstraintError,
                                          create_user_friendly_message,
                                          get_http_status_code,
                                          handle_database_errors,
                                          handle_repository_errors,
                                          is_connection_error,
                                          is_integrity_error,
                                          is_not_found_error,
                                          is_retryable_error, is_timeout_error,
                                          is_user_input_error,
                                          map_sqlalchemy_exception)
from svc.core.database.monitor import get_database_health, get_database_stats
from svc.core.database.session import create_session, get_session
from svc.core.database.transactions import (TransactionTimeoutError,
                                            with_transaction)
# 向后兼容的工具函数
from svc.core.database.utils import (get_session_for_event,
                                     get_session_for_route,
                                     get_session_for_script,
                                     resolve_dependencies_for_event)
# 基础数据库组件导入
from svc.core.models.base import Base

__all__ = [
    # 基础组件
    "Base",

    # 引擎管理
    "get_engine",
    "close_engine",
    "check_database_health",

    # 会话管理
    "get_db",
    "get_session",
    "create_session",

    # 监控功能
    "get_database_stats",
    "get_database_health",

    # 事务管理
    "with_transaction",
    "TransactionTimeoutError",

    # 异常处理
    "DatabaseError",
    "ConnectionError",
    "SessionError",
    "IntegrityConstraintError",
    "UniqueConstraintError",
    "ForeignKeyConstraintError",
    "RecordNotFoundError",
    "MultipleRecordsFoundError",
    "QueryExecutionError",
    "DataValidationError",
    "map_sqlalchemy_exception",
    "handle_database_errors",
    "handle_repository_errors",
    "is_integrity_error",
    "is_connection_error",
    "is_timeout_error",
    "is_not_found_error",
    "create_user_friendly_message",
    "get_http_status_code",
    "is_retryable_error",
    "is_user_input_error",

    # 向后兼容的工具函数
    "get_session_for_route",
    "get_session_for_script",
    "get_session_for_event",
    "resolve_dependencies_for_event",
]
