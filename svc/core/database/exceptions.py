"""
数据库相关异常模块

提供完整的数据库异常处理体系，包括：
- 基础异常类
- SQLAlchemy 异常映射
- 异常处理装饰器
- 异常分类和错误码映射
"""

import logging
from functools import wraps
from typing import Any, Dict, Optional, Type

from sqlalchemy.exc import DatabaseError as SQLAlchemyDatabaseError
from sqlalchemy.exc import (DataError, DisconnectionError, IntegrityError,
                            InvalidRequestError, MultipleResultsFound,
                            NoResultFound, OperationalError, ProgrammingError,
                            StatementError)
from sqlalchemy.exc import TimeoutError as SQLTimeoutError

logger = logging.getLogger(__name__)


# ============================================================================
# 基础异常类
# ============================================================================

class DatabaseError(Exception):
    """数据库基础异常"""

    def __init__(
        self,
        message: str,
        error_code: Optional[str] = None,
        original_error: Optional[Exception] = None,
        context: Optional[Dict[str, Any]] = None
    ):
        super().__init__(message)
        self.message = message
        self.error_code = error_code or "DATABASE_ERROR"
        self.original_error = original_error
        self.context = context or {}

    def __str__(self) -> str:
        return f"[{self.error_code}] {self.message}"

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式，便于日志记录和API响应"""
        return {
            "error_code": self.error_code,
            "message": self.message,
            "original_error": str(self.original_error) if self.original_error else None,
            "context": self.context
        }


class ConnectionError(DatabaseError):
    """数据库连接异常"""

    def __init__(self, message: str = "数据库连接失败", **kwargs):
        super().__init__(message, error_code="DB_CONNECTION_ERROR", **kwargs)


class SessionError(DatabaseError):
    """数据库会话异常"""

    def __init__(self, message: str = "数据库会话错误", **kwargs):
        super().__init__(message, error_code="DB_SESSION_ERROR", **kwargs)


class TransactionTimeoutError(DatabaseError):
    """事务超时异常"""

    def __init__(self, message: str = "事务执行超时", timeout_seconds: Optional[int] = None, **kwargs):
        if timeout_seconds:
            message = f"{message} ({timeout_seconds}秒)"
        super().__init__(message, error_code="DB_TRANSACTION_TIMEOUT", **kwargs)
        self.timeout_seconds = timeout_seconds


class IntegrityConstraintError(DatabaseError):
    """数据完整性约束异常"""

    def __init__(
        self,
        message: str = "数据完整性约束违反",
        constraint_name: Optional[str] = None,
        **kwargs
    ):
        # 如果子类没有设置 error_code，使用默认值
        if 'error_code' not in kwargs:
            kwargs['error_code'] = "DB_INTEGRITY_ERROR"
        super().__init__(message, **kwargs)
        self.constraint_name = constraint_name


class UniqueConstraintError(IntegrityConstraintError):
    """唯一约束异常"""

    def __init__(
        self,
        message: str = "唯一约束违反",
        field_name: Optional[str] = None,
        field_value: Optional[Any] = None,
        **kwargs
    ):
        if field_name and field_value:
            message = f"{message}: {field_name}='{field_value}' 已存在"
        # 设置特定的错误码，覆盖父类的默认错误码
        kwargs['error_code'] = "DB_UNIQUE_CONSTRAINT_ERROR"
        super().__init__(message, **kwargs)
        self.field_name = field_name
        self.field_value = field_value


class ForeignKeyConstraintError(IntegrityConstraintError):
    """外键约束异常"""

    def __init__(
        self,
        message: str = "外键约束违反",
        foreign_key: Optional[str] = None,
        **kwargs
    ):
        if foreign_key:
            message = f"{message}: 外键 '{foreign_key}' 引用的记录不存在"
        # 设置特定的错误码，覆盖父类的默认错误码
        kwargs['error_code'] = "DB_FOREIGN_KEY_ERROR"
        super().__init__(message, **kwargs)
        self.foreign_key = foreign_key


class RecordNotFoundError(DatabaseError):
    """记录未找到异常"""

    def __init__(
        self,
        message: str = "记录未找到",
        model_name: Optional[str] = None,
        record_id: Optional[Any] = None,
        **kwargs
    ):
        if model_name and record_id:
            message = f"{model_name} 记录未找到: ID={record_id}"
        super().__init__(message, error_code="DB_RECORD_NOT_FOUND", **kwargs)
        self.model_name = model_name
        self.record_id = record_id


class MultipleRecordsFoundError(DatabaseError):
    """找到多条记录异常（期望唯一结果时）"""

    def __init__(
        self,
        message: str = "找到多条记录，期望唯一结果",
        model_name: Optional[str] = None,
        **kwargs
    ):
        if model_name:
            message = f"{model_name}: {message}"
        super().__init__(message, error_code="DB_MULTIPLE_RECORDS_FOUND", **kwargs)
        self.model_name = model_name


class QueryExecutionError(DatabaseError):
    """查询执行异常"""

    def __init__(
        self,
        message: str = "查询执行失败",
        query: Optional[str] = None,
        **kwargs
    ):
        super().__init__(message, error_code="DB_QUERY_EXECUTION_ERROR", **kwargs)
        self.query = query


class DataValidationError(DatabaseError):
    """数据验证异常"""

    def __init__(
        self,
        message: str = "数据验证失败",
        field_name: Optional[str] = None,
        field_value: Optional[Any] = None,
        **kwargs
    ):
        if field_name:
            message = f"{message}: 字段 '{field_name}'"
            if field_value is not None:
                message += f" 值 '{field_value}'"
        super().__init__(message, error_code="DB_DATA_VALIDATION_ERROR", **kwargs)
        self.field_name = field_name
        self.field_value = field_value


# ============================================================================
# SQLAlchemy 异常映射
# ============================================================================

def _extract_constraint_info(error_msg: str) -> Dict[str, Optional[str]]:
    """从错误消息中提取约束信息"""
    import re

    # 提取唯一约束信息
    unique_match = re.search(r'UNIQUE constraint failed: (\w+)\.(\w+)', error_msg)
    if unique_match:
        return {
            'constraint_type': 'unique',
            'table_name': unique_match.group(1),
            'field_name': unique_match.group(2)
        }

    # 提取外键约束信息
    fk_match = re.search(r'FOREIGN KEY constraint failed', error_msg)
    if fk_match:
        return {
            'constraint_type': 'foreign_key',
            'table_name': None,
            'field_name': None
        }

    # 提取检查约束信息
    check_match = re.search(r'CHECK constraint failed: (\w+)', error_msg)
    if check_match:
        return {
            'constraint_type': 'check',
            'constraint_name': check_match.group(1),
            'field_name': None
        }

    return {'constraint_type': 'unknown', 'table_name': None, 'field_name': None}


def map_sqlalchemy_exception(exc: Exception) -> DatabaseError:
    """将 SQLAlchemy 异常映射为自定义数据库异常"""
    error_msg = str(exc)

    # 完整性约束错误
    if isinstance(exc, IntegrityError):
        constraint_info = _extract_constraint_info(error_msg)

        if constraint_info['constraint_type'] == 'unique':
            return UniqueConstraintError(
                message="唯一约束违反",
                field_name=constraint_info['field_name'],
                original_error=exc,
                context=constraint_info
            )
        elif constraint_info['constraint_type'] == 'foreign_key':
            return ForeignKeyConstraintError(
                message="外键约束违反",
                original_error=exc,
                context=constraint_info
            )
        else:
            return IntegrityConstraintError(
                message="数据完整性约束违反",
                constraint_name=constraint_info.get('constraint_name'),
                original_error=exc,
                context=constraint_info
            )

    # 操作错误（通常是连接问题）
    elif isinstance(exc, OperationalError):
        if "database is locked" in error_msg.lower():
            return DatabaseError(
                message="数据库被锁定，请稍后重试",
                error_code="DB_LOCKED",
                original_error=exc
            )
        elif "no such table" in error_msg.lower():
            return DatabaseError(
                message="数据表不存在",
                error_code="DB_TABLE_NOT_FOUND",
                original_error=exc
            )
        else:
            return ConnectionError(
                message="数据库操作失败",
                original_error=exc
            )

    # 连接断开错误
    elif isinstance(exc, DisconnectionError):
        return ConnectionError(
            message="数据库连接已断开",
            original_error=exc
        )

    # 超时错误
    elif isinstance(exc, SQLTimeoutError):
        return TransactionTimeoutError(
            message="数据库操作超时",
            original_error=exc
        )

    # 数据错误
    elif isinstance(exc, DataError):
        return DataValidationError(
            message="数据格式或类型错误",
            original_error=exc
        )

    # 编程错误（SQL语法错误等）
    elif isinstance(exc, ProgrammingError):
        return QueryExecutionError(
            message="SQL查询语法错误",
            original_error=exc
        )

    # 语句错误
    elif isinstance(exc, StatementError):
        return QueryExecutionError(
            message="SQL语句执行错误",
            original_error=exc
        )

    # 无效请求错误
    elif isinstance(exc, InvalidRequestError):
        return SessionError(
            message="数据库会话请求无效",
            original_error=exc
        )

    # 未找到结果
    elif isinstance(exc, NoResultFound):
        return RecordNotFoundError(
            message="查询未返回结果",
            original_error=exc
        )

    # 找到多个结果
    elif isinstance(exc, MultipleResultsFound):
        return MultipleRecordsFoundError(
            message="查询返回多个结果，期望唯一结果",
            original_error=exc
        )

    # 通用 SQLAlchemy 数据库错误
    elif isinstance(exc, SQLAlchemyDatabaseError):
        return DatabaseError(
            message="数据库操作失败",
            error_code="DB_OPERATION_ERROR",
            original_error=exc
        )

    # 其他异常
    else:
        return DatabaseError(
            message=f"未知数据库错误: {error_msg}",
            error_code="DB_UNKNOWN_ERROR",
            original_error=exc
        )


# ============================================================================
# 异常处理装饰器
# ============================================================================

def handle_database_errors(
    reraise_as: Optional[Type[DatabaseError]] = None,
    log_errors: bool = True,
    context: Optional[Dict[str, Any]] = None
):
    """
    数据库异常处理装饰器

    Args:
        reraise_as: 重新抛出为指定的异常类型
        log_errors: 是否记录错误日志
        context: 额外的上下文信息

    Usage:
        @handle_database_errors()
        async def create_user(self, user_data: dict):
            # 数据库操作
            pass

        @handle_database_errors(reraise_as=RecordNotFoundError, log_errors=False)
        async def get_user_by_id(self, user_id: int):
            # 查询操作
            pass
    """
    def decorator(func):
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            try:
                return await func(*args, **kwargs)
            except DatabaseError:
                # 如果已经是自定义数据库异常，直接重新抛出
                raise
            except Exception as exc:
                # 映射 SQLAlchemy 异常
                db_error = map_sqlalchemy_exception(exc)

                # 添加上下文信息
                if context:
                    db_error.context.update(context)

                # 记录错误日志
                if log_errors:
                    logger.error(
                        f"数据库操作失败 in {func.__name__}: {db_error}",
                        exc_info=True,
                        extra={
                            "function": func.__name__,
                            "error_code": db_error.error_code,
                            "context": db_error.context
                        }
                    )

                # 重新抛出为指定类型
                if reraise_as:
                    raise reraise_as(
                        message=db_error.message,
                        original_error=exc,
                        context=db_error.context
                    )
                else:
                    raise db_error

        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except DatabaseError:
                # 如果已经是自定义数据库异常，直接重新抛出
                raise
            except Exception as exc:
                # 映射 SQLAlchemy 异常
                db_error = map_sqlalchemy_exception(exc)

                # 添加上下文信息
                if context:
                    db_error.context.update(context)

                # 记录错误日志
                if log_errors:
                    logger.error(
                        f"数据库操作失败 in {func.__name__}: {db_error}",
                        exc_info=True,
                        extra={
                            "function": func.__name__,
                            "error_code": db_error.error_code,
                            "context": db_error.context
                        }
                    )

                # 重新抛出为指定类型
                if reraise_as:
                    raise reraise_as(
                        message=db_error.message,
                        original_error=exc,
                        context=db_error.context
                    )
                else:
                    raise db_error

        # 根据函数类型返回对应的包装器
        import asyncio
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper

    return decorator


def handle_repository_errors(model_name: Optional[str] = None):
    """
    仓储层专用的异常处理装饰器

    Args:
        model_name: 模型名称，用于生成更友好的错误消息

    Usage:
        class UserRepository(BaseRepository):
            @handle_repository_errors(model_name="User")
            async def create(self, user_data: dict):
                # 创建用户
                pass
    """
    def decorator(func):
        @handle_database_errors(
            context={"model_name": model_name, "operation": func.__name__}
        )
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            return await func(*args, **kwargs)

        @handle_database_errors(
            context={"model_name": model_name, "operation": func.__name__}
        )
        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            return func(*args, **kwargs)

        # 根据函数类型返回对应的包装器
        import asyncio
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper

    return decorator


# ============================================================================
# 工具函数
# ============================================================================

def is_integrity_error(exc: Exception) -> bool:
    """检查是否为完整性约束错误"""
    return isinstance(exc, (IntegrityError, IntegrityConstraintError,
                           UniqueConstraintError, ForeignKeyConstraintError))


def is_connection_error(exc: Exception) -> bool:
    """检查是否为连接错误"""
    return isinstance(exc, (OperationalError, DisconnectionError, ConnectionError))


def is_timeout_error(exc: Exception) -> bool:
    """检查是否为超时错误"""
    return isinstance(exc, (SQLTimeoutError, TransactionTimeoutError))


def is_not_found_error(exc: Exception) -> bool:
    """检查是否为记录未找到错误"""
    return isinstance(exc, (NoResultFound, RecordNotFoundError))


def extract_error_details(exc: Exception) -> Dict[str, Any]:
    """提取异常的详细信息"""
    if isinstance(exc, DatabaseError):
        return exc.to_dict()

    return {
        "error_type": type(exc).__name__,
        "message": str(exc),
        "original_error": str(exc)
    }


def create_user_friendly_message(exc: Exception) -> str:
    """创建用户友好的错误消息"""
    if isinstance(exc, UniqueConstraintError):
        if exc.field_name:
            return f"该{exc.field_name}已存在，请使用其他值"
        return "数据重复，请检查输入"

    elif isinstance(exc, ForeignKeyConstraintError):
        return "关联的数据不存在，请检查相关信息"

    elif isinstance(exc, RecordNotFoundError):
        if exc.model_name:
            return f"{exc.model_name}不存在"
        return "请求的数据不存在"

    elif isinstance(exc, ConnectionError):
        return "数据库连接失败，请稍后重试"

    elif isinstance(exc, TransactionTimeoutError):
        return "操作超时，请稍后重试"

    elif isinstance(exc, DataValidationError):
        if exc.field_name:
            return f"字段'{exc.field_name}'的数据格式不正确"
        return "数据格式不正确，请检查输入"

    elif isinstance(exc, DatabaseError):
        return "数据库操作失败，请稍后重试"

    else:
        return "系统错误，请联系管理员"


# ============================================================================
# 异常分类和错误码映射
# ============================================================================

# 错误码到HTTP状态码的映射
ERROR_CODE_TO_HTTP_STATUS = {
    "DB_RECORD_NOT_FOUND": 404,
    "DB_UNIQUE_CONSTRAINT_ERROR": 409,
    "DB_FOREIGN_KEY_ERROR": 400,
    "DB_INTEGRITY_ERROR": 400,
    "DB_DATA_VALIDATION_ERROR": 400,
    "DB_CONNECTION_ERROR": 503,
    "DB_TRANSACTION_TIMEOUT": 408,
    "DB_SESSION_ERROR": 500,
    "DB_QUERY_EXECUTION_ERROR": 500,
    "DATABASE_ERROR": 500,
    "DB_UNKNOWN_ERROR": 500,
}

# 可重试的错误类型
RETRYABLE_ERRORS = {
    ConnectionError,
    TransactionTimeoutError,
    OperationalError,
    DisconnectionError,
    SQLTimeoutError,
}

# 用户输入错误类型（不应该记录为系统错误）
USER_INPUT_ERRORS = {
    UniqueConstraintError,
    ForeignKeyConstraintError,
    DataValidationError,
    RecordNotFoundError,
}


def get_http_status_code(exc: Exception) -> int:
    """根据异常获取对应的HTTP状态码"""
    if isinstance(exc, DatabaseError):
        return ERROR_CODE_TO_HTTP_STATUS.get(exc.error_code, 500)

    # SQLAlchemy 异常的默认映射
    if isinstance(exc, NoResultFound):
        return 404
    elif isinstance(exc, IntegrityError):
        return 409
    elif isinstance(exc, (OperationalError, DisconnectionError)):
        return 503
    elif isinstance(exc, SQLTimeoutError):
        return 408
    else:
        return 500


def is_retryable_error(exc: Exception) -> bool:
    """检查错误是否可重试"""
    return any(isinstance(exc, error_type) for error_type in RETRYABLE_ERRORS)


def is_user_input_error(exc: Exception) -> bool:
    """检查是否为用户输入错误（不应记录为系统错误）"""
    return any(isinstance(exc, error_type) for error_type in USER_INPUT_ERRORS)


# ============================================================================
# 导出的异常类和函数
# ============================================================================

__all__ = [
    # 基础异常类
    "DatabaseError",
    "ConnectionError",
    "SessionError",
    "TransactionTimeoutError",

    # 具体异常类
    "IntegrityConstraintError",
    "UniqueConstraintError",
    "ForeignKeyConstraintError",
    "RecordNotFoundError",
    "MultipleRecordsFoundError",
    "QueryExecutionError",
    "DataValidationError",

    # 异常映射和处理
    "map_sqlalchemy_exception",
    "handle_database_errors",
    "handle_repository_errors",

    # 工具函数
    "is_integrity_error",
    "is_connection_error",
    "is_timeout_error",
    "is_not_found_error",
    "extract_error_details",
    "create_user_friendly_message",
    "get_http_status_code",
    "is_retryable_error",
    "is_user_input_error",

    # 常量
    "ERROR_CODE_TO_HTTP_STATUS",
    "RETRYABLE_ERRORS",
    "USER_INPUT_ERRORS",
]