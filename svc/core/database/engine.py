"""数据库引擎管理模块"""
import logging
from typing import Optional

from sqlalchemy.ext.asyncio import AsyncEngine, create_async_engine
from sqlalchemy.pool import AsyncAdaptedQueuePool

from svc.core.config.settings import get_settings

logger = logging.getLogger(__name__)

# 全局引擎实例
_engine: Optional[AsyncEngine] = None
_initialized = False

async def get_engine(testing: bool = False) -> AsyncEngine:
    """获取数据库引擎"""
    global _engine, _initialized

    if _initialized and _engine:
        return _engine

    settings = get_settings()
    db_uri = settings.db_test_uri if testing else settings.db_uri

    _engine = create_async_engine(
        db_uri,
        echo=settings.db_echo,
        future=True,
        pool_pre_ping=True,
        poolclass=AsyncAdaptedQueuePool,
        pool_size=settings.db_pool_size,
        max_overflow=settings.db_max_overflow,
        pool_timeout=settings.db_pool_timeout,
        pool_recycle=settings.db_pool_recycle
    )

    _initialized = True
    logger.info(f"数据库引擎已初始化，连接池大小: {settings.db_pool_size}")
    return _engine

async def close_engine() -> None:
    """关闭数据库引擎"""
    global _engine, _initialized

    if _engine:
        await _engine.dispose()
        _engine = None
        _initialized = False
        logger.info("数据库引擎已关闭")

async def check_database_health() -> bool:
    """检查数据库健康状态"""
    if not _engine:
        return False

    try:
        from sqlalchemy.future import select
        async with _engine.connect() as conn:
            await conn.execute(select(1))
        return True
    except Exception as e:
        logger.error(f"数据库健康检查失败: {e}")
        return False