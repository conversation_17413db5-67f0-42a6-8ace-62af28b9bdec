"""事务管理模块"""
import asyncio
import functools
import logging
from typing import Any, Callable, Optional, TypeVar

from sqlalchemy.ext.asyncio import AsyncSession

from .exceptions import TransactionTimeoutError
from .session import get_session

logger = logging.getLogger(__name__)

F = TypeVar('F', bound=Callable[..., Any])

def with_transaction(
    auto_commit: bool = True,
    timeout_seconds: Optional[int] = None
):
    """事务装饰器"""
    def decorator(func: F) -> F:
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            # 检查是否已有数据库会话
            db = kwargs.get('db')
            if db is not None:
                return await _execute_with_existing_session(
                    func, db, auto_commit, timeout_seconds, *args, **kwargs
                )
            else:
                return await _execute_with_new_session(
                    func, auto_commit, timeout_seconds, *args, **kwargs
                )
        return wrapper
    return decorator

async def _execute_with_existing_session(
    func: Callable,
    db: AsyncSession,
    auto_commit: bool,
    timeout_seconds: Optional[int],
    *args,
    **kwargs
):
    """使用现有会话执行函数"""
    try:
        if timeout_seconds:
            result = await asyncio.wait_for(
                func(*args, **kwargs),
                timeout=timeout_seconds
            )
        else:
            result = await func(*args, **kwargs)
        
        if auto_commit:
            await db.commit()
        return result
    except asyncio.TimeoutError:
        await db.rollback()
        raise TransactionTimeoutError(f"事务超时 ({timeout_seconds}秒)")
    except Exception:
        await db.rollback()
        raise

async def _execute_with_new_session(
    func: Callable,
    auto_commit: bool,
    timeout_seconds: Optional[int],
    *args,
    **kwargs
):
    """使用新会话执行函数"""
    async with get_session() as session:
        kwargs['db'] = session
        return await _execute_with_existing_session(
            func, session, auto_commit, timeout_seconds, *args, **kwargs
        )