from functools import wraps
from svc.core.exceptions.error_codes import Error<PERSON><PERSON>

def check_resource_exists(id_param: str = "id", error_code: int = ErrorCode.NOT_FOUND):
    def decorator(method):
        @wraps(method)
        async def wrapper(self, *args, **kwargs):
            resource_id = kwargs.get(id_param)
            if not resource_id:
                return self.create_error_result(
                    error_code=ErrorCode.INVALID_PARAMS,
                    error_message=f"缺少参数: {id_param}"
                )
            try:
                resource = await self.get_resource_by_id(resource_id)
                if not resource:
                    return self.resource_not_found_result(resource_id, error_code)
                kwargs["resource"] = resource
                return await method(self, *args, **kwargs)
            except Exception as e:
                self.logger.exception(f"检查资源存在时出错: {str(e)}")
                return self.create_error_result(
                    error_code=ErrorCode.INTERNAL_ERROR,
                    error_message=str(e)
                )
        return wrapper
    return decorator

def handle_service_errors(error_code: int = ErrorCode.OPERATION_FAILED):
    def decorator(method):
        @wraps(method)
        async def wrapper(self, *args, **kwargs):
            try:
                return await method(self, *args, **kwargs)
            except Exception as e:
                self.logger.exception(f"服务方法执行出错: {str(e)}")
                return self.create_error_result(
                    error_code=error_code,
                    error_message=str(e)
                )
        return wrapper
    return decorator 