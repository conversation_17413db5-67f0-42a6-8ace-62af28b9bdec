from typing import Any, Callable, Dict, List, Optional, TypeVar
from svc.core.models.result import Result
from svc.core.exceptions.error_codes import ErrorCode

class BatchOperationMixin(object):
    async def batch_update_resources(
        self,
        resource_ids: List[int],
        update_data: Dict[str, Any],
        repository: Any,
        resource_type: str = "资源",
        event_prefix: Optional[str] = None,
        cache_key_generator: Optional[Callable[[int], str]] = None
    ) -> Result:
        try:
            from fastapi_events.dispatcher import dispatch
            from svc.core.schemas.batch import BatchUpdateResponse
            self.logger.info(f"批量更新{resource_type}: 请求更新{len(resource_ids)}个{resource_type}")
            if not resource_ids:
                return self.create_error_result(
                    error_code=ErrorCode.INVALID_PARAMS,
                    error_message=f"{resource_type}ID列表不能为空"
                )
            if not update_data:
                return self.create_error_result(
                    error_code=ErrorCode.INVALID_PARAMS,
                    error_message="更新数据不能为空"
                )
            existing_ids = await repository.get_existing_ids(resource_ids)
            if not existing_ids:
                self.logger.warning(f"没有找到任何有效的{resource_type}ID: {resource_ids}")
                response = BatchUpdateResponse(
                    updated_count=0,
                    failed_ids=resource_ids,
                    total_requested=len(resource_ids)
                )
                return self.create_success_result(response)
            failed_ids = [rid for rid in resource_ids if rid not in existing_ids]
            updated_count = await repository.batch_update(existing_ids, update_data)
            if cache_key_generator and hasattr(self, 'redis') and self.redis:
                for resource_id in existing_ids:
                    cache_key = cache_key_generator(resource_id)
                    await self.redis.delete(cache_key)
            if event_prefix:
                dispatch(f"{event_prefix}:batch_updated", payload={
                    "updated_ids": existing_ids,
                    "update_data": update_data,
                    "updated_count": updated_count
                })
            response = BatchUpdateResponse(
                updated_count=updated_count,
                failed_ids=failed_ids,
                total_requested=len(resource_ids)
            )
            self.logger.info(f"批量更新{resource_type}完成: 成功更新{updated_count}个{resource_type}，失败{len(failed_ids)}个")
            return self.create_success_result(response)
        except Exception as e:
            self.logger.error(f"批量更新{resource_type}失败: {str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.UPDATE_FAILED,
                error_message=f"批量更新{resource_type}失败: {str(e)}"
            )

    async def batch_delete_resources(
        self,
        resource_ids: List[int],
        repository: Any,
        resource_type: str = "资源",
        soft_delete: bool = True,
        event_prefix: Optional[str] = None,
        cache_key_generator: Optional[Callable[[int], str]] = None
    ) -> Result:
        try:
            from fastapi_events.dispatcher import dispatch
            from svc.core.schemas.batch import BatchDeleteResponse
            self.logger.info(f"批量删除{resource_type}: 请求删除{len(resource_ids)}个{resource_type}")
            if not resource_ids:
                return self.create_error_result(
                    error_code=ErrorCode.INVALID_PARAMS,
                    error_message=f"{resource_type}ID列表不能为空"
                )
            existing_ids = await repository.get_existing_ids(resource_ids)
            if not existing_ids:
                self.logger.warning(f"没有找到任何有效的{resource_type}ID: {resource_ids}")
                response = BatchDeleteResponse(
                    deleted_count=0,
                    failed_ids=resource_ids,
                    total_requested=len(resource_ids)
                )
                return self.create_success_result(response)
            failed_ids = [rid for rid in resource_ids if rid not in existing_ids]
            deleted_count = await repository.batch_delete(existing_ids, soft_delete=soft_delete)
            if cache_key_generator and hasattr(self, 'redis') and self.redis:
                for resource_id in existing_ids:
                    cache_key = cache_key_generator(resource_id)
                    await self.redis.delete(cache_key)
            if event_prefix:
                dispatch(f"{event_prefix}:batch_deleted", payload={
                    "deleted_ids": existing_ids,
                    "deleted_count": deleted_count,
                    "soft_delete": soft_delete
                })
            response = BatchDeleteResponse(
                deleted_count=deleted_count,
                failed_ids=failed_ids,
                total_requested=len(resource_ids)
            )
            self.logger.info(f"批量删除{resource_type}完成: 成功删除{deleted_count}个{resource_type}，失败{len(failed_ids)}个")
            return self.create_success_result(response)
        except Exception as e:
            self.logger.error(f"批量删除{resource_type}失败: {str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.DELETE_FAILED,
                error_message=f"批量删除{resource_type}失败: {str(e)}"
            ) 