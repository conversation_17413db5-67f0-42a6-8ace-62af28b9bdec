"""
产品模块API接口测试
测试所有产品相关的API端点
"""
import pytest
from fastapi.testclient import TestClient
from httpx import AsyncClient


class TestProductAPI:
    """商品API测试"""
    
    @pytest.mark.asyncio
    async def test_admin_list_products(self, async_test_client: AsyncClient, admin_headers):
        """测试获取商品列表 (管理端)"""
        response = await async_test_client.get(
            "/products/admin/list",
            headers=admin_headers,
            params={"pageNum": 1, "pageSize": 10}
        )
        assert response.status_code == 200
        data = response.json()
        assert "data" in data
        assert "items" in data["data"]
        assert "total" in data["data"]
        assert "pageNum" in data["data"]
        assert "pageSize" in data["data"]
        assert "pageCount" in data["data"]
    
    @pytest.mark.asyncio
    async def test_admin_create_product(self, async_test_client: AsyncClient, admin_headers, sample_product_data):
        """测试创建商品 (管理端)"""
        response = await async_test_client.post(
            "/products/admin/create",
            headers=admin_headers,
            json=sample_product_data
        )
        assert response.status_code == 201
        data = response.json()
        assert data["success"] is True
        assert "data" in data
    
    @pytest.mark.asyncio
    async def test_admin_get_product(self, async_test_client: AsyncClient, admin_headers):
        """测试获取商品详情 (管理端)"""
        # 首先创建一个商品
        create_response = await async_test_client.post(
            "/products/admin/create",
            headers=admin_headers,
            json={
                "name": "测试商品",
                "price": 9999,
                "sku": "TEST-001"
            }
        )
        assert create_response.status_code == 201
        product_id = create_response.json()["data"]["id"]
        
        # 获取商品详情
        response = await async_test_client.get(
            f"/products/admin/{product_id}",
            headers=admin_headers
        )
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["data"]["id"] == product_id
    
    @pytest.mark.asyncio
    async def test_admin_update_product(self, async_test_client: AsyncClient, admin_headers):
        """测试更新商品 (管理端)"""
        # 首先创建一个商品
        create_response = await async_test_client.post(
            "/products/admin/create",
            headers=admin_headers,
            json={
                "name": "测试商品",
                "price": 9999,
                "sku": "TEST-002"
            }
        )
        assert create_response.status_code == 201
        product_id = create_response.json()["data"]["id"]
        
        # 更新商品
        update_data = {
            "name": "更新后的商品名称",
            "price": 12999
        }
        response = await async_test_client.put(
            f"/products/admin/{product_id}",
            headers=admin_headers,
            json=update_data
        )
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["data"]["name"] == "更新后的商品名称"
    
    @pytest.mark.asyncio
    async def test_admin_delete_product(self, async_test_client: AsyncClient, admin_headers):
        """测试删除商品 (管理端)"""
        # 首先创建一个商品
        create_response = await async_test_client.post(
            "/products/admin/create",
            headers=admin_headers,
            json={
                "name": "待删除商品",
                "price": 9999,
                "sku": "TEST-DELETE-001"
            }
        )
        assert create_response.status_code == 201
        product_id = create_response.json()["data"]["id"]
        
        # 删除商品
        response = await async_test_client.delete(
            f"/products/admin/{product_id}",
            headers=admin_headers
        )
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
    
    @pytest.mark.asyncio
    async def test_admin_batch_update_products(self, async_test_client: AsyncClient, admin_headers):
        """测试批量更新商品 (管理端)"""
        # 首先创建几个商品
        product_ids = []
        for i in range(3):
            create_response = await async_test_client.post(
                "/products/admin/create",
                headers=admin_headers,
                json={
                    "name": f"批量测试商品{i+1}",
                    "price": 9999,
                    "sku": f"BATCH-TEST-{i+1:03d}"
                }
            )
            assert create_response.status_code == 201
            product_ids.append(create_response.json()["data"]["id"])
        
        # 批量更新
        batch_data = {
            "ids": product_ids,
            "updates": {
                "status": "inactive",
                "is_featured": False
            }
        }
        response = await async_test_client.patch(
            "/products/admin/batch-update",
            headers=admin_headers,
            json=batch_data
        )
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "updated_count" in data["data"]
    
    @pytest.mark.asyncio
    async def test_client_list_products(self, async_test_client: AsyncClient):
        """测试获取商品列表 (客户端)"""
        response = await async_test_client.get(
            "/products/list",
            params={"pageNum": 1, "pageSize": 10}
        )
        assert response.status_code == 200
        data = response.json()
        assert "data" in data
        assert "items" in data["data"]
    
    @pytest.mark.asyncio
    async def test_client_get_product(self, async_test_client: AsyncClient, admin_headers):
        """测试获取商品详情 (客户端)"""
        # 首先创建一个商品
        create_response = await async_test_client.post(
            "/products/admin/create",
            headers=admin_headers,
            json={
                "name": "客户端测试商品",
                "price": 9999,
                "sku": "CLIENT-TEST-001",
                "status": "active"
            }
        )
        assert create_response.status_code == 201
        product_id = create_response.json()["data"]["id"]
        
        # 客户端获取商品详情
        response = await async_test_client.get(f"/products/{product_id}")
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["data"]["id"] == product_id
    
    @pytest.mark.asyncio
    async def test_client_list_products_by_category(self, async_test_client: AsyncClient, admin_headers):
        """测试按分类获取商品列表 (客户端)"""
        # 首先创建一个分类
        category_response = await async_test_client.post(
            "/categories/admin/create",
            headers=admin_headers,
            json={
                "name": "测试分类",
                "slug": "test-category",
                "status": "active"
            }
        )
        assert category_response.status_code == 201
        category_id = category_response.json()["data"]["id"]
        
        # 创建属于该分类的商品
        await async_test_client.post(
            "/products/admin/create",
            headers=admin_headers,
            json={
                "name": "分类测试商品",
                "price": 9999,
                "sku": "CATEGORY-TEST-001",
                "category_id": category_id,
                "status": "active"
            }
        )
        
        # 按分类获取商品
        response = await async_test_client.get(
            f"/products/category/{category_id}",
            params={"pageNum": 1, "pageSize": 10}
        )
        assert response.status_code == 200
        data = response.json()
        assert "data" in data
        assert "items" in data["data"]
    
    @pytest.mark.asyncio
    async def test_client_get_featured_products(self, async_test_client: AsyncClient, admin_headers):
        """测试获取推荐商品 (客户端)"""
        # 首先创建一个推荐商品
        await async_test_client.post(
            "/products/admin/create",
            headers=admin_headers,
            json={
                "name": "推荐商品",
                "price": 9999,
                "sku": "FEATURED-001",
                "is_featured": True,
                "status": "active"
            }
        )
        
        # 获取推荐商品
        response = await async_test_client.get(
            "/products/featured",
            params={"pageNum": 1, "pageSize": 10}
        )
        assert response.status_code == 200
        data = response.json()
        assert "data" in data
        assert "items" in data["data"]


class TestCategoryAPI:
    """分类API测试"""
    
    @pytest.mark.asyncio
    async def test_admin_list_categories(self, async_test_client: AsyncClient, admin_headers):
        """测试获取分类列表 (管理端)"""
        response = await async_test_client.get(
            "/categories/admin/list",
            headers=admin_headers,
            params={"pageNum": 1, "pageSize": 10}
        )
        assert response.status_code == 200
        data = response.json()
        assert "data" in data
        assert "items" in data["data"]
    
    @pytest.mark.asyncio
    async def test_admin_create_category(self, async_test_client: AsyncClient, admin_headers, sample_category_data):
        """测试创建分类 (管理端)"""
        response = await async_test_client.post(
            "/categories/admin/create",
            headers=admin_headers,
            json=sample_category_data
        )
        assert response.status_code == 201
        data = response.json()
        assert data["success"] is True
        assert "data" in data
    
    @pytest.mark.asyncio
    async def test_admin_get_category_tree(self, async_test_client: AsyncClient, admin_headers):
        """测试获取分类树 (管理端)"""
        response = await async_test_client.get(
            "/categories/admin/tree",
            headers=admin_headers
        )
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert isinstance(data["data"], list)
    
    @pytest.mark.asyncio
    async def test_client_list_categories(self, async_test_client: AsyncClient):
        """测试获取分类列表 (客户端)"""
        response = await async_test_client.get(
            "/categories/list",
            params={"pageNum": 1, "pageSize": 10}
        )
        assert response.status_code == 200
        data = response.json()
        assert "data" in data
        assert "items" in data["data"]
    
    @pytest.mark.asyncio
    async def test_client_get_category_tree(self, async_test_client: AsyncClient):
        """测试获取分类树 (客户端)"""
        response = await async_test_client.get("/categories/tree")
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert isinstance(data["data"], list)


class TestSpecAPI:
    """规格API测试"""

    @pytest.mark.asyncio
    async def test_admin_list_specs(self, async_test_client: AsyncClient, admin_headers):
        """测试获取规格列表 (管理端)"""
        response = await async_test_client.get(
            "/specs/admin/specs",
            headers=admin_headers,
            params={"pageNum": 1, "pageSize": 10}
        )
        assert response.status_code == 200
        data = response.json()
        assert "data" in data
        assert "items" in data["data"]

    @pytest.mark.asyncio
    async def test_admin_create_spec(self, async_test_client: AsyncClient, admin_headers, sample_spec_data):
        """测试创建规格 (管理端)"""
        response = await async_test_client.post(
            "/specs/admin/specs",
            headers=admin_headers,
            json=sample_spec_data
        )
        assert response.status_code == 201
        data = response.json()
        assert data["success"] is True
        assert "data" in data

    @pytest.mark.asyncio
    async def test_admin_get_spec(self, async_test_client: AsyncClient, admin_headers):
        """测试获取规格详情 (管理端)"""
        # 首先创建一个规格
        create_response = await async_test_client.post(
            "/specs/admin/specs",
            headers=admin_headers,
            json={
                "name": "测试规格",
                "type": "select",
                "status": "active"
            }
        )
        assert create_response.status_code == 201
        spec_id = create_response.json()["data"]["id"]

        # 获取规格详情
        response = await async_test_client.get(
            f"/specs/admin/specs/{spec_id}",
            headers=admin_headers
        )
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["data"]["id"] == spec_id

    @pytest.mark.asyncio
    async def test_admin_list_spec_options(self, async_test_client: AsyncClient, admin_headers):
        """测试获取规格选项列表 (管理端)"""
        # 首先创建一个规格
        spec_response = await async_test_client.post(
            "/specs/admin/specs",
            headers=admin_headers,
            json={
                "name": "颜色规格",
                "type": "select",
                "status": "active"
            }
        )
        assert spec_response.status_code == 201
        spec_id = spec_response.json()["data"]["id"]

        # 获取规格选项列表
        response = await async_test_client.get(
            f"/specs/admin/specs/{spec_id}/options",
            headers=admin_headers,
            params={"pageNum": 1, "pageSize": 10}
        )
        assert response.status_code == 200
        data = response.json()
        assert "data" in data
        assert "items" in data["data"]

    @pytest.mark.asyncio
    async def test_admin_create_spec_option(self, async_test_client: AsyncClient, admin_headers):
        """测试创建规格选项 (管理端)"""
        # 首先创建一个规格
        spec_response = await async_test_client.post(
            "/specs/admin/specs",
            headers=admin_headers,
            json={
                "name": "尺寸规格",
                "type": "select",
                "status": "active"
            }
        )
        assert spec_response.status_code == 201
        spec_id = spec_response.json()["data"]["id"]

        # 创建规格选项
        option_data = {
            "value": "大号",
            "display_name": "大号(L)",
            "sort_order": 1,
            "status": "active"
        }
        response = await async_test_client.post(
            f"/specs/admin/specs/{spec_id}/options",
            headers=admin_headers,
            json=option_data
        )
        assert response.status_code == 201
        data = response.json()
        assert data["success"] is True
        assert data["data"]["value"] == "大号"

    @pytest.mark.asyncio
    async def test_client_list_specs(self, async_test_client: AsyncClient):
        """测试获取规格列表 (客户端)"""
        response = await async_test_client.get(
            "/specs/specs",
            params={"pageNum": 1, "pageSize": 10}
        )
        assert response.status_code == 200
        data = response.json()
        assert "data" in data
        assert "items" in data["data"]

    @pytest.mark.asyncio
    async def test_client_list_spec_options(self, async_test_client: AsyncClient, admin_headers):
        """测试获取规格选项列表 (客户端)"""
        # 首先创建一个规格和选项
        spec_response = await async_test_client.post(
            "/specs/admin/specs",
            headers=admin_headers,
            json={
                "name": "材质规格",
                "type": "select",
                "status": "active"
            }
        )
        assert spec_response.status_code == 201
        spec_id = spec_response.json()["data"]["id"]

        # 创建规格选项
        await async_test_client.post(
            f"/specs/admin/specs/{spec_id}/options",
            headers=admin_headers,
            json={
                "value": "棉质",
                "display_name": "纯棉",
                "status": "active"
            }
        )

        # 客户端获取规格选项
        response = await async_test_client.get(
            f"/specs/specs/{spec_id}/options",
            params={"pageNum": 1, "pageSize": 10}
        )
        assert response.status_code == 200
        data = response.json()
        assert "data" in data
        assert "items" in data["data"]


class TestSKUAPI:
    """SKU API测试"""

    @pytest.mark.asyncio
    async def test_admin_list_skus(self, async_test_client: AsyncClient, admin_headers):
        """测试获取SKU列表 (管理端)"""
        response = await async_test_client.get(
            "/skus/admin/skus",
            headers=admin_headers,
            params={"pageNum": 1, "pageSize": 10}
        )
        assert response.status_code == 200
        data = response.json()
        assert "data" in data
        assert "items" in data["data"]

    @pytest.mark.asyncio
    async def test_admin_create_sku(self, async_test_client: AsyncClient, admin_headers):
        """测试创建SKU (管理端)"""
        # 首先创建一个商品
        product_response = await async_test_client.post(
            "/products/admin/create",
            headers=admin_headers,
            json={
                "name": "SKU测试商品",
                "price": 9999,
                "sku": "SKU-PRODUCT-001"
            }
        )
        assert product_response.status_code == 201
        product_id = product_response.json()["data"]["id"]

        # 创建SKU
        sku_data = {
            "product_id": product_id,
            "sku_code": "SKU-001-RED-L",
            "price": 9999,
            "stock_quantity": 50,
            "status": "active"
        }
        response = await async_test_client.post(
            "/skus/admin/skus",
            headers=admin_headers,
            json=sku_data
        )
        assert response.status_code == 201
        data = response.json()
        assert data["success"] is True
        assert data["data"]["sku_code"] == "SKU-001-RED-L"

    @pytest.mark.asyncio
    async def test_admin_get_sku(self, async_test_client: AsyncClient, admin_headers):
        """测试获取SKU详情 (管理端)"""
        # 首先创建商品和SKU
        product_response = await async_test_client.post(
            "/products/admin/create",
            headers=admin_headers,
            json={
                "name": "SKU详情测试商品",
                "price": 9999,
                "sku": "SKU-DETAIL-PRODUCT"
            }
        )
        product_id = product_response.json()["data"]["id"]

        sku_response = await async_test_client.post(
            "/skus/admin/skus",
            headers=admin_headers,
            json={
                "product_id": product_id,
                "sku_code": "SKU-DETAIL-001",
                "price": 9999,
                "stock_quantity": 30,
                "status": "active"
            }
        )
        assert sku_response.status_code == 201
        sku_id = sku_response.json()["data"]["id"]

        # 获取SKU详情
        response = await async_test_client.get(
            f"/skus/admin/skus/{sku_id}",
            headers=admin_headers
        )
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["data"]["id"] == sku_id

    @pytest.mark.asyncio
    async def test_admin_list_product_skus(self, async_test_client: AsyncClient, admin_headers):
        """测试获取产品的所有SKU (管理端)"""
        # 首先创建商品
        product_response = await async_test_client.post(
            "/products/admin/create",
            headers=admin_headers,
            json={
                "name": "多SKU测试商品",
                "price": 9999,
                "sku": "MULTI-SKU-PRODUCT"
            }
        )
        product_id = product_response.json()["data"]["id"]

        # 创建多个SKU
        for i in range(3):
            await async_test_client.post(
                "/skus/admin/skus",
                headers=admin_headers,
                json={
                    "product_id": product_id,
                    "sku_code": f"MULTI-SKU-{i+1:03d}",
                    "price": 9999 + i * 1000,
                    "stock_quantity": 20 + i * 10,
                    "status": "active"
                }
            )

        # 获取产品的所有SKU
        response = await async_test_client.get(
            f"/skus/admin/products/{product_id}/skus",
            headers=admin_headers,
            params={"pageNum": 1, "pageSize": 10}
        )
        assert response.status_code == 200
        data = response.json()
        assert "data" in data
        assert "items" in data["data"]
        assert len(data["data"]["items"]) == 3

    @pytest.mark.asyncio
    async def test_admin_batch_create_skus(self, async_test_client: AsyncClient, admin_headers):
        """测试批量创建SKU (管理端)"""
        # 首先创建商品
        product_response = await async_test_client.post(
            "/products/admin/create",
            headers=admin_headers,
            json={
                "name": "批量SKU测试商品",
                "price": 9999,
                "sku": "BATCH-SKU-PRODUCT"
            }
        )
        product_id = product_response.json()["data"]["id"]

        # 批量创建SKU
        skus_data = [
            {
                "product_id": product_id,
                "sku_code": f"BATCH-SKU-{i+1:03d}",
                "price": 9999 + i * 500,
                "stock_quantity": 25 + i * 5,
                "status": "active"
            }
            for i in range(5)
        ]

        response = await async_test_client.post(
            "/skus/admin/batch-create",
            headers=admin_headers,
            json=skus_data
        )
        assert response.status_code == 201
        data = response.json()
        assert data["success"] is True
        assert isinstance(data["data"], list)
        assert len(data["data"]) == 5

    @pytest.mark.asyncio
    async def test_client_list_product_skus(self, async_test_client: AsyncClient, admin_headers):
        """测试获取产品的所有可用SKU (客户端)"""
        # 首先创建商品和SKU
        product_response = await async_test_client.post(
            "/products/admin/create",
            headers=admin_headers,
            json={
                "name": "客户端SKU测试商品",
                "price": 9999,
                "sku": "CLIENT-SKU-PRODUCT",
                "status": "active"
            }
        )
        product_id = product_response.json()["data"]["id"]

        await async_test_client.post(
            "/skus/admin/skus",
            headers=admin_headers,
            json={
                "product_id": product_id,
                "sku_code": "CLIENT-SKU-001",
                "price": 9999,
                "stock_quantity": 40,
                "status": "active"
            }
        )

        # 客户端获取产品SKU
        response = await async_test_client.get(
            f"/skus/products/{product_id}/skus",
            params={"pageNum": 1, "pageSize": 10}
        )
        assert response.status_code == 200
        data = response.json()
        assert "data" in data
        assert "items" in data["data"]

    @pytest.mark.asyncio
    async def test_client_get_sku_details(self, async_test_client: AsyncClient, admin_headers):
        """测试获取SKU详情 (客户端)"""
        # 创建商品和SKU
        product_response = await async_test_client.post(
            "/products/admin/create",
            headers=admin_headers,
            json={
                "name": "客户端SKU详情测试",
                "price": 9999,
                "sku": "CLIENT-SKU-DETAIL",
                "status": "active"
            }
        )
        product_id = product_response.json()["data"]["id"]

        sku_response = await async_test_client.post(
            "/skus/admin/skus",
            headers=admin_headers,
            json={
                "product_id": product_id,
                "sku_code": "CLIENT-SKU-DETAIL-001",
                "price": 9999,
                "stock_quantity": 35,
                "status": "active"
            }
        )
        sku_id = sku_response.json()["data"]["id"]

        # 客户端获取SKU详情
        response = await async_test_client.get(f"/skus/skus/{sku_id}")
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["data"]["id"] == sku_id


class TestInventoryAPI:
    """库存API测试"""

    @pytest.mark.asyncio
    async def test_admin_list_inventories(self, async_test_client: AsyncClient, admin_headers):
        """测试获取库存列表 (管理端)"""
        response = await async_test_client.get(
            "/inventory/admin/list",
            headers=admin_headers,
            params={"pageNum": 1, "pageSize": 10}
        )
        assert response.status_code == 200
        data = response.json()
        assert "data" in data

    @pytest.mark.asyncio
    async def test_admin_create_inventory(self, async_test_client: AsyncClient, admin_headers):
        """测试创建库存记录 (管理端)"""
        # 首先创建商品
        product_response = await async_test_client.post(
            "/products/admin/create",
            headers=admin_headers,
            json={
                "name": "库存测试商品",
                "price": 9999,
                "sku": "INVENTORY-PRODUCT-001"
            }
        )
        product_id = product_response.json()["data"]["id"]

        # 创建库存记录
        inventory_data = {
            "product_id": product_id,
            "available_quantity": 100,
            "warehouse_location": "A-01-001",
            "batch_number": "BATCH-001",
            "cost_price": 5000,
            "status": "available"
        }
        response = await async_test_client.post(
            "/inventory/admin/create",
            headers=admin_headers,
            json=inventory_data
        )
        assert response.status_code == 201
        data = response.json()
        assert data["success"] is True
        assert data["data"]["available_quantity"] == 100

    @pytest.mark.asyncio
    async def test_admin_adjust_inventory(self, async_test_client: AsyncClient, admin_headers):
        """测试调整库存 (管理端)"""
        # 首先创建商品和库存
        product_response = await async_test_client.post(
            "/products/admin/create",
            headers=admin_headers,
            json={
                "name": "库存调整测试商品",
                "price": 9999,
                "sku": "INVENTORY-ADJUST-001"
            }
        )
        product_id = product_response.json()["data"]["id"]

        inventory_response = await async_test_client.post(
            "/inventory/admin/create",
            headers=admin_headers,
            json={
                "product_id": product_id,
                "available_quantity": 50,
                "warehouse_location": "A-02-001",
                "status": "available"
            }
        )
        inventory_id = inventory_response.json()["data"]["id"]

        # 调整库存
        adjustment_data = {
            "quantity_change": 25,
            "reason": "补货",
            "notes": "定期补货操作"
        }
        response = await async_test_client.post(
            f"/inventory/admin/{inventory_id}/adjust",
            headers=admin_headers,
            json=adjustment_data
        )
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True

    @pytest.mark.asyncio
    async def test_admin_reserve_inventory(self, async_test_client: AsyncClient, admin_headers):
        """测试预留库存 (管理端)"""
        # 首先创建商品和库存
        product_response = await async_test_client.post(
            "/products/admin/create",
            headers=admin_headers,
            json={
                "name": "库存预留测试商品",
                "price": 9999,
                "sku": "INVENTORY-RESERVE-001"
            }
        )
        product_id = product_response.json()["data"]["id"]

        inventory_response = await async_test_client.post(
            "/inventory/admin/create",
            headers=admin_headers,
            json={
                "product_id": product_id,
                "available_quantity": 80,
                "warehouse_location": "A-03-001",
                "status": "available"
            }
        )
        inventory_id = inventory_response.json()["data"]["id"]

        # 预留库存
        reservation_data = {
            "quantity": 15,
            "reason": "订单预留",
            "order_id": "ORDER-001",
            "expires_at": "2024-12-31T23:59:59"
        }
        response = await async_test_client.post(
            f"/inventory/admin/{inventory_id}/reserve",
            headers=admin_headers,
            json=reservation_data
        )
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True

    @pytest.mark.asyncio
    async def test_internal_get_product_inventory(self, async_test_client: AsyncClient, admin_headers):
        """测试获取商品库存 (内部API)"""
        # 首先创建商品和库存
        product_response = await async_test_client.post(
            "/products/admin/create",
            headers=admin_headers,
            json={
                "name": "内部API测试商品",
                "price": 9999,
                "sku": "INTERNAL-API-001"
            }
        )
        product_id = product_response.json()["data"]["id"]

        await async_test_client.post(
            "/inventory/admin/create",
            headers=admin_headers,
            json={
                "product_id": product_id,
                "available_quantity": 60,
                "warehouse_location": "A-04-001",
                "status": "available"
            }
        )

        # 获取商品库存
        response = await async_test_client.get(
            f"/inventory/internal/product/{product_id}",
            params={"pageNum": 1, "pageSize": 10}
        )
        assert response.status_code == 200
        data = response.json()
        assert "data" in data
        assert "items" in data["data"]


class TestIntegrationScenarios:
    """集成测试场景"""

    @pytest.mark.asyncio
    async def test_complete_product_workflow(self, async_test_client: AsyncClient, admin_headers):
        """测试完整的商品工作流程"""
        # 1. 创建分类
        category_response = await async_test_client.post(
            "/categories/admin/create",
            headers=admin_headers,
            json={
                "name": "集成测试分类",
                "slug": "integration-test-category",
                "status": "active"
            }
        )
        assert category_response.status_code == 201
        category_id = category_response.json()["data"]["id"]

        # 2. 创建规格
        spec_response = await async_test_client.post(
            "/specs/admin/specs",
            headers=admin_headers,
            json={
                "name": "集成测试规格",
                "type": "select",
                "status": "active"
            }
        )
        assert spec_response.status_code == 201
        spec_id = spec_response.json()["data"]["id"]

        # 3. 创建规格选项
        option_response = await async_test_client.post(
            f"/specs/admin/specs/{spec_id}/options",
            headers=admin_headers,
            json={
                "value": "集成测试选项",
                "display_name": "集成测试选项",
                "status": "active"
            }
        )
        assert option_response.status_code == 201

        # 4. 创建商品
        product_response = await async_test_client.post(
            "/products/admin/create",
            headers=admin_headers,
            json={
                "name": "集成测试商品",
                "price": 9999,
                "sku": "INTEGRATION-TEST-001",
                "category_id": category_id,
                "status": "active"
            }
        )
        assert product_response.status_code == 201
        product_id = product_response.json()["data"]["id"]

        # 5. 创建SKU
        sku_response = await async_test_client.post(
            "/skus/admin/skus",
            headers=admin_headers,
            json={
                "product_id": product_id,
                "sku_code": "INTEGRATION-SKU-001",
                "price": 9999,
                "stock_quantity": 100,
                "status": "active"
            }
        )
        assert sku_response.status_code == 201

        # 6. 创建库存
        inventory_response = await async_test_client.post(
            "/inventory/admin/create",
            headers=admin_headers,
            json={
                "product_id": product_id,
                "available_quantity": 100,
                "warehouse_location": "INTEGRATION-WAREHOUSE",
                "status": "available"
            }
        )
        assert inventory_response.status_code == 201

        # 7. 验证客户端可以正常获取数据
        client_product_response = await async_test_client.get(f"/products/{product_id}")
        assert client_product_response.status_code == 200

        client_category_response = await async_test_client.get(f"/products/category/{category_id}")
        assert client_category_response.status_code == 200

        # 8. 验证数据完整性
        product_data = client_product_response.json()["data"]
        assert product_data["name"] == "集成测试商品"
        assert product_data["category_id"] == category_id

        print("✅ 完整的商品工作流程测试通过")

    @pytest.mark.asyncio
    async def test_error_handling(self, async_test_client: AsyncClient, admin_headers):
        """测试错误处理"""
        # 测试获取不存在的商品
        response = await async_test_client.get("/products/99999")
        assert response.status_code == 404

        # 测试创建重复SKU的商品
        await async_test_client.post(
            "/products/admin/create",
            headers=admin_headers,
            json={
                "name": "重复SKU测试1",
                "price": 9999,
                "sku": "DUPLICATE-SKU-001"
            }
        )

        duplicate_response = await async_test_client.post(
            "/products/admin/create",
            headers=admin_headers,
            json={
                "name": "重复SKU测试2",
                "price": 9999,
                "sku": "DUPLICATE-SKU-001"
            }
        )
        assert duplicate_response.status_code == 400

        print("✅ 错误处理测试通过")
