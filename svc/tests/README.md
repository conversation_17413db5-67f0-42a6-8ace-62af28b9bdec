# 产品模块API测试文档

本文档介绍如何运行和使用产品模块的API测试。

## 📋 测试概览

产品模块包含以下API测试：

### 🧪 测试类别

1. **TestProductAPI** - 商品API测试
   - 管理端商品CRUD操作
   - 客户端商品查询
   - 批量操作
   - 分类筛选
   - 推荐商品

2. **TestCategoryAPI** - 分类API测试
   - 管理端分类管理
   - 客户端分类查询
   - 分类树结构

3. **TestSpecAPI** - 规格API测试
   - 规格管理
   - 规格选项管理
   - 客户端规格查询

4. **TestSKUAPI** - SKU API测试
   - SKU创建和管理
   - 批量SKU操作
   - 商品SKU关联
   - 规格选项SKU查询

5. **TestInventoryAPI** - 库存API测试
   - 库存记录管理
   - 库存调整
   - 库存预留
   - 内部API

6. **TestIntegrationScenarios** - 集成测试
   - 完整业务流程测试
   - 错误处理测试
   - 数据一致性验证

## 🚀 快速开始

### 1. 环境准备

确保已安装所需依赖：

```bash
pip install pytest pytest-asyncio httpx
```

### 2. 快速测试

运行快速API验证：

```bash
python scripts/quick_test_products.py
```

这个脚本会：
- 测试所有主要API端点的可访问性
- 验证响应状态码
- 检查基本功能是否正常

### 3. 完整测试

运行完整的API测试套件：

```bash
python scripts/test_products_api.py
```

## 📖 详细使用说明

### 运行特定测试类

```bash
# 只运行商品API测试
python scripts/test_products_api.py --test TestProductAPI

# 只运行分类API测试
python scripts/test_products_api.py --test TestCategoryAPI

# 只运行集成测试
python scripts/test_products_api.py --integration
```

### 使用pytest直接运行

```bash
# 运行所有产品测试
pytest svc/tests/test_products_api.py -v

# 运行特定测试类
pytest svc/tests/test_products_api.py::TestProductAPI -v

# 运行特定测试方法
pytest svc/tests/test_products_api.py::TestProductAPI::test_admin_create_product -v

# 运行测试并显示详细输出
pytest svc/tests/test_products_api.py -v -s

# 运行测试并生成覆盖率报告
pytest svc/tests/test_products_api.py --cov=svc.apps.products
```

### 查看可用测试

```bash
python scripts/test_products_api.py --list
```

## 🔧 测试配置

### 测试数据库

测试使用内存SQLite数据库，每个测试会话都会：
1. 创建新的数据库实例
2. 自动创建所有表结构
3. 测试结束后自动清理

### 认证模拟

测试中的认证头部是模拟的，实际使用时需要：
1. 实现真实的JWT token生成
2. 配置正确的权限验证
3. 更新`conftest.py`中的认证fixtures

### 测试数据

测试使用fixtures提供的示例数据：
- `sample_product_data` - 商品数据
- `sample_category_data` - 分类数据
- `sample_spec_data` - 规格数据
- `sample_sku_data` - SKU数据
- `sample_inventory_data` - 库存数据

## 📊 测试结果解读

### 状态码说明

- **200** - 请求成功
- **201** - 创建成功
- **400** - 请求参数错误
- **401** - 未认证（需要登录）
- **403** - 无权限
- **404** - 资源不存在
- **500** - 服务器内部错误

### 常见问题

1. **401 Unauthorized**
   - 原因：认证系统未正确配置
   - 解决：更新认证fixtures或跳过认证检查

2. **404 Not Found**
   - 原因：路由未正确注册或URL路径错误
   - 解决：检查路由配置和URL映射

3. **500 Internal Server Error**
   - 原因：服务器代码错误或数据库连接问题
   - 解决：检查日志和错误堆栈

## 🎯 测试最佳实践

### 1. 测试隔离

每个测试方法都应该：
- 独立运行，不依赖其他测试
- 使用独立的测试数据
- 清理测试产生的数据

### 2. 数据验证

测试应该验证：
- 响应状态码
- 响应数据结构
- 业务逻辑正确性
- 边界条件处理

### 3. 错误处理

测试应该覆盖：
- 正常流程
- 异常情况
- 边界条件
- 错误恢复

## 🔍 调试测试

### 启用详细日志

```bash
pytest svc/tests/test_products_api.py -v -s --log-cli-level=DEBUG
```

### 单步调试

在测试代码中添加断点：

```python
import pdb; pdb.set_trace()
```

### 查看测试覆盖率

```bash
pytest svc/tests/test_products_api.py --cov=svc.apps.products --cov-report=html
```

## 📝 扩展测试

### 添加新测试

1. 在`test_products_api.py`中添加新的测试方法
2. 使用适当的fixtures提供测试数据
3. 验证预期的行为和结果
4. 更新文档说明新测试的用途

### 添加新的测试数据

1. 在`conftest.py`中添加新的fixture
2. 确保数据符合模型定义
3. 考虑数据的可重用性

## 🚨 注意事项

1. **测试环境隔离**：测试不应影响生产数据
2. **认证安全**：不要在测试中使用真实的认证凭据
3. **性能考虑**：大量测试可能影响CI/CD性能
4. **数据一致性**：确保测试数据的完整性和一致性

## 📞 支持

如果遇到测试相关问题：

1. 检查测试日志和错误信息
2. 验证环境配置是否正确
3. 确认依赖项是否已安装
4. 查看相关API实现是否有变更
