# 定义Python版本作为参数
ARG PYTHON_VERSION=3.13

# 安装uv和依赖
FROM python:${PYTHON_VERSION}-slim AS builder

# 安装uv
RUN apt-get update && apt-get install -y curl && \
    curl -sSf https://astral.sh/uv/install.sh | sh && \
    ln -s /root/.cargo/bin/uv /usr/local/bin/uv

# 显示当前运行的命令
SHELL ["sh", "-exc"]

# 设置工作目录
WORKDIR /svc

# 复制依赖文件
COPY pyproject.toml uv.lock ./

# 安装依赖
RUN --mount=type=cache,target=/root/.cache/uv \
    uv sync --no-install-project --locked --no-dev

# 复制项目源代码到构建阶段
COPY . /svc

# 最终阶段，最小化镜像大小
FROM python:${PYTHON_VERSION}-slim

# 设置工作目录
WORKDIR /svc

# 设置环境变量
ENV PYTHONPATH=/app \
    PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1

# 安装运行时依赖
RUN apt-get update && apt-get install -y --no-install-recommends \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 从构建阶段复制虚拟环境和源代码
COPY --from=builder /svc/.venv /svc/.venv
COPY --from=builder /svc /svc

# 设置环境变量并将venv添加到PATH
ENV PATH="/svc/.venv/bin:$PATH"

# 暴露应用端口
EXPOSE 8000

# 健康检查
HEALTHCHECK --interval=30s --timeout=5s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# 应用入口点
ENTRYPOINT ["gunicorn", "svc.main:app", "--workers", "2", "--worker-class", "uvicorn.workers.UvicornWorker", "-b", "0.0.0.0:8000"] 