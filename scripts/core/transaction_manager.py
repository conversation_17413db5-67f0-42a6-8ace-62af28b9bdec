"""
事务管理器
提供分层事务管理、保存点机制和智能回滚策略
"""
import uuid
import logging
from typing import Dict, Optional, Set
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import text

from scripts.core.interfaces import ITransactionManager

logger = logging.getLogger(__name__)


class TransactionManager(ITransactionManager):
    """事务管理器实现"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
        self._savepoints: Dict[str, str] = {}
        self._active_savepoints: Set[str] = set()
        self._transaction_started = False
    
    async def start_transaction(self) -> None:
        """开始事务"""
        if not self._transaction_started:
            # SQLAlchemy的AsyncSession默认开启事务
            self._transaction_started = True
            logger.debug("事务已开始")
    
    async def create_savepoint(self, name: str) -> str:
        """创建保存点"""
        await self.start_transaction()
        
        savepoint_id = f"sp_{uuid.uuid4().hex[:8]}"
        savepoint_name = f"{name}_{savepoint_id}"
        
        try:
            await self.db.execute(text(f"SAVEPOINT {savepoint_name}"))
            self._savepoints[savepoint_id] = savepoint_name
            self._active_savepoints.add(savepoint_id)
            
            logger.debug(f"创建保存点: {savepoint_name}")
            return savepoint_id
        except Exception as e:
            logger.error(f"创建保存点失败: {e}")
            raise
    
    async def rollback_to_savepoint(self, savepoint_id: str) -> None:
        """回滚到保存点"""
        if savepoint_id not in self._savepoints:
            raise ValueError(f"保存点不存在: {savepoint_id}")
        
        if savepoint_id not in self._active_savepoints:
            raise ValueError(f"保存点已被释放: {savepoint_id}")
        
        savepoint_name = self._savepoints[savepoint_id]
        
        try:
            await self.db.execute(text(f"ROLLBACK TO SAVEPOINT {savepoint_name}"))
            
            # 移除此保存点之后创建的所有保存点
            to_remove = []
            for sp_id, sp_name in self._savepoints.items():
                if sp_id != savepoint_id and sp_id in self._active_savepoints:
                    # 简单的时间顺序判断（基于ID生成时间）
                    if sp_id > savepoint_id:
                        to_remove.append(sp_id)
            
            for sp_id in to_remove:
                self._active_savepoints.discard(sp_id)
            
            logger.debug(f"回滚到保存点: {savepoint_name}")
        except Exception as e:
            logger.error(f"回滚到保存点失败: {e}")
            raise
    
    async def release_savepoint(self, savepoint_id: str) -> None:
        """释放保存点"""
        if savepoint_id not in self._savepoints:
            raise ValueError(f"保存点不存在: {savepoint_id}")
        
        if savepoint_id not in self._active_savepoints:
            logger.warning(f"保存点已被释放: {savepoint_id}")
            return
        
        savepoint_name = self._savepoints[savepoint_id]
        
        try:
            await self.db.execute(text(f"RELEASE SAVEPOINT {savepoint_name}"))
            self._active_savepoints.remove(savepoint_id)
            
            logger.debug(f"释放保存点: {savepoint_name}")
        except Exception as e:
            logger.error(f"释放保存点失败: {e}")
            raise
    
    async def commit_transaction(self) -> None:
        """提交事务"""
        if not self._transaction_started:
            logger.warning("没有活动的事务可提交")
            return
        
        try:
            await self.db.commit()
            self._transaction_started = False
            self._savepoints.clear()
            self._active_savepoints.clear()
            
            logger.debug("事务已提交")
        except Exception as e:
            logger.error(f"事务提交失败: {e}")
            await self.rollback_transaction()
            raise
    
    async def rollback_transaction(self) -> None:
        """回滚事务"""
        if not self._transaction_started:
            logger.warning("没有活动的事务可回滚")
            return
        
        try:
            await self.db.rollback()
            self._transaction_started = False
            self._savepoints.clear()
            self._active_savepoints.clear()
            
            logger.debug("事务已回滚")
        except Exception as e:
            logger.error(f"事务回滚失败: {e}")
            raise
    
    async def flush(self) -> None:
        """刷新会话（不提交事务）"""
        try:
            await self.db.flush()
            logger.debug("会话已刷新")
        except Exception as e:
            logger.error(f"会话刷新失败: {e}")
            raise
    
    def get_savepoint_count(self) -> int:
        """获取活动保存点数量"""
        return len(self._active_savepoints)
    
    def is_transaction_active(self) -> bool:
        """检查是否有活动事务"""
        return self._transaction_started
    
    def get_savepoint_info(self) -> Dict[str, str]:
        """获取保存点信息"""
        return {
            sp_id: sp_name 
            for sp_id, sp_name in self._savepoints.items() 
            if sp_id in self._active_savepoints
        }


class NestedTransactionManager:
    """嵌套事务管理器，支持多层事务嵌套"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
        self._transaction_stack: list[TransactionManager] = []
        self._current_manager: Optional[TransactionManager] = None
    
    async def begin_nested(self, name: str = "nested") -> str:
        """开始嵌套事务"""
        if not self._current_manager:
            # 第一层事务
            self._current_manager = TransactionManager(self.db)
            await self._current_manager.start_transaction()
            savepoint_id = None
        else:
            # 嵌套事务，创建保存点
            savepoint_id = await self._current_manager.create_savepoint(name)
        
        # 创建新的事务管理器
        nested_manager = TransactionManager(self.db)
        nested_manager._transaction_started = True
        self._transaction_stack.append(nested_manager)
        
        return savepoint_id or "root_transaction"
    
    async def commit_nested(self, savepoint_id: Optional[str] = None) -> None:
        """提交嵌套事务"""
        if not self._transaction_stack:
            raise RuntimeError("没有嵌套事务可提交")
        
        nested_manager = self._transaction_stack.pop()
        
        if savepoint_id and savepoint_id != "root_transaction":
            # 释放保存点
            await self._current_manager.release_savepoint(savepoint_id)
        elif not self._transaction_stack:
            # 最后一层事务，提交整个事务
            await self._current_manager.commit_transaction()
            self._current_manager = None
    
    async def rollback_nested(self, savepoint_id: Optional[str] = None) -> None:
        """回滚嵌套事务"""
        if not self._transaction_stack:
            raise RuntimeError("没有嵌套事务可回滚")
        
        nested_manager = self._transaction_stack.pop()
        
        if savepoint_id and savepoint_id != "root_transaction":
            # 回滚到保存点
            await self._current_manager.rollback_to_savepoint(savepoint_id)
        else:
            # 回滚整个事务
            await self._current_manager.rollback_transaction()
            self._current_manager = None
    
    async def cleanup(self) -> None:
        """清理所有事务"""
        while self._transaction_stack:
            await self.rollback_nested()
        
        if self._current_manager and self._current_manager.is_transaction_active():
            await self._current_manager.rollback_transaction()
        
        self._current_manager = None
    
    def get_nesting_level(self) -> int:
        """获取嵌套层级"""
        return len(self._transaction_stack)
    
    def is_in_transaction(self) -> bool:
        """检查是否在事务中"""
        return self._current_manager is not None and self._current_manager.is_transaction_active()
