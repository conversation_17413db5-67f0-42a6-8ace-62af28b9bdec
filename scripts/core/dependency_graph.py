"""
数据依赖图管理器
管理种子数据之间的依赖关系，计算最优执行顺序，支持并行执行
"""
from typing import Dict, List, Set
from collections import defaultdict, deque

from scripts.core.interfaces import (
    IDependencyGraph, DataType, DataDependency
)


class DependencyGraph(IDependencyGraph):
    """依赖图管理器实现"""
    
    def __init__(self):
        self._dependencies: Dict[DataType, DataDependency] = {}
        self._graph: Dict[DataType, Set[DataType]] = defaultdict(set)
        self._reverse_graph: Dict[DataType, Set[DataType]] = defaultdict(set)
        self._initialized = False
        self._initialize_default_dependencies()
    
    def _initialize_default_dependencies(self) -> None:
        """初始化默认的依赖关系"""
        default_dependencies = [
            # 基础数据 - 无依赖
            DataDependency(DataType.PERMISSIONS, set(), can_parallel=True, priority=1),
            
            # 角色依赖权限
            DataDependency(DataType.ROLES, {DataType.PERMISSIONS}, can_parallel=False, priority=2),
            
            # 用户依赖角色
            DataDependency(DataType.USERS, {DataType.ROLES}, can_parallel=False, priority=3),
            
            # 微信用户依赖用户
            DataDependency(DataType.WECHAT_USERS, {DataType.USERS}, can_parallel=True, priority=4),
            
            # 订阅计划依赖用户
            DataDependency(DataType.SUBSCRIPTION_PLANS, {DataType.USERS}, can_parallel=True, priority=4),
            
            # 营销活动依赖用户
            DataDependency(DataType.CAMPAIGNS, {DataType.USERS}, can_parallel=True, priority=4),
            
            # 奖励策略依赖营销活动
            DataDependency(DataType.REWARD_STRATEGIES, {DataType.CAMPAIGNS}, can_parallel=True, priority=5),
            
            # 邀请依赖营销活动和用户
            DataDependency(DataType.INVITATIONS, {DataType.CAMPAIGNS, DataType.USERS}, can_parallel=True, priority=5),
            
            # 奖励记录依赖多个模块
            DataDependency(
                DataType.REWARD_RECORDS, 
                {DataType.CAMPAIGNS, DataType.USERS, DataType.INVITATIONS, DataType.REWARD_STRATEGIES}, 
                can_parallel=True, 
                priority=6
            ),
            
            # 分类 - 独立模块
            DataDependency(DataType.CATEGORIES, set(), can_parallel=True, priority=1),
            
            # 图册 - 独立模块
            DataDependency(DataType.ALBUMS, set(), can_parallel=True, priority=1),
            
            # 图册图片依赖图册
            DataDependency(DataType.ALBUM_IMAGES, {DataType.ALBUMS}, can_parallel=True, priority=2),
            
            # 产品依赖分类和图册
            DataDependency(DataType.PRODUCTS, {DataType.CATEGORIES, DataType.ALBUMS}, can_parallel=True, priority=3),
            
            # 产品规格依赖产品
            DataDependency(DataType.PRODUCT_SPECS, {DataType.PRODUCTS}, can_parallel=True, priority=4),
            
            # 门店依赖图册
            DataDependency(DataType.SHOPS, {DataType.ALBUMS}, can_parallel=True, priority=3),
            
            # 库存依赖产品
            DataDependency(DataType.INVENTORIES, {DataType.PRODUCTS}, can_parallel=True, priority=4),
        ]
        
        for dependency in default_dependencies:
            self.add_dependency(dependency)
        
        self._initialized = True
    
    def add_dependency(self, dependency: DataDependency) -> None:
        """添加依赖关系"""
        self._dependencies[dependency.data_type] = dependency
        
        # 构建依赖图
        for dep in dependency.depends_on:
            self._graph[dep].add(dependency.data_type)
            self._reverse_graph[dependency.data_type].add(dep)
    
    def get_execution_order(self) -> List[List[DataType]]:
        """
        获取执行顺序，返回分层列表，每层内的数据类型可以并行执行
        使用拓扑排序算法，考虑优先级和并行性
        """
        if not self._initialized:
            raise RuntimeError("依赖图未初始化")
        
        # 计算入度
        in_degree = defaultdict(int)
        all_types = set(self._dependencies.keys())
        
        for data_type in all_types:
            in_degree[data_type] = len(self._reverse_graph[data_type])
        
        # 按优先级分组
        priority_groups = defaultdict(list)
        for data_type, dependency in self._dependencies.items():
            priority_groups[dependency.priority].append(data_type)
        
        execution_order = []
        processed = set()
        
        # 按优先级处理
        for priority in sorted(priority_groups.keys()):
            current_level = []
            
            # 找到当前优先级中入度为0的节点
            for data_type in priority_groups[priority]:
                if data_type not in processed and in_degree[data_type] == 0:
                    current_level.append(data_type)
            
            # 如果当前优先级没有可执行的节点，检查其他优先级
            if not current_level:
                for other_priority in sorted(priority_groups.keys()):
                    if other_priority <= priority:
                        continue
                    for data_type in priority_groups[other_priority]:
                        if data_type not in processed and in_degree[data_type] == 0:
                            current_level.append(data_type)
                            break
                    if current_level:
                        break
            
            if current_level:
                # 按是否可并行分组
                parallel_types = []
                sequential_types = []
                
                for data_type in current_level:
                    dependency = self._dependencies[data_type]
                    if dependency.can_parallel:
                        parallel_types.append(data_type)
                    else:
                        sequential_types.append(data_type)
                
                # 先执行不可并行的（每个单独一层）
                for data_type in sequential_types:
                    execution_order.append([data_type])
                    processed.add(data_type)
                    
                    # 更新入度
                    for dependent in self._graph[data_type]:
                        in_degree[dependent] -= 1
                
                # 再执行可并行的（放在同一层）
                if parallel_types:
                    execution_order.append(parallel_types)
                    for data_type in parallel_types:
                        processed.add(data_type)
                        
                        # 更新入度
                        for dependent in self._graph[data_type]:
                            in_degree[dependent] -= 1
        
        # 检查是否还有未处理的节点（可能存在循环依赖）
        remaining = all_types - processed
        if remaining:
            # 尝试处理剩余节点
            while remaining:
                current_level = []
                for data_type in list(remaining):
                    if in_degree[data_type] == 0:
                        current_level.append(data_type)
                        remaining.remove(data_type)
                
                if not current_level:
                    # 存在循环依赖，强制处理
                    current_level = [remaining.pop()]
                
                execution_order.append(current_level)
                for data_type in current_level:
                    processed.add(data_type)
                    for dependent in self._graph[data_type]:
                        in_degree[dependent] -= 1
        
        return execution_order
    
    def validate_dependencies(self) -> bool:
        """验证依赖关系，检查循环依赖"""
        def has_cycle_util(node: DataType, visited: Set[DataType], rec_stack: Set[DataType]) -> bool:
            visited.add(node)
            rec_stack.add(node)
            
            for neighbor in self._graph[node]:
                if neighbor not in visited:
                    if has_cycle_util(neighbor, visited, rec_stack):
                        return True
                elif neighbor in rec_stack:
                    return True
            
            rec_stack.remove(node)
            return False
        
        visited = set()
        rec_stack = set()
        
        for data_type in self._dependencies.keys():
            if data_type not in visited:
                if has_cycle_util(data_type, visited, rec_stack):
                    return False
        
        return True
    
    def get_dependents(self, data_type: DataType) -> Set[DataType]:
        """获取依赖于指定类型的数据类型"""
        return self._graph[data_type].copy()
    
    def get_dependencies(self, data_type: DataType) -> Set[DataType]:
        """获取指定类型依赖的数据类型"""
        return self._reverse_graph[data_type].copy()
    
    def can_execute_parallel(self, data_types: List[DataType]) -> bool:
        """检查多个数据类型是否可以并行执行"""
        for data_type in data_types:
            if data_type not in self._dependencies:
                return False
            if not self._dependencies[data_type].can_parallel:
                return False
        
        # 检查是否存在相互依赖
        for i, type1 in enumerate(data_types):
            for type2 in data_types[i+1:]:
                if type2 in self.get_dependencies(type1) or type1 in self.get_dependencies(type2):
                    return False
        
        return True
    
    def get_execution_statistics(self) -> Dict[str, any]:
        """获取执行统计信息"""
        execution_order = self.get_execution_order()
        
        total_levels = len(execution_order)
        max_parallel = max(len(level) for level in execution_order) if execution_order else 0
        total_types = len(self._dependencies)
        
        parallel_types = sum(1 for dep in self._dependencies.values() if dep.can_parallel)
        sequential_types = total_types - parallel_types
        
        return {
            "total_data_types": total_types,
            "execution_levels": total_levels,
            "max_parallel_in_level": max_parallel,
            "parallel_types": parallel_types,
            "sequential_types": sequential_types,
            "has_cycles": not self.validate_dependencies(),
            "execution_order": [[dt.value for dt in level] for level in execution_order]
        }
