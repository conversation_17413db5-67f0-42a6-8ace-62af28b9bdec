"""
种子数据配置管理器
提供灵活的配置管理，支持环境特定配置和动态调整
"""
import os
from pathlib import Path
from typing import Any, Dict, Optional

import yaml

from scripts.core.interfaces import (BatchConfig, DataType,
                                     IConfigurationManager)


class ConfigurationManager(IConfigurationManager):
    """配置管理器实现"""
    
    def __init__(self, config_file: Optional[str] = None):
        self.config_file = config_file or self._get_default_config_file()
        self._config = self._load_config()
        self._validate_config()
    
    def _get_default_config_file(self) -> str:
        """获取默认配置文件路径"""
        env = os.getenv("ENVIRONMENT", "development")
        config_dir = Path(__file__).parent.parent / "config"
        return str(config_dir / f"seed_data_{env}.yaml")
    
    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            # 判断配置文件是否存在
            if os.path.exists(self.config_file):
                # 如果存在，则打开文件并读取内容
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    return yaml.safe_load(f) or {}
            # 如果不存在，则返回默认配置
            else:
                return self._get_default_config()
        except Exception as e:
            # 如果加载配置文件失败，则打印警告信息，并返回默认配置
            print(f"警告: 加载配置文件失败 {self.config_file}: {e}")
            return self._get_default_config()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            "global": {
                "batch_size": 1000,
                "max_memory_mb": 512,
                "parallel_workers": 4,
                "enable_streaming": True,
                "connection_pool_size": 10,
                "connection_timeout": 30
            },
            "data_types": {
                "permissions": {
                    "enabled": True,
                    "count": 70,
                    "batch_size": 100,
                    "parallel_workers": 1
                },
                "roles": {
                    "enabled": True,
                    "count": 3,
                    "batch_size": 10,
                    "parallel_workers": 1
                },
                "users": {
                    "enabled": True,
                    "count": 3,
                    "batch_size": 10,
                    "parallel_workers": 1
                },
                "wechat_users": {
                    "enabled": True,
                    "count": 2,
                    "batch_size": 10,
                    "parallel_workers": 1
                },
                "subscription_plans": {
                    "enabled": True,
                    "count": 3,
                    "batch_size": 10,
                    "parallel_workers": 2
                },
                "campaigns": {
                    "enabled": True,
                    "count": 3,
                    "batch_size": 10,
                    "parallel_workers": 2
                },
                "reward_strategies": {
                    "enabled": True,
                    "count": 5,
                    "batch_size": 10,
                    "parallel_workers": 2
                },
                "invitations": {
                    "enabled": True,
                    "count": 3,
                    "batch_size": 10,
                    "parallel_workers": 2
                },
                "reward_records": {
                    "enabled": True,
                    "count": 3,
                    "batch_size": 10,
                    "parallel_workers": 2
                },
                "categories": {
                    "enabled": True,
                    "count": 6,
                    "batch_size": 20,
                    "parallel_workers": 2
                },
                "albums": {
                    "enabled": True,
                    "count": 50,
                    "batch_size": 50,
                    "parallel_workers": 3
                },
                "album_images": {
                    "enabled": True,
                    "count": 150,
                    "batch_size": 100,
                    "parallel_workers": 4
                },
                "products": {
                    "enabled": True,
                    "count": 30,
                    "batch_size": 50,
                    "parallel_workers": 3
                },
                "product_specs": {
                    "enabled": True,
                    "count": 90,
                    "batch_size": 50,
                    "parallel_workers": 3
                },
                "shops": {
                    "enabled": True,
                    "count": 10,
                    "batch_size": 20,
                    "parallel_workers": 2
                },
                "inventories": {
                    "enabled": True,
                    "count": 120,
                    "batch_size": 100,
                    "parallel_workers": 3
                }
            }
        }
    
    def _validate_config(self) -> None:
        """验证配置有效性"""
        required_sections = ["global", "data_types"]
        for section in required_sections:
            if section not in self._config:
                raise ValueError(f"配置文件缺少必需的节: {section}")
        
        # 验证全局配置
        global_config = self._config["global"]
        required_global_keys = ["batch_size", "max_memory_mb", "parallel_workers"]
        for key in required_global_keys:
            if key not in global_config:
                raise ValueError(f"全局配置缺少必需的键: {key}")
    
    def get_batch_config(self, data_type: DataType) -> BatchConfig:
        """获取批处理配置"""
        global_config = self._config["global"]
        type_config = self._config["data_types"].get(data_type.value, {})
        
        return BatchConfig(
            batch_size=type_config.get("batch_size", global_config["batch_size"]),
            max_memory_mb=type_config.get("max_memory_mb", global_config["max_memory_mb"]),
            parallel_workers=type_config.get("parallel_workers", global_config["parallel_workers"]),
            enable_streaming=type_config.get("enable_streaming", global_config.get("enable_streaming", True))
        )
    
    def get_data_count(self, data_type: DataType) -> int:
        """获取数据数量配置"""
        type_config = self._config["data_types"].get(data_type.value, {})
        return type_config.get("count", 0)
    
    def is_enabled(self, data_type: DataType) -> bool:
        """检查数据类型是否启用"""
        type_config = self._config["data_types"].get(data_type.value, {})
        return type_config.get("enabled", True)
    
    def get_connection_config(self) -> Dict[str, Any]:
        """获取数据库连接配置"""
        global_config = self._config["global"]
        return {
            "pool_size": global_config.get("connection_pool_size", 10),
            "timeout": global_config.get("connection_timeout", 30),
            "max_overflow": global_config.get("connection_max_overflow", 20),
            "pool_pre_ping": global_config.get("connection_pool_pre_ping", True)
        }
    
    def get_config_value(self, key: str, default: Any = None) -> Any:
        """获取配置值（支持点号分隔的嵌套键）"""
        keys = key.split('.')
        value = self._config
        
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
        
        return value
    
    def update_config(self, key: str, value: Any) -> None:
        """更新配置值（运行时动态调整）"""
        keys = key.split('.')
        config = self._config
        
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        config[keys[-1]] = value
    
    def save_config(self, file_path: Optional[str] = None) -> None:
        """保存配置到文件"""
        target_file = file_path or self.config_file
        
        # 确保目录存在
        os.makedirs(os.path.dirname(target_file), exist_ok=True)
        
        with open(target_file, 'w', encoding='utf-8') as f:
            yaml.dump(self._config, f, default_flow_style=False, allow_unicode=True)
    
    def reload_config(self) -> None:
        """重新加载配置"""
        self._config = self._load_config()
        self._validate_config()
    
    def get_environment_info(self) -> Dict[str, Any]:
        """获取环境信息"""
        return {
            "config_file": self.config_file,
            "environment": os.getenv("ENVIRONMENT", "development"),
            "config_loaded": bool(self._config),
            "total_data_types": len(self._config.get("data_types", {})),
            "enabled_data_types": len([
                dt for dt, config in self._config.get("data_types", {}).items()
                if config.get("enabled", True)
            ])
        }
