"""
种子数据编排器
协调整个种子数据创建流程，支持并行执行、错误恢复和进度跟踪
"""
import asyncio
import logging
import time
from typing import Dict, List, Optional, Type

from sqlalchemy.ext.asyncio import AsyncSession

from scripts.core.configuration import ConfigurationManager
from scripts.core.dependency_graph import DependencyGraph
from scripts.core.interfaces import (DataType, ExecutionResult,
                                     ExecutionStatus, IDataFactory,
                                     ISeedDataOrchestrator)
from scripts.core.progress_tracker import ProgressTracker
from scripts.core.transaction_manager import NestedTransactionManager

logger = logging.getLogger(__name__)


class SeedDataOrchestrator(ISeedDataOrchestrator):
    """种子数据编排器实现"""
    
    def __init__(
        self, 
        db: AsyncSession,
        config_manager: Optional[ConfigurationManager] = None
    ):
        self.db = db
        self.config_manager = config_manager or ConfigurationManager()
        self.dependency_graph = DependencyGraph()
        self.transaction_manager = NestedTransactionManager(db)
        self.progress_tracker = ProgressTracker(db)
        
        # 注册的数据工厂
        self._factories: Dict[DataType, IDataFactory] = {}
        self._execution_context: Dict[str, any] = {}
        
        # 性能监控
        self._start_time: Optional[float] = None
        self._execution_stats: Dict[str, any] = {}
    
    def register_factory(self, factory: IDataFactory) -> None:
        """注册数据工厂"""
        self._factories[factory.data_type] = factory
        logger.debug(f"注册数据工厂: {factory.data_type.value}")
    
    def register_factories(self, factories: List[IDataFactory]) -> None:
        """批量注册数据工厂"""
        for factory in factories:
            self.register_factory(factory)
    
    async def execute_full_seed(
        self, 
        execution_id: Optional[str] = None,
        resume: bool = False
    ) -> Dict[DataType, ExecutionResult]:
        """执行完整的种子数据创建"""
        if not execution_id:
            execution_id = self.progress_tracker.generate_execution_id()
        
        logger.info(f"开始执行完整种子数据创建，执行ID: {execution_id}")
        
        # 检查是否可以恢复
        if resume and not await self.progress_tracker.can_resume(execution_id):
            logger.warning(f"无法恢复执行 {execution_id}，将开始新的执行")
            resume = False
        
        # 获取执行顺序
        execution_order = self.dependency_graph.get_execution_order()
        enabled_types = [
            dt for dt in DataType 
            if self.config_manager.is_enabled(dt) and dt in self._factories
        ]
        
        # 过滤执行顺序，只包含启用的数据类型
        filtered_order = []
        for level in execution_order:
            filtered_level = [dt for dt in level if dt in enabled_types]
            if filtered_level:
                filtered_order.append(filtered_level)
        
        return await self._execute_ordered_creation(
            execution_id, filtered_order, resume
        )
    
    async def execute_partial_seed(
        self, 
        data_types: List[DataType],
        execution_id: Optional[str] = None
    ) -> Dict[DataType, ExecutionResult]:
        """执行部分种子数据创建"""
        if not execution_id:
            execution_id = self.progress_tracker.generate_execution_id()
        
        logger.info(f"开始执行部分种子数据创建: {[dt.value for dt in data_types]}")
        
        # 计算依赖关系，确保依赖的数据类型也被包含
        required_types = set(data_types)
        for data_type in data_types:
            if data_type in self._factories:
                required_types.update(self._factories[data_type].dependencies)
        
        # 获取执行顺序并过滤
        execution_order = self.dependency_graph.get_execution_order()
        filtered_order = []
        for level in execution_order:
            filtered_level = [dt for dt in level if dt in required_types]
            if filtered_level:
                filtered_order.append(filtered_level)
        
        return await self._execute_ordered_creation(
            execution_id, filtered_order, resume=False
        )
    
    async def _execute_ordered_creation(
        self,
        execution_id: str,
        execution_order: List[List[DataType]],
        resume: bool = False
    ) -> Dict[DataType, ExecutionResult]:
        """按顺序执行数据创建"""
        self._start_time = time.time()
        results: Dict[DataType, ExecutionResult] = {}
        
        try:
            # 开始执行跟踪
            if not resume:
                await self.progress_tracker.start_execution(execution_id)
            else:
                # 获取已有进度
                existing_progress = await self.progress_tracker.get_progress(execution_id)
                results.update(existing_progress)
            
            # 开始事务
            root_transaction = await self.transaction_manager.begin_nested("root")
            
            try:
                for level_index, level in enumerate(execution_order):
                    logger.info(f"执行第 {level_index + 1} 层: {[dt.value for dt in level]}")
                    
                    # 检查是否可以并行执行
                    if len(level) > 1 and self.dependency_graph.can_execute_parallel(level):
                        # 并行执行
                        level_results = await self._execute_parallel_level(
                            execution_id, level, results
                        )
                    else:
                        # 串行执行
                        level_results = await self._execute_sequential_level(
                            execution_id, level, results
                        )
                    
                    results.update(level_results)
                    
                    # 检查是否有失败的数据类型
                    failed_types = [
                        dt for dt, result in level_results.items()
                        if result.status == ExecutionStatus.FAILED
                    ]
                    
                    if failed_types:
                        logger.error(f"第 {level_index + 1} 层执行失败: {[dt.value for dt in failed_types]}")
                        # 根据配置决定是否继续执行
                        if not self.config_manager.get_config_value("global.continue_on_error", False):
                            break
                
                # 提交事务
                await self.transaction_manager.commit_nested(root_transaction)
                
                # 标记完成
                await self.progress_tracker.mark_completed(execution_id)
                
            except Exception as e:
                logger.error(f"执行过程中发生错误: {e}")
                await self.transaction_manager.rollback_nested(root_transaction)
                raise
            
        except Exception as e:
            logger.error(f"种子数据创建失败: {e}")
            # 更新失败状态
            for data_type in [dt for level in execution_order for dt in level]:
                if data_type not in results:
                    results[data_type] = ExecutionResult(
                        data_type=data_type,
                        status=ExecutionStatus.FAILED,
                        error_message=str(e)
                    )
            raise
        finally:
            # 清理事务管理器
            await self.transaction_manager.cleanup()
            
            # 记录执行统计
            total_time = time.time() - self._start_time if self._start_time else 0
            self._execution_stats = {
                "execution_id": execution_id,
                "total_time": total_time,
                "total_types": len(results),
                "successful_types": len([r for r in results.values() if r.status == ExecutionStatus.COMPLETED]),
                "failed_types": len([r for r in results.values() if r.status == ExecutionStatus.FAILED]),
                "total_records": sum(r.created_count for r in results.values())
            }
            
            logger.info(f"执行完成，统计信息: {self._execution_stats}")
        
        return results
    
    async def _execute_parallel_level(
        self,
        execution_id: str,
        level: List[DataType],
        existing_results: Dict[DataType, ExecutionResult]
    ) -> Dict[DataType, ExecutionResult]:
        """并行执行一个层级的数据类型"""
        logger.info(f"并行执行数据类型: {[dt.value for dt in level]}")
        
        # 创建并行任务
        tasks = []
        for data_type in level:
            if data_type not in existing_results or existing_results[data_type].status != ExecutionStatus.COMPLETED:
                task = self._execute_single_type(execution_id, data_type)
                tasks.append((data_type, task))
        
        # 等待所有任务完成
        results = {}
        if tasks:
            task_results = await asyncio.gather(
                *[task for _, task in tasks], 
                return_exceptions=True
            )
            
            for (data_type, _), result in zip(tasks, task_results):
                if isinstance(result, Exception):
                    results[data_type] = ExecutionResult(
                        data_type=data_type,
                        status=ExecutionStatus.FAILED,
                        error_message=str(result)
                    )
                else:
                    results[data_type] = result
        
        return results
    
    async def _execute_sequential_level(
        self,
        execution_id: str,
        level: List[DataType],
        existing_results: Dict[DataType, ExecutionResult]
    ) -> Dict[DataType, ExecutionResult]:
        """串行执行一个层级的数据类型"""
        logger.info(f"串行执行数据类型: {[dt.value for dt in level]}")
        
        results = {}
        for data_type in level:
            if data_type not in existing_results or existing_results[data_type].status != ExecutionStatus.COMPLETED:
                try:
                    result = await self._execute_single_type(execution_id, data_type)
                    results[data_type] = result
                except Exception as e:
                    logger.error(f"执行数据类型 {data_type.value} 失败: {e}")
                    results[data_type] = ExecutionResult(
                        data_type=data_type,
                        status=ExecutionStatus.FAILED,
                        error_message=str(e)
                    )
                    
                    # 根据配置决定是否继续
                    if not self.config_manager.get_config_value("global.continue_on_error", False):
                        break
        
        return results

    async def _execute_single_type(
        self,
        execution_id: str,
        data_type: DataType
    ) -> ExecutionResult:
        """执行单个数据类型的创建"""
        start_time = time.time()
        factory = self._factories.get(data_type)

        if not factory:
            error_msg = f"未找到数据类型 {data_type.value} 的工厂"
            logger.error(error_msg)
            return ExecutionResult(
                data_type=data_type,
                status=ExecutionStatus.FAILED,
                error_message=error_msg
            )

        try:
            # 更新进度为运行中
            running_result = ExecutionResult(
                data_type=data_type,
                status=ExecutionStatus.RUNNING
            )
            await self.progress_tracker.update_progress(execution_id, data_type, running_result)

            # 检查是否已存在
            if await factory.check_exists(self.db):
                logger.info(f"数据类型 {data_type.value} 已存在，跳过创建")
                result = ExecutionResult(
                    data_type=data_type,
                    status=ExecutionStatus.SKIPPED,
                    execution_time=time.time() - start_time
                )
            else:
                # 生成数据
                data = await factory.generate_data(self._execution_context)

                # 验证数据
                if not await factory.validate_data(data):
                    raise ValueError("数据验证失败")

                # 创建数据
                created_objects = await factory.create_batch(self.db, data)

                result = ExecutionResult(
                    data_type=data_type,
                    status=ExecutionStatus.COMPLETED,
                    created_count=len(created_objects),
                    execution_time=time.time() - start_time,
                    memory_usage=await factory.estimate_memory_usage(len(data))
                )

                # 更新执行上下文
                self._execution_context[data_type.value] = created_objects

            # 更新进度
            await self.progress_tracker.update_progress(execution_id, data_type, result)

            logger.info(f"数据类型 {data_type.value} 执行完成: {result.status.value}")
            return result

        except Exception as e:
            logger.error(f"执行数据类型 {data_type.value} 失败: {e}")

            # 尝试清理
            try:
                await factory.cleanup_on_error(self.db)
            except Exception as cleanup_error:
                logger.error(f"清理失败: {cleanup_error}")

            error_result = ExecutionResult(
                data_type=data_type,
                status=ExecutionStatus.FAILED,
                error_message=str(e),
                execution_time=time.time() - start_time
            )

            await self.progress_tracker.update_progress(execution_id, data_type, error_result)
            return error_result

    async def cleanup_all_data(self) -> None:
        """清理所有种子数据"""
        logger.warning("开始清理所有种子数据...")

        # 按依赖关系的逆序清理
        execution_order = self.dependency_graph.get_execution_order()
        cleanup_order = list(reversed(execution_order))

        for level in cleanup_order:
            for data_type in level:
                if data_type in self._factories:
                    try:
                        factory = self._factories[data_type]
                        await factory.cleanup_on_error(self.db)
                        logger.info(f"已清理数据类型: {data_type.value}")
                    except Exception as e:
                        logger.error(f"清理数据类型 {data_type.value} 失败: {e}")

        await self.db.commit()
        logger.info("数据清理完成")

    async def validate_data_integrity(self) -> Dict[DataType, bool]:
        """验证数据完整性"""
        logger.info("开始验证数据完整性...")

        integrity_results = {}
        for data_type, factory in self._factories.items():
            try:
                exists = await factory.check_exists(self.db)
                integrity_results[data_type] = exists

                if exists:
                    logger.debug(f"数据类型 {data_type.value} 完整性检查通过")
                else:
                    logger.warning(f"数据类型 {data_type.value} 不存在或不完整")

            except Exception as e:
                logger.error(f"验证数据类型 {data_type.value} 完整性失败: {e}")
                integrity_results[data_type] = False

        return integrity_results

    def get_execution_statistics(self) -> Dict[str, any]:
        """获取执行统计信息"""
        stats = self._execution_stats.copy()
        stats.update({
            "registered_factories": len(self._factories),
            "dependency_stats": self.dependency_graph.get_execution_statistics(),
            "config_info": self.config_manager.get_environment_info()
        })
        return stats

    async def get_execution_report(self, execution_id: str) -> Dict[str, any]:
        """获取详细的执行报告"""
        report = await self.progress_tracker.get_execution_report(execution_id)
        report["statistics"] = self.get_execution_statistics()
        return report
