"""
种子数据系统核心接口定义
定义了重构后的种子数据系统的核心抽象接口
"""
from abc import ABC, abstractmethod
from enum import Enum
from typing import Any, Dict, List, Optional, Set, Tuple, Union
from dataclasses import dataclass
from sqlalchemy.ext.asyncio import AsyncSession


class DataType(Enum):
    """数据类型枚举"""
    PERMISSIONS = "permissions"
    ROLES = "roles"
    USERS = "users"
    WECHAT_USERS = "wechat_users"
    SUBSCRIPTION_PLANS = "subscription_plans"
    CAMPAIGNS = "campaigns"
    REWARD_STRATEGIES = "reward_strategies"
    INVITATIONS = "invitations"
    REWARD_RECORDS = "reward_records"
    CATEGORIES = "categories"
    ALBUMS = "albums"
    ALBUM_IMAGES = "album_images"
    PRODUCTS = "products"
    PRODUCT_SPECS = "product_specs"
    SHOPS = "shops"
    INVENTORIES = "inventories"


class ExecutionStatus(Enum):
    """执行状态枚举"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    SKIPPED = "skipped"


@dataclass
class DataDependency:
    """数据依赖关系"""
    data_type: DataType
    depends_on: Set[DataType]
    can_parallel: bool = True
    priority: int = 0


@dataclass
class ExecutionResult:
    """执行结果"""
    data_type: DataType
    status: ExecutionStatus
    created_count: int = 0
    error_message: Optional[str] = None
    execution_time: float = 0.0
    memory_usage: Optional[int] = None


@dataclass
class BatchConfig:
    """批处理配置"""
    batch_size: int = 1000
    max_memory_mb: int = 512
    parallel_workers: int = 4
    enable_streaming: bool = True


class IDataFactory(ABC):
    """数据工厂接口"""
    
    @property
    @abstractmethod
    def data_type(self) -> DataType:
        """数据类型"""
        pass
    
    @property
    @abstractmethod
    def dependencies(self) -> Set[DataType]:
        """依赖的数据类型"""
        pass
    
    @abstractmethod
    async def check_exists(self, db: AsyncSession) -> bool:
        """检查数据是否已存在"""
        pass
    
    @abstractmethod
    async def generate_data(self, context: Dict[str, Any]) -> List[Dict[str, Any]]:
        """生成数据"""
        pass
    
    @abstractmethod
    async def create_batch(
        self, 
        db: AsyncSession, 
        data_batch: List[Dict[str, Any]]
    ) -> List[Any]:
        """批量创建数据"""
        pass
    
    @abstractmethod
    async def validate_data(self, data: List[Dict[str, Any]]) -> bool:
        """验证数据有效性"""
        pass
    
    @abstractmethod
    async def cleanup_on_error(self, db: AsyncSession) -> None:
        """错误时清理数据"""
        pass


class ITransactionManager(ABC):
    """事务管理器接口"""
    
    @abstractmethod
    async def create_savepoint(self, name: str) -> str:
        """创建保存点"""
        pass
    
    @abstractmethod
    async def rollback_to_savepoint(self, savepoint_id: str) -> None:
        """回滚到保存点"""
        pass
    
    @abstractmethod
    async def release_savepoint(self, savepoint_id: str) -> None:
        """释放保存点"""
        pass
    
    @abstractmethod
    async def commit_transaction(self) -> None:
        """提交事务"""
        pass
    
    @abstractmethod
    async def rollback_transaction(self) -> None:
        """回滚事务"""
        pass


class IProgressTracker(ABC):
    """进度跟踪器接口"""
    
    @abstractmethod
    async def start_execution(self, execution_id: str) -> None:
        """开始执行"""
        pass
    
    @abstractmethod
    async def update_progress(
        self, 
        execution_id: str, 
        data_type: DataType, 
        result: ExecutionResult
    ) -> None:
        """更新进度"""
        pass
    
    @abstractmethod
    async def get_progress(self, execution_id: str) -> Dict[DataType, ExecutionResult]:
        """获取进度"""
        pass
    
    @abstractmethod
    async def mark_completed(self, execution_id: str) -> None:
        """标记完成"""
        pass
    
    @abstractmethod
    async def can_resume(self, execution_id: str) -> bool:
        """是否可以恢复执行"""
        pass


class IDependencyGraph(ABC):
    """依赖图管理器接口"""
    
    @abstractmethod
    def add_dependency(self, dependency: DataDependency) -> None:
        """添加依赖关系"""
        pass
    
    @abstractmethod
    def get_execution_order(self) -> List[List[DataType]]:
        """获取执行顺序（支持并行）"""
        pass
    
    @abstractmethod
    def validate_dependencies(self) -> bool:
        """验证依赖关系（检查循环依赖）"""
        pass
    
    @abstractmethod
    def get_dependents(self, data_type: DataType) -> Set[DataType]:
        """获取依赖于指定类型的数据类型"""
        pass


class IConfigurationManager(ABC):
    """配置管理器接口"""
    
    @abstractmethod
    def get_batch_config(self, data_type: DataType) -> BatchConfig:
        """获取批处理配置"""
        pass
    
    @abstractmethod
    def get_data_count(self, data_type: DataType) -> int:
        """获取数据数量配置"""
        pass
    
    @abstractmethod
    def is_enabled(self, data_type: DataType) -> bool:
        """检查数据类型是否启用"""
        pass
    
    @abstractmethod
    def get_connection_config(self) -> Dict[str, Any]:
        """获取数据库连接配置"""
        pass


class ISeedDataOrchestrator(ABC):
    """种子数据编排器接口"""
    
    @abstractmethod
    async def execute_full_seed(
        self, 
        execution_id: Optional[str] = None,
        resume: bool = False
    ) -> Dict[DataType, ExecutionResult]:
        """执行完整的种子数据创建"""
        pass
    
    @abstractmethod
    async def execute_partial_seed(
        self, 
        data_types: List[DataType],
        execution_id: Optional[str] = None
    ) -> Dict[DataType, ExecutionResult]:
        """执行部分种子数据创建"""
        pass
    
    @abstractmethod
    async def cleanup_all_data(self) -> None:
        """清理所有种子数据"""
        pass
    
    @abstractmethod
    async def validate_data_integrity(self) -> Dict[DataType, bool]:
        """验证数据完整性"""
        pass
