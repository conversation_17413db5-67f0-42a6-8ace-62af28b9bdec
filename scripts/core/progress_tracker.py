"""
进度跟踪器
提供执行进度跟踪、断点续传和详细报告功能
"""
import json
import time
import uuid
from datetime import datetime
from typing import Dict, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import text, select
from sqlalchemy.orm import declarative_base
from sqlalchemy import Column, String, DateTime, Integer, Text, Float

from scripts.core.interfaces import (
    IProgressTracker, DataType, ExecutionResult, ExecutionStatus
)

Base = declarative_base()


class SeedExecutionLog(Base):
    """种子数据执行日志表"""
    __tablename__ = "seed_execution_logs"
    
    execution_id = Column(String(50), primary_key=True)
    data_type = Column(String(50), primary_key=True)
    status = Column(String(20), nullable=False)
    created_count = Column(Integer, default=0)
    error_message = Column(Text, nullable=True)
    execution_time = Column(Float, default=0.0)
    memory_usage = Column(Integer, nullable=True)
    started_at = Column(DateTime, nullable=True)
    completed_at = Column(DateTime, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)


class SeedExecutionSummary(Base):
    """种子数据执行摘要表"""
    __tablename__ = "seed_execution_summary"
    
    execution_id = Column(String(50), primary_key=True)
    total_types = Column(Integer, default=0)
    completed_types = Column(Integer, default=0)
    failed_types = Column(Integer, default=0)
    total_records = Column(Integer, default=0)
    total_execution_time = Column(Float, default=0.0)
    status = Column(String(20), default="running")
    started_at = Column(DateTime, default=datetime.utcnow)
    completed_at = Column(DateTime, nullable=True)
    error_message = Column(Text, nullable=True)
    metadata = Column(Text, nullable=True)  # JSON格式的元数据


class ProgressTracker(IProgressTracker):
    """进度跟踪器实现"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
        self._ensure_tables_exist()
    
    def _ensure_tables_exist(self) -> None:
        """确保跟踪表存在"""
        # 注意：在实际使用中，这些表应该通过Alembic迁移创建
        # 这里只是为了演示目的
        pass
    
    async def start_execution(self, execution_id: str) -> None:
        """开始执行"""
        try:
            # 创建执行摘要记录
            summary_sql = text("""
                INSERT INTO seed_execution_summary 
                (execution_id, status, started_at, metadata)
                VALUES (:execution_id, :status, :started_at, :metadata)
                ON CONFLICT (execution_id) DO UPDATE SET
                    status = :status,
                    started_at = :started_at,
                    metadata = :metadata
            """)
            
            await self.db.execute(summary_sql, {
                "execution_id": execution_id,
                "status": "running",
                "started_at": datetime.utcnow(),
                "metadata": json.dumps({
                    "version": "2.0",
                    "started_by": "seed_data_script"
                })
            })
            
            await self.db.commit()
        except Exception as e:
            await self.db.rollback()
            raise RuntimeError(f"启动执行跟踪失败: {e}")
    
    async def update_progress(
        self, 
        execution_id: str, 
        data_type: DataType, 
        result: ExecutionResult
    ) -> None:
        """更新进度"""
        try:
            # 更新或插入数据类型执行记录
            log_sql = text("""
                INSERT INTO seed_execution_logs 
                (execution_id, data_type, status, created_count, error_message, 
                 execution_time, memory_usage, started_at, completed_at)
                VALUES (:execution_id, :data_type, :status, :created_count, 
                        :error_message, :execution_time, :memory_usage, 
                        :started_at, :completed_at)
                ON CONFLICT (execution_id, data_type) DO UPDATE SET
                    status = :status,
                    created_count = :created_count,
                    error_message = :error_message,
                    execution_time = :execution_time,
                    memory_usage = :memory_usage,
                    completed_at = :completed_at,
                    updated_at = :updated_at
            """)
            
            now = datetime.utcnow()
            await self.db.execute(log_sql, {
                "execution_id": execution_id,
                "data_type": data_type.value,
                "status": result.status.value,
                "created_count": result.created_count,
                "error_message": result.error_message,
                "execution_time": result.execution_time,
                "memory_usage": result.memory_usage,
                "started_at": now if result.status == ExecutionStatus.RUNNING else None,
                "completed_at": now if result.status in [ExecutionStatus.COMPLETED, ExecutionStatus.FAILED] else None,
                "updated_at": now
            })
            
            # 更新执行摘要
            await self._update_execution_summary(execution_id)
            
            await self.db.commit()
        except Exception as e:
            await self.db.rollback()
            raise RuntimeError(f"更新进度失败: {e}")
    
    async def _update_execution_summary(self, execution_id: str) -> None:
        """更新执行摘要"""
        # 统计各种状态的数量
        stats_sql = text("""
            SELECT 
                COUNT(*) as total_types,
                COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_types,
                COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_types,
                COALESCE(SUM(created_count), 0) as total_records,
                COALESCE(SUM(execution_time), 0) as total_execution_time
            FROM seed_execution_logs 
            WHERE execution_id = :execution_id
        """)
        
        result = await self.db.execute(stats_sql, {"execution_id": execution_id})
        stats = result.fetchone()
        
        if stats:
            # 确定整体状态
            if stats.failed_types > 0:
                overall_status = "failed"
            elif stats.completed_types == stats.total_types and stats.total_types > 0:
                overall_status = "completed"
            else:
                overall_status = "running"
            
            # 更新摘要
            update_sql = text("""
                UPDATE seed_execution_summary SET
                    total_types = :total_types,
                    completed_types = :completed_types,
                    failed_types = :failed_types,
                    total_records = :total_records,
                    total_execution_time = :total_execution_time,
                    status = :status,
                    completed_at = CASE WHEN :status IN ('completed', 'failed') 
                                       THEN :completed_at ELSE completed_at END
                WHERE execution_id = :execution_id
            """)
            
            await self.db.execute(update_sql, {
                "execution_id": execution_id,
                "total_types": stats.total_types,
                "completed_types": stats.completed_types,
                "failed_types": stats.failed_types,
                "total_records": stats.total_records,
                "total_execution_time": stats.total_execution_time,
                "status": overall_status,
                "completed_at": datetime.utcnow() if overall_status in ["completed", "failed"] else None
            })
    
    async def get_progress(self, execution_id: str) -> Dict[DataType, ExecutionResult]:
        """获取进度"""
        try:
            sql = text("""
                SELECT data_type, status, created_count, error_message, 
                       execution_time, memory_usage
                FROM seed_execution_logs 
                WHERE execution_id = :execution_id
            """)
            
            result = await self.db.execute(sql, {"execution_id": execution_id})
            rows = result.fetchall()
            
            progress = {}
            for row in rows:
                data_type = DataType(row.data_type)
                progress[data_type] = ExecutionResult(
                    data_type=data_type,
                    status=ExecutionStatus(row.status),
                    created_count=row.created_count or 0,
                    error_message=row.error_message,
                    execution_time=row.execution_time or 0.0,
                    memory_usage=row.memory_usage
                )
            
            return progress
        except Exception as e:
            raise RuntimeError(f"获取进度失败: {e}")
    
    async def mark_completed(self, execution_id: str) -> None:
        """标记完成"""
        try:
            sql = text("""
                UPDATE seed_execution_summary SET
                    status = 'completed',
                    completed_at = :completed_at
                WHERE execution_id = :execution_id
            """)
            
            await self.db.execute(sql, {
                "execution_id": execution_id,
                "completed_at": datetime.utcnow()
            })
            
            await self.db.commit()
        except Exception as e:
            await self.db.rollback()
            raise RuntimeError(f"标记完成失败: {e}")
    
    async def can_resume(self, execution_id: str) -> bool:
        """是否可以恢复执行"""
        try:
            sql = text("""
                SELECT status FROM seed_execution_summary 
                WHERE execution_id = :execution_id
            """)
            
            result = await self.db.execute(sql, {"execution_id": execution_id})
            row = result.fetchone()
            
            if not row:
                return False
            
            # 只有运行中或失败的执行可以恢复
            return row.status in ["running", "failed"]
        except Exception as e:
            raise RuntimeError(f"检查恢复状态失败: {e}")
    
    async def get_execution_report(self, execution_id: str) -> Dict[str, any]:
        """获取执行报告"""
        try:
            # 获取摘要信息
            summary_sql = text("""
                SELECT * FROM seed_execution_summary 
                WHERE execution_id = :execution_id
            """)
            
            summary_result = await self.db.execute(summary_sql, {"execution_id": execution_id})
            summary = summary_result.fetchone()
            
            # 获取详细日志
            logs_sql = text("""
                SELECT * FROM seed_execution_logs 
                WHERE execution_id = :execution_id
                ORDER BY started_at
            """)
            
            logs_result = await self.db.execute(logs_sql, {"execution_id": execution_id})
            logs = logs_result.fetchall()
            
            return {
                "execution_id": execution_id,
                "summary": dict(summary._mapping) if summary else None,
                "details": [dict(log._mapping) for log in logs],
                "generated_at": datetime.utcnow().isoformat()
            }
        except Exception as e:
            raise RuntimeError(f"生成执行报告失败: {e}")
    
    def generate_execution_id(self) -> str:
        """生成执行ID"""
        timestamp = int(time.time())
        unique_id = uuid.uuid4().hex[:8]
        return f"seed_{timestamp}_{unique_id}"
