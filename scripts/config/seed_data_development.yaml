# 种子数据配置文件 - 开发环境
# 配置各种数据类型的创建参数和执行策略

global:
  # 全局批处理配置
  batch_size: 1000
  max_memory_mb: 512
  parallel_workers: 4
  enable_streaming: true
  
  # 数据库连接配置
  connection_pool_size: 10
  connection_timeout: 30
  connection_max_overflow: 20
  connection_pool_pre_ping: true
  
  # 执行策略
  continue_on_error: false
  enable_progress_tracking: true
  enable_performance_monitoring: true
  
  # 日志配置
  log_level: INFO
  log_to_file: true
  log_file_path: "logs/seed_data.log"

# 各数据类型的具体配置
data_types:
  permissions:
    enabled: true
    count: 70
    batch_size: 100
    parallel_workers: 1
    max_memory_mb: 64
    enable_streaming: false
    priority: 1
    
  roles:
    enabled: true
    count: 3
    batch_size: 10
    parallel_workers: 1
    max_memory_mb: 32
    enable_streaming: false
    priority: 2
    
  users:
    enabled: true
    count: 3
    batch_size: 10
    parallel_workers: 1
    max_memory_mb: 32
    enable_streaming: false
    priority: 3
    
  wechat_users:
    enabled: true
    count: 2
    batch_size: 10
    parallel_workers: 1
    max_memory_mb: 32
    enable_streaming: false
    priority: 4
    
  subscription_plans:
    enabled: true
    count: 3
    batch_size: 10
    parallel_workers: 2
    max_memory_mb: 64
    enable_streaming: false
    priority: 4
    
  campaigns:
    enabled: true
    count: 3
    batch_size: 10
    parallel_workers: 2
    max_memory_mb: 64
    enable_streaming: false
    priority: 4
    
  reward_strategies:
    enabled: true
    count: 5
    batch_size: 10
    parallel_workers: 2
    max_memory_mb: 64
    enable_streaming: false
    priority: 5
    
  invitations:
    enabled: true
    count: 3
    batch_size: 10
    parallel_workers: 2
    max_memory_mb: 64
    enable_streaming: false
    priority: 5
    
  reward_records:
    enabled: true
    count: 3
    batch_size: 10
    parallel_workers: 2
    max_memory_mb: 64
    enable_streaming: false
    priority: 6
    
  categories:
    enabled: true
    count: 6
    batch_size: 20
    parallel_workers: 2
    max_memory_mb: 64
    enable_streaming: false
    priority: 1
    
  albums:
    enabled: true
    count: 50
    batch_size: 50
    parallel_workers: 3
    max_memory_mb: 128
    enable_streaming: true
    priority: 1
    
  album_images:
    enabled: true
    count: 150
    batch_size: 100
    parallel_workers: 4
    max_memory_mb: 256
    enable_streaming: true
    priority: 2
    
  products:
    enabled: true
    count: 30
    batch_size: 50
    parallel_workers: 3
    max_memory_mb: 128
    enable_streaming: false
    priority: 3
    
  product_specs:
    enabled: true
    count: 90
    batch_size: 50
    parallel_workers: 3
    max_memory_mb: 128
    enable_streaming: false
    priority: 4
    
  shops:
    enabled: true
    count: 10
    batch_size: 20
    parallel_workers: 2
    max_memory_mb: 64
    enable_streaming: false
    priority: 3
    
  inventories:
    enabled: true
    count: 120
    batch_size: 100
    parallel_workers: 3
    max_memory_mb: 128
    enable_streaming: true
    priority: 4

# 环境特定设置
environment:
  name: "development"
  description: "开发环境种子数据配置"
  
  # 开发环境特殊设置
  debug_mode: true
  verbose_logging: true
  enable_data_validation: true
  enable_integrity_checks: true
  
  # 性能调优
  optimize_for_speed: false
  optimize_for_memory: true
  
  # 测试相关
  enable_test_data: true
  test_data_prefix: "test_"
  
# 高级配置
advanced:
  # 重试策略
  retry_config:
    max_retries: 3
    retry_delay: 1.0
    exponential_backoff: true
    
  # 监控配置
  monitoring:
    enable_metrics: true
    metrics_interval: 10
    enable_memory_tracking: true
    enable_performance_profiling: false
    
  # 缓存配置
  cache:
    enable_query_cache: true
    cache_size_mb: 128
    cache_ttl_seconds: 3600
    
  # 并发控制
  concurrency:
    max_concurrent_factories: 4
    max_concurrent_batches: 8
    semaphore_timeout: 30
    
  # 数据验证
  validation:
    strict_mode: true
    validate_foreign_keys: true
    validate_data_types: true
    validate_constraints: true
    
  # 清理策略
  cleanup:
    auto_cleanup_on_error: true
    preserve_partial_data: false
    cleanup_timeout: 60
