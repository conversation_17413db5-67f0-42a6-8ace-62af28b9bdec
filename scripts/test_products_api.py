#!/usr/bin/env python3
"""
产品模块API接口测试脚本
运行所有产品相关的API测试
"""
import asyncio
import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

import pytest
import subprocess
from typing import List, Dict, Any


class ProductAPITester:
    """产品API测试器"""
    
    def __init__(self):
        self.test_results = {}
        self.total_tests = 0
        self.passed_tests = 0
        self.failed_tests = 0
    
    def run_all_tests(self) -> Dict[str, Any]:
        """运行所有产品API测试"""
        print("🚀 开始运行产品模块API接口测试...")
        print("=" * 60)
        
        # 设置测试环境变量
        os.environ["TESTING"] = "1"
        os.environ["DATABASE_URL"] = "sqlite+aiosqlite:///:memory:"
        
        # 运行测试
        test_files = [
            "svc/tests/test_products_api.py::TestProductAPI",
            "svc/tests/test_products_api.py::TestCategoryAPI", 
            "svc/tests/test_products_api.py::TestSpecAPI",
            "svc/tests/test_products_api.py::TestSKUAPI",
            "svc/tests/test_products_api.py::TestInventoryAPI",
            "svc/tests/test_products_api.py::TestIntegrationScenarios"
        ]
        
        results = {}
        
        for test_class in test_files:
            print(f"\n📋 运行测试: {test_class}")
            print("-" * 40)
            
            result = self._run_pytest(test_class)
            class_name = test_class.split("::")[-1]
            results[class_name] = result
            
            if result["success"]:
                print(f"✅ {class_name} 测试通过")
            else:
                print(f"❌ {class_name} 测试失败")
                print(f"错误信息: {result.get('error', 'Unknown error')}")
        
        # 汇总结果
        self._print_summary(results)
        return results
    
    def _run_pytest(self, test_path: str) -> Dict[str, Any]:
        """运行单个pytest测试"""
        try:
            cmd = [
                sys.executable, "-m", "pytest",
                test_path,
                "-v",
                "--tb=short",
                "--no-header",
                "--disable-warnings"
            ]
            
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                cwd=project_root
            )
            
            return {
                "success": result.returncode == 0,
                "stdout": result.stdout,
                "stderr": result.stderr,
                "returncode": result.returncode
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "stdout": "",
                "stderr": "",
                "returncode": -1
            }
    
    def _print_summary(self, results: Dict[str, Dict[str, Any]]):
        """打印测试结果汇总"""
        print("\n" + "=" * 60)
        print("📊 测试结果汇总")
        print("=" * 60)
        
        total_classes = len(results)
        passed_classes = sum(1 for r in results.values() if r["success"])
        failed_classes = total_classes - passed_classes
        
        print(f"总测试类数: {total_classes}")
        print(f"通过: {passed_classes}")
        print(f"失败: {failed_classes}")
        print(f"成功率: {(passed_classes/total_classes)*100:.1f}%")
        
        if failed_classes > 0:
            print("\n❌ 失败的测试类:")
            for class_name, result in results.items():
                if not result["success"]:
                    print(f"  - {class_name}")
                    if result.get("stderr"):
                        print(f"    错误: {result['stderr'][:200]}...")
        
        print("\n" + "=" * 60)
    
    def run_specific_test(self, test_name: str):
        """运行特定的测试"""
        print(f"🎯 运行特定测试: {test_name}")
        
        cmd = [
            sys.executable, "-m", "pytest",
            f"svc/tests/test_products_api.py::{test_name}",
            "-v", "-s",
            "--tb=long"
        ]
        
        subprocess.run(cmd, cwd=project_root)
    
    def run_integration_tests(self):
        """运行集成测试"""
        print("🔗 运行集成测试...")
        
        cmd = [
            sys.executable, "-m", "pytest",
            "svc/tests/test_products_api.py::TestIntegrationScenarios",
            "-v", "-s",
            "--tb=long"
        ]
        
        subprocess.run(cmd, cwd=project_root)


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="产品模块API测试")
    parser.add_argument(
        "--test", 
        help="运行特定测试类或方法",
        default=None
    )
    parser.add_argument(
        "--integration",
        action="store_true",
        help="只运行集成测试"
    )
    parser.add_argument(
        "--list",
        action="store_true", 
        help="列出所有可用的测试"
    )
    
    args = parser.parse_args()
    
    tester = ProductAPITester()
    
    if args.list:
        print("📋 可用的测试类:")
        test_classes = [
            "TestProductAPI - 商品API测试",
            "TestCategoryAPI - 分类API测试", 
            "TestSpecAPI - 规格API测试",
            "TestSKUAPI - SKU API测试",
            "TestInventoryAPI - 库存API测试",
            "TestIntegrationScenarios - 集成测试场景"
        ]
        for test_class in test_classes:
            print(f"  - {test_class}")
        return
    
    if args.integration:
        tester.run_integration_tests()
        return
    
    if args.test:
        tester.run_specific_test(args.test)
        return
    
    # 运行所有测试
    results = tester.run_all_tests()
    
    # 根据结果设置退出码
    failed_count = sum(1 for r in results.values() if not r["success"])
    sys.exit(failed_count)


if __name__ == "__main__":
    main()
