"""
数据工厂基类
提供通用的数据创建、验证和清理功能
"""
import logging
from abc import ABC
from typing import Any, Dict, List, Set, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, delete

from scripts.core.interfaces import IDataFactory, DataType

logger = logging.getLogger(__name__)


class BaseDataFactory(IDataFactory, ABC):
    """数据工厂基类"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    @property
    def dependencies(self) -> Set[DataType]:
        """默认无依赖"""
        return set()
    
    async def check_exists(self, db: AsyncSession) -> bool:
        """默认检查实现 - 子类应该重写"""
        return False
    
    async def validate_data(self, data: List[Dict[str, Any]]) -> bool:
        """默认数据验证 - 检查基本结构"""
        if not data:
            return True
        
        # 检查数据是否为字典列表
        if not all(isinstance(item, dict) for item in data):
            logger.error("数据必须是字典列表")
            return False
        
        # 检查必需字段（子类可以重写）
        required_fields = self.get_required_fields()
        for i, item in enumerate(data):
            for field in required_fields:
                if field not in item:
                    logger.error(f"数据项 {i} 缺少必需字段: {field}")
                    return False
        
        return True
    
    def get_required_fields(self) -> List[str]:
        """获取必需字段列表 - 子类可以重写"""
        return []
    
    async def cleanup_on_error(self, db: AsyncSession) -> None:
        """默认错误清理 - 子类可以重写"""
        logger.warning(f"数据类型 {self.data_type.value} 没有实现错误清理逻辑")
    
    async def pre_create_hook(self, data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """创建前钩子 - 子类可以重写进行数据预处理"""
        return data
    
    async def post_create_hook(self, created_objects: List[Any]) -> List[Any]:
        """创建后钩子 - 子类可以重写进行后处理"""
        return created_objects
    
    async def get_batch_size(self) -> int:
        """获取批次大小 - 子类可以重写"""
        return 1000
    
    async def estimate_memory_usage(self, data_count: int) -> int:
        """估算内存使用量（MB） - 子类可以重写"""
        # 默认估算：每条记录约1KB
        return max(1, data_count // 1024)


class StreamingDataFactory(BaseDataFactory):
    """流式数据工厂 - 支持大数据集的流式处理"""
    
    async def create_streaming(
        self, 
        data_generator,
        batch_size: int = 1000
    ) -> List[Any]:
        """流式创建数据"""
        created_objects = []
        current_batch = []
        
        async for data_item in data_generator:
            current_batch.append(data_item)
            
            if len(current_batch) >= batch_size:
                # 处理当前批次
                batch_objects = await self.create_batch(self.db, current_batch)
                created_objects.extend(batch_objects)
                current_batch = []
                
                # 刷新会话以释放内存
                await self.db.flush()
        
        # 处理剩余数据
        if current_batch:
            batch_objects = await self.create_batch(self.db, current_batch)
            created_objects.extend(batch_objects)
        
        return created_objects


class CachedDataFactory(BaseDataFactory):
    """缓存数据工厂 - 支持数据缓存以减少重复查询"""
    
    def __init__(self, db: AsyncSession):
        super().__init__(db)
        self._cache: Dict[str, Any] = {}
    
    def get_cache_key(self, **kwargs) -> str:
        """生成缓存键"""
        key_parts = [f"{k}={v}" for k, v in sorted(kwargs.items())]
        return f"{self.data_type.value}:{'&'.join(key_parts)}"
    
    def get_from_cache(self, cache_key: str) -> Optional[Any]:
        """从缓存获取数据"""
        return self._cache.get(cache_key)
    
    def set_cache(self, cache_key: str, value: Any) -> None:
        """设置缓存"""
        self._cache[cache_key] = value
    
    def clear_cache(self) -> None:
        """清空缓存"""
        self._cache.clear()


class ValidatedDataFactory(BaseDataFactory):
    """验证数据工厂 - 提供增强的数据验证功能"""
    
    def __init__(self, db: AsyncSession):
        super().__init__(db)
        self._validation_rules = self.get_validation_rules()
    
    def get_validation_rules(self) -> Dict[str, Any]:
        """获取验证规则 - 子类应该重写"""
        return {}
    
    async def validate_data(self, data: List[Dict[str, Any]]) -> bool:
        """增强的数据验证"""
        # 先执行基础验证
        if not await super().validate_data(data):
            return False
        
        # 执行自定义验证规则
        for i, item in enumerate(data):
            if not await self.validate_item(item, i):
                return False
        
        return True
    
    async def validate_item(self, item: Dict[str, Any], index: int) -> bool:
        """验证单个数据项"""
        for field, rules in self._validation_rules.items():
            if field in item:
                value = item[field]
                
                # 类型验证
                if 'type' in rules and not isinstance(value, rules['type']):
                    logger.error(f"数据项 {index} 字段 {field} 类型错误")
                    return False
                
                # 长度验证
                if 'max_length' in rules and hasattr(value, '__len__'):
                    if len(value) > rules['max_length']:
                        logger.error(f"数据项 {index} 字段 {field} 长度超限")
                        return False
                
                # 范围验证
                if 'min_value' in rules and value < rules['min_value']:
                    logger.error(f"数据项 {index} 字段 {field} 值过小")
                    return False
                
                if 'max_value' in rules and value > rules['max_value']:
                    logger.error(f"数据项 {index} 字段 {field} 值过大")
                    return False
                
                # 自定义验证函数
                if 'validator' in rules:
                    if not await rules['validator'](value):
                        logger.error(f"数据项 {index} 字段 {field} 自定义验证失败")
                        return False
        
        return True


class TransactionalDataFactory(BaseDataFactory):
    """事务数据工厂 - 支持事务管理"""
    
    async def create_with_transaction(
        self, 
        data: List[Dict[str, Any]],
        savepoint_name: Optional[str] = None
    ) -> List[Any]:
        """在事务中创建数据"""
        from scripts.core.transaction_manager import TransactionManager
        
        tx_manager = TransactionManager(self.db)
        savepoint_id = None
        
        try:
            if savepoint_name:
                savepoint_id = await tx_manager.create_savepoint(savepoint_name)
            
            # 预处理数据
            processed_data = await self.pre_create_hook(data)
            
            # 验证数据
            if not await self.validate_data(processed_data):
                raise ValueError("数据验证失败")
            
            # 创建数据
            created_objects = await self.create_batch(self.db, processed_data)
            
            # 后处理
            final_objects = await self.post_create_hook(created_objects)
            
            if savepoint_id:
                await tx_manager.release_savepoint(savepoint_id)
            
            return final_objects
            
        except Exception as e:
            if savepoint_id:
                await tx_manager.rollback_to_savepoint(savepoint_id)
            else:
                await tx_manager.rollback_transaction()
            
            logger.error(f"事务创建数据失败: {e}")
            raise


class ParallelDataFactory(BaseDataFactory):
    """并行数据工厂 - 支持并行处理"""
    
    async def create_parallel(
        self, 
        data: List[Dict[str, Any]],
        worker_count: int = 4
    ) -> List[Any]:
        """并行创建数据"""
        import asyncio
        
        if len(data) < worker_count:
            # 数据量小，直接串行处理
            return await self.create_batch(self.db, data)
        
        # 分割数据
        chunk_size = len(data) // worker_count
        chunks = [
            data[i:i + chunk_size] 
            for i in range(0, len(data), chunk_size)
        ]
        
        # 并行处理
        tasks = [
            self.create_batch(self.db, chunk)
            for chunk in chunks
        ]
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 合并结果
        created_objects = []
        for result in results:
            if isinstance(result, Exception):
                raise result
            created_objects.extend(result)
        
        return created_objects
