"""
权限数据工厂
专门负责权限数据的创建、验证和管理
"""
from typing import Any, Dict, List, Set
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, text

from scripts.core.interfaces import DataType
from scripts.factories.base_factory import ValidatedDataFactory
from svc.apps.auth.models import Permission


class PermissionFactory(ValidatedDataFactory):
    """权限工厂"""
    
    @property
    def data_type(self) -> DataType:
        return DataType.PERMISSIONS
    
    @property
    def dependencies(self) -> Set[DataType]:
        return set()  # 权限无依赖
    
    def get_required_fields(self) -> List[str]:
        return ["name", "description"]
    
    def get_validation_rules(self) -> Dict[str, Any]:
        return {
            "name": {
                "type": str,
                "max_length": 100,
                "validator": self._validate_permission_name
            },
            "description": {
                "type": str,
                "max_length": 255
            }
        }
    
    async def _validate_permission_name(self, name: str) -> bool:
        """验证权限名称格式"""
        if not name:
            return False
        
        # 权限名称应该符合 module:action 格式
        if ":" not in name and name != "*:*":
            self.logger.warning(f"权限名称 {name} 不符合标准格式")
        
        return True
    
    async def check_exists(self, db: AsyncSession) -> bool:
        """检查权限是否已存在"""
        try:
            result = await db.execute(select(Permission).limit(1))
            return result.scalars().first() is not None
        except Exception as e:
            self.logger.error(f"检查权限存在性失败: {e}")
            return False
    
    async def generate_data(self, context: Dict[str, Any]) -> List[Dict[str, Any]]:
        """生成权限数据"""
        permissions_data = [
            # 全局权限
            {"name": "*:*", "description": "所有权限（超级管理员）"},
            
            # 认证授权模块权限
            {"name": "user:read", "description": "查看用户信息"},
            {"name": "user:create", "description": "创建用户"},
            {"name": "user:update", "description": "更新用户信息"},
            {"name": "user:delete", "description": "删除用户"},
            {"name": "user:manage", "description": "管理用户（禁用/启用账户等）"},
            {"name": "role:read", "description": "查看角色信息"},
            {"name": "role:create", "description": "创建角色"},
            {"name": "role:update", "description": "更新角色"},
            {"name": "role:delete", "description": "删除角色"},
            {"name": "role:assign", "description": "分配角色给用户"},
            {"name": "permission:read", "description": "查看权限信息"},
            {"name": "permission:assign", "description": "给角色分配权限"},
            
            # 计费/订阅模块权限
            {"name": "subscription_plan:read", "description": "查看订阅计划"},
            {"name": "subscription_plan:create", "description": "创建订阅计划"},
            {"name": "subscription_plan:update", "description": "更新订阅计划"},
            {"name": "subscription_plan:delete", "description": "删除订阅计划"},
            {"name": "subscription:read", "description": "查看订阅信息"},
            {"name": "subscription:create", "description": "创建订阅"},
            {"name": "subscription:update", "description": "更新订阅"},
            {"name": "subscription:cancel", "description": "取消订阅"},
            {"name": "subscription:renew", "description": "续订订阅"},
            {"name": "invoice:read", "description": "查看发票"},
            {"name": "invoice:create", "description": "创建发票"},
            {"name": "invoice:update", "description": "更新发票"},
            {"name": "invoice:delete", "description": "删除发票"},
            {"name": "payment:read", "description": "查看支付信息"},
            {"name": "payment:process", "description": "处理支付"},
            {"name": "payment:refund", "description": "退款操作"},
            
            # 营销模块权限
            {"name": "campaign:read", "description": "查看营销活动"},
            {"name": "campaign:create", "description": "创建营销活动"},
            {"name": "campaign:update", "description": "更新营销活动"},
            {"name": "campaign:delete", "description": "删除营销活动"},
            {"name": "campaign:activate", "description": "激活/停用营销活动"},
            {"name": "invitation:read", "description": "查看邀请信息"},
            {"name": "invitation:create", "description": "创建邀请"},
            {"name": "invitation:delete", "description": "删除邀请"},
            {"name": "reward_strategy:read", "description": "查看奖励策略"},
            {"name": "reward_strategy:create", "description": "创建奖励策略"},
            {"name": "reward_strategy:update", "description": "更新奖励策略"},
            {"name": "reward_strategy:delete", "description": "删除奖励策略"},
            {"name": "reward_record:read", "description": "查看奖励记录"},
            {"name": "reward_record:create", "description": "创建奖励记录"},
            {"name": "reward_record:issue", "description": "发放奖励"},
            
            # 产品模块权限
            {"name": "category:read", "description": "查看产品分类"},
            {"name": "category:create", "description": "创建产品分类"},
            {"name": "category:update", "description": "更新产品分类"},
            {"name": "category:delete", "description": "删除产品分类"},
            {"name": "product:read", "description": "查看产品信息"},
            {"name": "product:create", "description": "创建产品"},
            {"name": "product:update", "description": "更新产品"},
            {"name": "product:delete", "description": "删除产品"},
            {"name": "product:manage", "description": "管理产品（上架/下架等）"},
            {"name": "inventory:read", "description": "查看库存信息"},
            {"name": "inventory:update", "description": "更新库存"},
            {"name": "inventory:manage", "description": "库存管理"},
            
            # 门店模块权限
            {"name": "shop:read", "description": "查看门店信息"},
            {"name": "shop:create", "description": "创建门店"},
            {"name": "shop:update", "description": "更新门店信息"},
            {"name": "shop:delete", "description": "删除门店"},
            {"name": "shop:manage", "description": "管理门店（开业/关闭等）"},
            
            # 图册模块权限
            {"name": "album:read", "description": "查看图册"},
            {"name": "album:create", "description": "创建图册"},
            {"name": "album:update", "description": "更新图册"},
            {"name": "album:delete", "description": "删除图册"},
            {"name": "album_image:read", "description": "查看图片"},
            {"name": "album_image:create", "description": "上传图片"},
            {"name": "album_image:update", "description": "更新图片"},
            {"name": "album_image:delete", "description": "删除图片"},
            
            # 系统模块权限
            {"name": "config:read", "description": "查看系统配置"},
            {"name": "config:update", "description": "更新系统配置"},
            {"name": "audit_log:read", "description": "查看审计日志"},
            {"name": "audit_log:delete", "description": "删除审计日志"},
            {"name": "system:backup", "description": "系统备份操作"},
            {"name": "system:restore", "description": "系统恢复操作"},
            {"name": "system:maintenance", "description": "系统维护模式切换"},
            {"name": "system:monitor", "description": "系统监控访问权限"}
        ]
        
        return permissions_data
    
    async def create_batch(
        self, 
        db: AsyncSession, 
        data_batch: List[Dict[str, Any]]
    ) -> List[Permission]:
        """批量创建权限"""
        try:
            # 使用原生SQL进行批量插入以提高性能
            if not data_batch:
                return []
            
            # 准备批量插入的数据
            values = []
            for item in data_batch:
                values.append(f"('{item['name']}', '{item['description']}')")
            
            values_str = ", ".join(values)
            sql = text(f"""
                INSERT INTO permissions (name, description) 
                VALUES {values_str}
                ON CONFLICT (name) DO NOTHING
            """)
            
            await db.execute(sql)
            await db.flush()
            
            # 返回创建的权限对象
            result = await db.execute(
                select(Permission).where(
                    Permission.name.in_([item['name'] for item in data_batch])
                )
            )
            
            created_permissions = result.scalars().all()
            self.logger.info(f"成功批量创建 {len(created_permissions)} 个权限")
            
            return created_permissions
            
        except Exception as e:
            self.logger.error(f"批量创建权限失败: {e}")
            raise
    
    async def cleanup_on_error(self, db: AsyncSession) -> None:
        """错误时清理权限数据"""
        try:
            # 删除所有权限（谨慎操作）
            await db.execute(text("DELETE FROM permissions"))
            await db.flush()
            self.logger.info("已清理所有权限数据")
        except Exception as e:
            self.logger.error(f"清理权限数据失败: {e}")
            raise
    
    async def get_existing_permissions(self, db: AsyncSession) -> List[Permission]:
        """获取现有权限"""
        try:
            result = await db.execute(select(Permission).order_by(Permission.id))
            return result.scalars().all()
        except Exception as e:
            self.logger.error(f"获取现有权限失败: {e}")
            return []
    
    async def estimate_memory_usage(self, data_count: int) -> int:
        """估算内存使用量"""
        # 权限数据较小，每条记录约0.5KB
        return max(1, data_count // 2048)
