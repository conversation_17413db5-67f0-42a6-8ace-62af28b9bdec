# 种子数据脚本更新总结

## 📋 更新概述

本次更新将种子数据脚本 (`scripts/seed_data.py`) 中的产品模块相关逻辑更新为最新的架构，主要涉及规格、SKU和库存管理的变更。

## 🔄 主要变更

### 1. 架构变更
- **旧架构**: 使用 `ProductSpecCombination` 模型来管理产品规格组合
- **新架构**: 使用 `ProductSKU` 模型来管理产品的可售单元，与规格选项通过多对多关系关联

### 2. 导入更新
```python
# 新增导入
from svc.apps.products.repositories.spec import SpecRepository, SpecOptionRepository
from svc.apps.products.repositories.sku import ProductSKURepository
from svc.apps.products.schemas.spec import SpecCreate, SpecOptionCreate
from svc.apps.products.schemas.sku import ProductSKUCreate
```

### 3. 函数重构

#### 3.1 `create_product_specs` → `create_product_specs_and_skus`
- **功能**: 为产品创建规格、规格选项和SKU
- **变更**:
  - 移除了 `ProductSpecCombinationRepository` 的使用
  - 使用 `ProductSKURepository` 创建SKU
  - 价格字段转换为分（乘以100）
  - 添加了SKU状态和排序字段

#### 3.2 `create_product_specs_combinations` → `create_product_skus_by_category`
- **功能**: 为每个产品按分类生成SKU
- **变更**:
  - 重命名函数以反映新的功能
  - 使用SKU模型替代规格组合模型
  - 添加SKU存在性检查

#### 3.3 `create_inventories` 简化
- **变更**:
  - 移除了对 `variants` 参数的依赖
  - 简化为只为产品创建库存
  - 移除了变体库存相关逻辑

### 4. 权限数据更新
添加了SKU相关的权限：
```python
{"name": "sku:read", "description": "查看SKU信息"},
{"name": "sku:create", "description": "创建SKU"},
{"name": "sku:update", "description": "更新SKU"},
{"name": "sku:delete", "description": "删除SKU"},
```

## 📊 数据结构变更

### 规格架构
```
Spec (规格)
├── SpecOption (规格选项)
└── ProductSKU (通过多对多关系关联)
```

### SKU架构
```
Product (产品)
└── ProductSKU (SKU)
    ├── spec_options (多对多关系)
    └── price (以分为单位)
```

## 🧪 测试验证

创建了测试脚本 `scripts/test_seed_data.py` 来验证更新后的功能：

### 测试内容
1. **规格和SKU创建测试**
   - 验证规格创建
   - 验证规格选项创建
   - 验证SKU创建和关联

2. **库存创建测试**
   - 验证产品库存创建
   - 验证库存数据完整性

### 运行测试
```bash
python scripts/test_seed_data.py
```

## 🚀 使用方法

### 完整重置并创建种子数据
```bash
# 重新创建数据库并运行种子数据
python scripts/seed_data.py --recreate

# 或者只重置迁移
python scripts/seed_data.py --reset
```

### 仅运行种子数据创建
```bash
python scripts/seed_data.py
```

## ⚠️ 注意事项

1. **数据库兼容性**: 确保数据库迁移已经应用到最新版本
2. **依赖关系**: 新的SKU架构依赖于规格和规格选项的正确创建
3. **价格单位**: SKU价格以分为单位存储，需要在显示时转换
4. **性能考虑**: 大量SKU创建可能需要较长时间，建议分批处理

## 🔧 故障排除

### 常见问题
1. **导入错误**: 确保所有新的仓库和模式类都已正确导入
2. **数据库字段错误**: 确保数据库迁移已应用
3. **外键约束**: 确保规格和规格选项在SKU创建前已存在

### 调试建议
1. 使用测试脚本验证各个组件
2. 检查日志输出了解创建进度
3. 验证数据库中的数据完整性

## 📈 性能优化

1. **批量操作**: 使用事务批量提交数据
2. **索引优化**: 确保SKU编码和规格选项有适当的索引
3. **内存管理**: 大量数据创建时注意内存使用

## 🎯 下一步计划

1. **扩展测试**: 添加更多边界情况测试
2. **性能测试**: 测试大量数据创建的性能
3. **文档完善**: 更新相关API文档
4. **监控集成**: 添加种子数据创建的监控指标
