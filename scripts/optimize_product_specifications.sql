-- 产品规格系统数据库优化脚本
-- 添加必要的索引和约束，提升查询性能

-- ============================================
-- 1. 规格表(specs)优化
-- ============================================

-- 添加复合索引：活跃状态 + 排序
CREATE INDEX IF NOT EXISTS idx_specs_active_sort 
ON specs(is_active, sort_order) 
WHERE is_active = true;

-- 添加名称索引（用于搜索）
CREATE INDEX IF NOT EXISTS idx_specs_name_gin 
ON specs USING gin(to_tsvector('simple', name));

-- 添加排序索引
CREATE INDEX IF NOT EXISTS idx_specs_sort_order 
ON specs(sort_order);

-- ============================================
-- 2. 规格值表(spec_options)优化
-- ============================================

-- 添加复合索引：规格ID + 活跃状态 + 排序
CREATE INDEX IF NOT EXISTS idx_spec_options_spec_active_sort 
ON spec_options(spec_id, is_active, sort_order) 
WHERE is_active = true;

-- 添加值搜索索引
CREATE INDEX IF NOT EXISTS idx_spec_options_value_gin 
ON spec_options USING gin(to_tsvector('simple', value));

-- 添加颜色代码索引（用于颜色筛选）
CREATE INDEX IF NOT EXISTS idx_spec_options_color_code 
ON spec_options(color_code) 
WHERE color_code IS NOT NULL;

-- ============================================
-- 3. 产品规格组合表(product_spec_combinations)优化
-- ============================================

-- 添加复合索引：产品ID + 活跃状态
CREATE INDEX IF NOT EXISTS idx_combinations_product_active 
ON product_spec_combinations(product_id, is_active);

-- 添加SKU唯一索引（如果还没有）
CREATE UNIQUE INDEX IF NOT EXISTS idx_combinations_sku_unique 
ON product_spec_combinations(sku);

-- 添加价格范围查询索引
CREATE INDEX IF NOT EXISTS idx_combinations_price_range 
ON product_spec_combinations(product_id, price) 
WHERE price IS NOT NULL AND is_active = true;

-- 添加库存查询索引
CREATE INDEX IF NOT EXISTS idx_combinations_stock 
ON product_spec_combinations(product_id, stock_quantity) 
WHERE is_active = true;

-- 添加条码索引
CREATE INDEX IF NOT EXISTS idx_combinations_barcode 
ON product_spec_combinations(barcode) 
WHERE barcode IS NOT NULL;

-- 添加默认组合索引
CREATE INDEX IF NOT EXISTS idx_combinations_default 
ON product_spec_combinations(product_id, is_default) 
WHERE is_default = true;

-- 添加重量索引（用于物流计算）
CREATE INDEX IF NOT EXISTS idx_combinations_weight 
ON product_spec_combinations(weight) 
WHERE weight IS NOT NULL;

-- ============================================
-- 4. 关联表(combination_spec_options)优化
-- ============================================

-- 添加组合ID索引
CREATE INDEX IF NOT EXISTS idx_combination_spec_options_combination 
ON combination_spec_options(combination_id);

-- 添加规格值ID索引
CREATE INDEX IF NOT EXISTS idx_combination_spec_options_spec_option 
ON combination_spec_options(spec_option_id);

-- 添加复合索引（用于反向查询）
CREATE INDEX IF NOT EXISTS idx_combination_spec_options_reverse 
ON combination_spec_options(spec_option_id, combination_id);

-- ============================================
-- 5. 外键约束检查和优化
-- ============================================

-- 确保外键约束存在
ALTER TABLE spec_options 
ADD CONSTRAINT IF NOT EXISTS fk_spec_options_spec_id 
FOREIGN KEY (spec_id) REFERENCES specs(id) ON DELETE CASCADE;

ALTER TABLE product_spec_combinations 
ADD CONSTRAINT IF NOT EXISTS fk_combinations_product_id 
FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE;

ALTER TABLE combination_spec_options 
ADD CONSTRAINT IF NOT EXISTS fk_combination_spec_options_combination_id 
FOREIGN KEY (combination_id) REFERENCES product_spec_combinations(id) ON DELETE CASCADE;

ALTER TABLE combination_spec_options 
ADD CONSTRAINT IF NOT EXISTS fk_combination_spec_options_spec_option_id 
FOREIGN KEY (spec_option_id) REFERENCES spec_options(id) ON DELETE CASCADE;

-- ============================================
-- 6. 数据完整性约束
-- ============================================

-- 确保价格为正数
ALTER TABLE product_spec_combinations 
ADD CONSTRAINT IF NOT EXISTS chk_combinations_price_positive 
CHECK (price IS NULL OR price >= 0);

-- 确保成本价为正数
ALTER TABLE product_spec_combinations 
ADD CONSTRAINT IF NOT EXISTS chk_combinations_cost_price_positive 
CHECK (cost_price IS NULL OR cost_price >= 0);

-- 确保库存数量为非负数
ALTER TABLE product_spec_combinations 
ADD CONSTRAINT IF NOT EXISTS chk_combinations_stock_non_negative 
CHECK (stock_quantity >= 0);

-- 确保最小库存水平为非负数
ALTER TABLE product_spec_combinations 
ADD CONSTRAINT IF NOT EXISTS chk_combinations_min_stock_non_negative 
CHECK (min_stock_level >= 0);

-- 确保重量为正数
ALTER TABLE product_spec_combinations 
ADD CONSTRAINT IF NOT EXISTS chk_combinations_weight_positive 
CHECK (weight IS NULL OR weight > 0);

-- 确保排序值为非负数
ALTER TABLE specs 
ADD CONSTRAINT IF NOT EXISTS chk_specs_sort_order_non_negative 
CHECK (sort_order >= 0);

ALTER TABLE spec_options 
ADD CONSTRAINT IF NOT EXISTS chk_spec_options_sort_order_non_negative 
CHECK (sort_order >= 0);

-- ============================================
-- 7. 分区表优化（可选，适用于大数据量）
-- ============================================

-- 如果产品规格组合表数据量很大，可以考虑按产品ID分区
-- 注意：这需要重建表，请在维护窗口执行

/*
-- 创建分区表示例（仅供参考）
CREATE TABLE product_spec_combinations_partitioned (
    LIKE product_spec_combinations INCLUDING ALL
) PARTITION BY HASH (product_id);

-- 创建分区
CREATE TABLE product_spec_combinations_part_0 
PARTITION OF product_spec_combinations_partitioned 
FOR VALUES WITH (modulus 4, remainder 0);

CREATE TABLE product_spec_combinations_part_1 
PARTITION OF product_spec_combinations_partitioned 
FOR VALUES WITH (modulus 4, remainder 1);

CREATE TABLE product_spec_combinations_part_2 
PARTITION OF product_spec_combinations_partitioned 
FOR VALUES WITH (modulus 4, remainder 2);

CREATE TABLE product_spec_combinations_part_3 
PARTITION OF product_spec_combinations_partitioned 
FOR VALUES WITH (modulus 4, remainder 3);
*/

-- ============================================
-- 8. 统计信息更新
-- ============================================

-- 更新表统计信息，提升查询计划质量
ANALYZE specs;
ANALYZE spec_options;
ANALYZE product_spec_combinations;
ANALYZE combination_spec_options;

-- ============================================
-- 9. 性能监控视图
-- ============================================

-- 创建性能监控视图
CREATE OR REPLACE VIEW v_product_spec_performance AS
SELECT 
    p.id as product_id,
    p.name as product_name,
    COUNT(psc.id) as total_combinations,
    COUNT(CASE WHEN psc.is_active THEN 1 END) as active_combinations,
    COUNT(CASE WHEN psc.price IS NOT NULL THEN 1 END) as priced_combinations,
    SUM(psc.stock_quantity) as total_stock,
    AVG(psc.price) as avg_price,
    MIN(psc.price) as min_price,
    MAX(psc.price) as max_price
FROM products p
LEFT JOIN product_spec_combinations psc ON p.id = psc.product_id
GROUP BY p.id, p.name;

-- 创建规格使用统计视图
CREATE OR REPLACE VIEW v_spec_usage_stats AS
SELECT 
    s.id as spec_id,
    s.name as spec_name,
    COUNT(DISTINCT psc.product_id) as product_count,
    COUNT(psc.id) as combination_count,
    COUNT(CASE WHEN psc.is_active THEN 1 END) as active_combination_count
FROM specs s
JOIN spec_options so ON s.id = so.spec_id
JOIN combination_spec_options cso ON so.id = cso.spec_option_id
JOIN product_spec_combinations psc ON cso.combination_id = psc.id
GROUP BY s.id, s.name
ORDER BY combination_count DESC;

-- ============================================
-- 10. 清理和维护建议
-- ============================================

-- 定期清理无效数据的建议SQL（请根据业务需求调整）

-- 清理没有组合的规格值
/*
DELETE FROM spec_options 
WHERE id NOT IN (
    SELECT DISTINCT spec_option_id 
    FROM combination_spec_options
);
*/

-- 清理没有规格值的规格
/*
DELETE FROM specs 
WHERE id NOT IN (
    SELECT DISTINCT spec_id 
    FROM spec_options
);
*/

-- 清理孤立的关联记录
/*
DELETE FROM combination_spec_options 
WHERE combination_id NOT IN (
    SELECT id FROM product_spec_combinations
) OR spec_option_id NOT IN (
    SELECT id FROM spec_options
);
*/

-- ============================================
-- 执行完成提示
-- ============================================

SELECT 'Product Specifications Database Optimization Completed!' as status;
