# 数据库异常处理指南

## 概述

本指南介绍如何使用项目中完善的数据库异常处理系统，包括异常类型、处理装饰器和最佳实践。

## 异常类型层次结构

### 基础异常类

```python
from svc.core.database import DatabaseError

# 所有数据库异常的基类
class DatabaseError(Exception):
    def __init__(self, message, error_code=None, original_error=None, context=None):
        # 提供丰富的错误信息
        pass
```

### 具体异常类型

#### 1. 连接相关异常

```python
from svc.core.database import ConnectionError, SessionError, TransactionTimeoutError

# 数据库连接失败
raise ConnectionError("数据库连接失败")

# 会话错误
raise SessionError("数据库会话错误")

# 事务超时
raise TransactionTimeoutError("事务执行超时", timeout_seconds=30)
```

#### 2. 数据完整性异常

```python
from svc.core.database import (
    UniqueConstraintError, 
    ForeignKeyConstraintError,
    IntegrityConstraintError
)

# 唯一约束违反
raise UniqueConstraintError(
    message="用户名已存在",
    field_name="username",
    field_value="john_doe"
)

# 外键约束违反
raise ForeignKeyConstraintError(
    message="关联的用户不存在",
    foreign_key="user_id"
)
```

#### 3. 查询相关异常

```python
from svc.core.database import (
    RecordNotFoundError,
    MultipleRecordsFoundError,
    QueryExecutionError
)

# 记录未找到
raise RecordNotFoundError(
    message="用户不存在",
    model_name="User",
    record_id=123
)

# 找到多条记录（期望唯一）
raise MultipleRecordsFoundError(
    message="找到多个用户",
    model_name="User"
)
```

## 异常处理装饰器

### 1. 通用数据库异常处理

```python
from svc.core.database import handle_database_errors

class UserRepository:
    @handle_database_errors()
    async def create_user(self, user_data: dict):
        """创建用户 - 自动处理所有数据库异常"""
        user = User(**user_data)
        self.db.add(user)
        await self.db.flush()
        return user
    
    @handle_database_errors(reraise_as=RecordNotFoundError, log_errors=False)
    async def get_user_by_id(self, user_id: int):
        """获取用户 - 将异常重新抛出为 RecordNotFoundError"""
        result = await self.db.execute(
            select(User).where(User.id == user_id)
        )
        user = result.scalars().first()
        if not user:
            raise NoResultFound()
        return user
```

### 2. 仓储层专用装饰器

```python
from svc.core.database import handle_repository_errors

class UserRepository(BaseRepository):
    @handle_repository_errors(model_name="User")
    async def create(self, user_data: dict):
        """创建用户 - 自动添加模型上下文信息"""
        return await super().create(user_data)
    
    @handle_repository_errors(model_name="User")
    async def update(self, user_id: int, update_data: dict):
        """更新用户 - 包含操作和模型信息"""
        user = await self.get_by_id(user_id)
        if not user:
            raise RecordNotFoundError(model_name="User", record_id=user_id)
        
        for key, value in update_data.items():
            setattr(user, key, value)
        
        await self.db.flush()
        return user
```

## SQLAlchemy 异常自动映射

系统会自动将 SQLAlchemy 异常映射为自定义异常：

```python
from svc.core.database import map_sqlalchemy_exception
from sqlalchemy.exc import IntegrityError

try:
    # 数据库操作
    await self.db.execute(stmt)
except IntegrityError as e:
    # 自动映射为 UniqueConstraintError 或 ForeignKeyConstraintError
    custom_error = map_sqlalchemy_exception(e)
    raise custom_error
```

## 工具函数

### 1. 异常类型检查

```python
from svc.core.database import (
    is_integrity_error,
    is_connection_error,
    is_timeout_error,
    is_not_found_error,
    is_retryable_error,
    is_user_input_error
)

try:
    # 数据库操作
    pass
except Exception as e:
    if is_retryable_error(e):
        # 可以重试的错误
        await asyncio.sleep(1)
        # 重试逻辑
    elif is_user_input_error(e):
        # 用户输入错误，不记录为系统错误
        logger.info(f"用户输入错误: {e}")
    else:
        # 系统错误，需要记录
        logger.error(f"系统错误: {e}", exc_info=True)
```

### 2. 用户友好的错误消息

```python
from svc.core.database import create_user_friendly_message

try:
    # 数据库操作
    pass
except Exception as e:
    user_message = create_user_friendly_message(e)
    return {"error": user_message}
```

### 3. HTTP 状态码映射

```python
from svc.core.database import get_http_status_code
from fastapi import HTTPException

try:
    # 数据库操作
    pass
except Exception as e:
    status_code = get_http_status_code(e)
    user_message = create_user_friendly_message(e)
    raise HTTPException(status_code=status_code, detail=user_message)
```

## 在服务层中的使用

```python
from svc.core.database import (
    handle_database_errors,
    RecordNotFoundError,
    UniqueConstraintError,
    create_user_friendly_message
)
from svc.core.services import BaseService

class UserService(BaseService):
    @handle_database_errors()
    async def create_user(self, user_data: dict):
        """创建用户"""
        try:
            user = await self.repository.create(user_data)
            return self.create_success_result(user)
        except UniqueConstraintError as e:
            return self.create_error_result(
                error_code="USER_ALREADY_EXISTS",
                error_message=create_user_friendly_message(e)
            )
    
    async def get_user_by_id(self, user_id: int):
        """获取用户"""
        try:
            user = await self.repository.get_by_id(user_id)
            if not user:
                raise RecordNotFoundError(model_name="User", record_id=user_id)
            return self.create_success_result(user)
        except RecordNotFoundError as e:
            return self.create_error_result(
                error_code="USER_NOT_FOUND",
                error_message=create_user_friendly_message(e)
            )
```

## 在路由层中的使用

```python
from fastapi import HTTPException
from svc.core.database import get_http_status_code, create_user_friendly_message

@router.post("/users")
async def create_user(user_data: UserCreate, service: UserService = Depends()):
    try:
        result = await service.create_user(user_data.dict())
        if result.is_success:
            return result.data
        else:
            raise HTTPException(status_code=400, detail=result.result_msg)
    except Exception as e:
        status_code = get_http_status_code(e)
        user_message = create_user_friendly_message(e)
        raise HTTPException(status_code=status_code, detail=user_message)
```

## 最佳实践

### 1. 异常处理层次

1. **仓储层**: 使用 `@handle_repository_errors` 装饰器
2. **服务层**: 捕获具体异常类型，返回 Result 对象
3. **路由层**: 将异常转换为 HTTP 响应

### 2. 日志记录

```python
# 系统错误 - 详细记录
if not is_user_input_error(e):
    logger.error(f"数据库操作失败: {e}", exc_info=True, extra={
        "error_code": e.error_code if hasattr(e, 'error_code') else None,
        "context": e.context if hasattr(e, 'context') else None
    })

# 用户输入错误 - 简单记录
else:
    logger.info(f"用户输入错误: {create_user_friendly_message(e)}")
```

### 3. 错误重试

```python
import asyncio
from svc.core.database import is_retryable_error

async def with_retry(func, max_retries=3, delay=1):
    """带重试的数据库操作"""
    for attempt in range(max_retries):
        try:
            return await func()
        except Exception as e:
            if not is_retryable_error(e) or attempt == max_retries - 1:
                raise
            await asyncio.sleep(delay * (2 ** attempt))  # 指数退避
```

## 错误码映射

系统提供了错误码到 HTTP 状态码的自动映射：

- `DB_RECORD_NOT_FOUND` → 404
- `DB_UNIQUE_CONSTRAINT_ERROR` → 409
- `DB_FOREIGN_KEY_ERROR` → 400
- `DB_CONNECTION_ERROR` → 503
- `DB_TRANSACTION_TIMEOUT` → 408

这样可以确保 API 返回正确的 HTTP 状态码。
