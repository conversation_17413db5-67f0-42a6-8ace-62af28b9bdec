#!/usr/bin/env python3
"""
数据库异常处理使用示例

展示如何在实际项目中使用完善的数据库异常处理系统
"""

from typing import Optional
from sqlalchemy.ext.asyncio import AsyncSession
from fastapi import HTTPException

# 导入数据库异常处理功能
from svc.core.database import (
    # 异常类
    DatabaseError,
    UniqueConstraintError,
    RecordNotFoundError,
    ConnectionError,
    
    # 装饰器
    handle_database_errors,
    handle_repository_errors,
    
    # 工具函数
    create_user_friendly_message,
    get_http_status_code,
    is_retryable_error,
    is_user_input_error,
)

from svc.core.repositories.base import BaseRepository
from svc.core.services.base import BaseService
from svc.apps.auth.models.user import User


# ============================================================================
# 仓储层示例
# ============================================================================

class ExampleUserRepository(BaseRepository[User]):
    """示例用户仓储类，展示异常处理的使用"""
    
    def __init__(self, db: AsyncSession):
        super().__init__(model=User, db=db)
    
    @handle_repository_errors(model_name="User")
    async def create_user(self, user_data: dict) -> User:
        """
        创建用户 - 自动处理数据库异常
        
        装饰器会自动：
        1. 捕获 SQLAlchemy 异常并映射为自定义异常
        2. 记录详细的错误日志
        3. 添加模型和操作的上下文信息
        """
        return await self.create(user_data)
    
    @handle_database_errors(reraise_as=RecordNotFoundError, log_errors=False)
    async def get_user_by_id(self, user_id: int) -> User:
        """
        获取用户 - 将未找到异常重新抛出为 RecordNotFoundError
        
        装饰器会：
        1. 捕获所有数据库异常
        2. 重新抛出为指定的异常类型
        3. 不记录错误日志（因为这是预期的业务逻辑）
        """
        user = await self.get_by_id(user_id)
        if not user:
            raise RecordNotFoundError(model_name="User", record_id=user_id)
        return user
    
    async def update_user_with_manual_handling(self, user_id: int, update_data: dict) -> User:
        """
        手动异常处理示例 - 更细粒度的控制
        """
        try:
            user = await self.get_by_id(user_id)
            if not user:
                raise RecordNotFoundError(model_name="User", record_id=user_id)
            
            # 更新用户数据
            for key, value in update_data.items():
                setattr(user, key, value)
            
            await self.db.flush()
            return user
            
        except UniqueConstraintError as e:
            # 处理唯一约束违反
            if e.field_name == "email":
                raise UniqueConstraintError(
                    message="邮箱地址已被使用",
                    field_name="email",
                    field_value=e.field_value,
                    original_error=e.original_error
                )
            else:
                raise  # 重新抛出其他唯一约束错误
        
        except ConnectionError as e:
            # 连接错误可以重试
            if is_retryable_error(e):
                # 这里可以实现重试逻辑
                pass
            raise


# ============================================================================
# 服务层示例
# ============================================================================

class ExampleUserService(BaseService):
    """示例用户服务类，展示服务层的异常处理"""
    
    def __init__(self, repository: ExampleUserRepository):
        super().__init__()
        self.repository = repository
    
    async def create_user(self, user_data: dict):
        """
        创建用户 - 服务层异常处理
        """
        try:
            user = await self.repository.create_user(user_data)
            return self.create_success_result(user)
            
        except UniqueConstraintError as e:
            # 用户输入错误，返回友好的错误消息
            return self.create_error_result(
                error_code="USER_ALREADY_EXISTS",
                error_message=create_user_friendly_message(e)
            )
        
        except DatabaseError as e:
            # 系统错误，记录日志并返回通用错误消息
            if not is_user_input_error(e):
                self.logger.error(f"创建用户失败: {e}", exc_info=True)
            
            return self.create_error_result(
                error_code="CREATE_USER_FAILED",
                error_message=create_user_friendly_message(e)
            )
    
    async def get_user_by_id(self, user_id: int):
        """
        获取用户 - 处理未找到的情况
        """
        try:
            user = await self.repository.get_user_by_id(user_id)
            return self.create_success_result(user)
            
        except RecordNotFoundError as e:
            return self.create_error_result(
                error_code="USER_NOT_FOUND",
                error_message=create_user_friendly_message(e)
            )
        
        except DatabaseError as e:
            self.logger.error(f"获取用户失败: {e}", exc_info=True)
            return self.create_error_result(
                error_code="GET_USER_FAILED",
                error_message="获取用户信息失败，请稍后重试"
            )
    
    async def update_user_with_retry(self, user_id: int, update_data: dict, max_retries: int = 3):
        """
        更新用户 - 带重试机制的异常处理
        """
        import asyncio
        
        for attempt in range(max_retries):
            try:
                user = await self.repository.update_user_with_manual_handling(user_id, update_data)
                return self.create_success_result(user)
                
            except Exception as e:
                # 检查是否可以重试
                if is_retryable_error(e) and attempt < max_retries - 1:
                    # 指数退避重试
                    delay = 2 ** attempt
                    self.logger.warning(f"更新用户失败，{delay}秒后重试 (尝试 {attempt + 1}/{max_retries}): {e}")
                    await asyncio.sleep(delay)
                    continue
                
                # 不可重试的错误或达到最大重试次数
                if is_user_input_error(e):
                    return self.create_error_result(
                        error_code="INVALID_INPUT",
                        error_message=create_user_friendly_message(e)
                    )
                else:
                    self.logger.error(f"更新用户失败: {e}", exc_info=True)
                    return self.create_error_result(
                        error_code="UPDATE_USER_FAILED",
                        error_message="更新用户失败，请稍后重试"
                    )


# ============================================================================
# 路由层示例
# ============================================================================

async def create_user_endpoint(user_data: dict, service: ExampleUserService):
    """
    创建用户端点 - 路由层异常处理
    """
    try:
        result = await service.create_user(user_data)
        
        if result.is_success:
            return {"status": "success", "data": result.data}
        else:
            # 服务层返回的业务错误
            status_code = 400  # 默认为客户端错误
            if result.result_code == "USER_NOT_FOUND":
                status_code = 404
            elif result.result_code == "USER_ALREADY_EXISTS":
                status_code = 409
            
            raise HTTPException(
                status_code=status_code,
                detail=result.result_msg
            )
    
    except DatabaseError as e:
        # 直接的数据库异常（绕过服务层）
        status_code = get_http_status_code(e)
        user_message = create_user_friendly_message(e)
        
        # 记录系统错误
        if not is_user_input_error(e):
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"API异常: {e}", exc_info=True)
        
        raise HTTPException(status_code=status_code, detail=user_message)
    
    except Exception as e:
        # 其他未预期的异常
        import logging
        logger = logging.getLogger(__name__)
        logger.error(f"未处理的异常: {e}", exc_info=True)
        
        raise HTTPException(
            status_code=500,
            detail="服务器内部错误，请稍后重试"
        )


# ============================================================================
# 使用示例
# ============================================================================

async def example_usage():
    """完整的使用示例"""
    
    # 假设我们有数据库会话
    # db_session = get_db_session()
    
    # 创建仓储和服务
    # repository = ExampleUserRepository(db_session)
    # service = ExampleUserService(repository)
    
    # 创建用户
    user_data = {
        "email": "<EMAIL>",
        "username": "testuser",
        "fullname": "Test User"
    }
    
    # result = await service.create_user(user_data)
    # 
    # if result.is_success:
    #     print(f"用户创建成功: {result.data}")
    # else:
    #     print(f"用户创建失败: {result.result_msg}")
    
    print("示例代码展示了完整的异常处理流程")


if __name__ == "__main__":
    import asyncio
    asyncio.run(example_usage())
